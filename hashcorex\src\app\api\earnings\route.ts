import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { transactionDb } from '@/lib/database';
import { calculateEstimatedEarnings } from '@/lib/mining';

// GET - Fetch user's earnings data
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get all earnings transactions
    const earningsTransactions = await transactionDb.findByUserId(user.id, 100);
    
    // Filter by earnings types
    const miningEarnings = earningsTransactions.filter(t => 
      t.type === 'MINING_EARNINGS' || 
      t.type === 'DIRECT_REFERRAL' || 
      t.type === 'BINARY_BONUS'
    );

    // Calculate totals
    const totalEarnings = miningEarnings
      .filter(t => t.status === 'COMPLETED')
      .reduce((sum, t) => sum + t.amount, 0);

    const pendingEarnings = miningEarnings
      .filter(t => t.status === 'PENDING')
      .reduce((sum, t) => sum + t.amount, 0);

    const miningEarningsTotal = miningEarnings
      .filter(t => t.type === 'MINING_EARNINGS' && t.status === 'COMPLETED')
      .reduce((sum, t) => sum + t.amount, 0);

    const referralEarningsTotal = miningEarnings
      .filter(t => (t.type === 'DIRECT_REFERRAL' || t.type === 'BINARY_BONUS') && t.status === 'COMPLETED')
      .reduce((sum, t) => sum + t.amount, 0);

    // Get estimated future earnings
    const estimatedEarnings = await calculateEstimatedEarnings(user.id);

    // Get recent earnings (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentEarnings = miningEarnings.filter(t => 
      new Date(t.createdAt) >= thirtyDaysAgo && t.status === 'COMPLETED'
    );

    return NextResponse.json({
      success: true,
      data: {
        totalEarnings,
        pendingEarnings,
        miningEarnings: miningEarningsTotal,
        referralEarnings: referralEarningsTotal,
        estimatedEarnings,
        recentEarnings: recentEarnings.map(t => ({
          id: t.id,
          type: t.type,
          amount: t.amount,
          description: t.description,
          createdAt: t.createdAt,
        })),
        earningsBreakdown: {
          mining: miningEarningsTotal,
          directReferral: miningEarnings
            .filter(t => t.type === 'DIRECT_REFERRAL' && t.status === 'COMPLETED')
            .reduce((sum, t) => sum + t.amount, 0),
          binaryBonus: miningEarnings
            .filter(t => t.type === 'BINARY_BONUS' && t.status === 'COMPLETED')
            .reduce((sum, t) => sum + t.amount, 0),
        },
      },
    });

  } catch (error: any) {
    console.error('Earnings fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch earnings data' },
      { status: 500 }
    );
  }
}
