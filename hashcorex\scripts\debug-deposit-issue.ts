import { prisma } from '../src/lib/prisma';
import { depositTransactionDb, walletBalanceDb, transactionDb } from '../src/lib/database';

async function debugDepositIssue() {
  console.log('🔍 Debugging Deposit Issue...\n');

  try {
    // Check recent deposits
    console.log('1. Checking recent deposits...');
    const recentDeposits = await prisma.depositTransaction.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    console.log(`Found ${recentDeposits.length} recent deposits:`);
    recentDeposits.forEach((deposit, index) => {
      console.log(`  ${index + 1}. TX: ${deposit.transactionId.slice(0, 16)}...`);
      console.log(`     User: ${deposit.user?.email || 'Unknown'}`);
      console.log(`     Amount: ${deposit.amount} USDT`);
      console.log(`     Status: ${deposit.status}`);
      console.log(`     Created: ${deposit.createdAt}`);
      console.log(`     Processed: ${deposit.processedAt || 'Not processed'}`);
      console.log('');
    });

    // Check the specific transaction from the logs
    const specificTx = '045d1afeb0d002b48db55908de27d2715d8f34858692669885e336ce29804afa';
    console.log(`2. Checking specific transaction: ${specificTx}...`);
    
    const specificDeposit = await prisma.depositTransaction.findUnique({
      where: { transactionId: specificTx },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (specificDeposit) {
      console.log('✅ Found specific deposit:');
      console.log(`   User: ${specificDeposit.user?.email || 'Unknown'} (ID: ${specificDeposit.userId})`);
      console.log(`   Amount: ${specificDeposit.amount} USDT`);
      console.log(`   Status: ${specificDeposit.status}`);
      console.log(`   Confirmations: ${specificDeposit.confirmations}`);
      console.log(`   Created: ${specificDeposit.createdAt}`);
      console.log(`   Verified: ${specificDeposit.verifiedAt || 'Not verified'}`);
      console.log(`   Processed: ${specificDeposit.processedAt || 'Not processed'}`);

      // Check user's wallet balance
      console.log('\n3. Checking user wallet balance...');
      const walletBalance = await prisma.walletBalance.findUnique({
        where: { userId: specificDeposit.userId },
      });

      if (walletBalance) {
        console.log('✅ Found wallet balance:');
        console.log(`   Available Balance: ${walletBalance.availableBalance} USDT`);
        console.log(`   Total Deposits: ${walletBalance.totalDeposits} USDT`);
        console.log(`   Last Updated: ${walletBalance.lastUpdated}`);
      } else {
        console.log('❌ No wallet balance found for user');
      }

      // Check user's transactions
      console.log('\n4. Checking user transactions...');
      const userTransactions = await prisma.transaction.findMany({
        where: { userId: specificDeposit.userId },
        orderBy: { createdAt: 'desc' },
        take: 10,
      });

      console.log(`Found ${userTransactions.length} transactions:`);
      userTransactions.forEach((tx, index) => {
        console.log(`  ${index + 1}. ${tx.type}: ${tx.amount} USDT - ${tx.status}`);
        console.log(`     Description: ${tx.description}`);
        console.log(`     Created: ${tx.createdAt}`);
        console.log('');
      });

      // Check if there's a deposit transaction for this specific TX
      const depositTransaction = userTransactions.find(tx => 
        tx.description.includes(specificTx) || tx.description.includes('Deposit')
      );

      if (depositTransaction) {
        console.log('✅ Found matching deposit transaction in wallet');
      } else {
        console.log('❌ No matching deposit transaction found in wallet');
        console.log('   This indicates the wallet crediting failed');
      }

    } else {
      console.log('❌ Specific deposit not found in database');
    }

    // Check system logs for this transaction
    console.log('\n5. Checking system logs...');
    const systemLogs = await prisma.systemLog.findMany({
      where: {
        OR: [
          { details: { contains: specificTx } },
          { action: { contains: 'DEPOSIT' } },
        ],
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    });

    console.log(`Found ${systemLogs.length} related system logs:`);
    systemLogs.forEach((log, index) => {
      console.log(`  ${index + 1}. ${log.action} - ${log.createdAt}`);
      console.log(`     Details: ${log.details || 'No details'}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugDepositIssue()
  .then(() => {
    console.log('\n🎉 Debug completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Debug failed:', error);
    process.exit(1);
  });
