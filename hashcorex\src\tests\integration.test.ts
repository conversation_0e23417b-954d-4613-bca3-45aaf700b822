import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { registerUser } from '../lib/auth';
import { getBinaryTreeStructure, getDetailedTeamStats } from '../lib/referral';
import { prisma } from '../lib/prisma';

describe('Integration Tests - Registration and Tree Building', () => {
  beforeEach(async () => {
    // Clean up test data
    await prisma.referral.deleteMany({});
    await prisma.binaryPoints.deleteMany({});
    await prisma.user.deleteMany({});
  });

  afterEach(async () => {
    // Clean up after tests
    await prisma.referral.deleteMany({});
    await prisma.binaryPoints.deleteMany({});
    await prisma.user.deleteMany({});
  });

  describe('Complete Registration Flow', () => {
    it('should register root user and build tree structure', async () => {
      // Register root user
      const rootUser = await registerUser({
        email: '<EMAIL>',
        firstName: 'Root',
        lastName: 'User',
        password: 'Password123!',
      });

      expect(rootUser.id).toBeTruthy();
      expect(rootUser.referralId).toBeTruthy();

      // Register first referral (should go to left)
      const user1 = await registerUser({
        email: '<EMAIL>',
        firstName: 'User',
        lastName: 'One',
        password: 'Password123!',
        referralCode: rootUser.referralId,
      });

      expect(user1.id).toBeTruthy();

      // Register second referral (should go to right)
      const user2 = await registerUser({
        email: '<EMAIL>',
        firstName: 'User',
        lastName: 'Two',
        password: 'Password123!',
        referralCode: rootUser.referralId,
      });

      expect(user2.id).toBeTruthy();

      // Verify tree structure
      const treeStructure = await getBinaryTreeStructure(rootUser.id, 3);
      expect(treeStructure).toBeTruthy();
      expect(treeStructure.leftChild || treeStructure.rightChild).toBeTruthy();

      // Verify team stats
      const teamStats = await getDetailedTeamStats(rootUser.id);
      expect(teamStats.totalTeam).toBe(2);
      expect(teamStats.directReferrals).toBe(2);
    });

    it('should handle specific side placement correctly', async () => {
      // Register root user
      const rootUser = await registerUser({
        email: '<EMAIL>',
        firstName: 'Root',
        lastName: 'User',
        password: 'Password123!',
      });

      // Register user with specific left side placement
      const leftUser = await registerUser({
        email: '<EMAIL>',
        firstName: 'Left',
        lastName: 'User',
        password: 'Password123!',
        referralCode: rootUser.referralId,
        placementSide: 'left',
      });

      // Register user with specific right side placement
      const rightUser = await registerUser({
        email: '<EMAIL>',
        firstName: 'Right',
        lastName: 'User',
        password: 'Password123!',
        referralCode: rootUser.referralId,
        placementSide: 'right',
      });

      // Verify placements
      const leftReferral = await prisma.referral.findFirst({
        where: { referrerId: rootUser.id, referredId: leftUser.id },
      });
      const rightReferral = await prisma.referral.findFirst({
        where: { referrerId: rootUser.id, referredId: rightUser.id },
      });

      expect(leftReferral?.placementSide).toBe('LEFT');
      expect(rightReferral?.placementSide).toBe('RIGHT');
    });

    it('should maintain sponsor relationships separate from binary placement', async () => {
      // Register root user
      const rootUser = await registerUser({
        email: '<EMAIL>',
        firstName: 'Root',
        lastName: 'User',
        password: 'Password123!',
      });

      // Register multiple users to fill direct spots
      const user1 = await registerUser({
        email: '<EMAIL>',
        firstName: 'User',
        lastName: 'One',
        password: 'Password123!',
        referralCode: rootUser.referralId,
      });

      const user2 = await registerUser({
        email: '<EMAIL>',
        firstName: 'User',
        lastName: 'Two',
        password: 'Password123!',
        referralCode: rootUser.referralId,
      });

      // Register third user (should be placed deeper in tree but still sponsored by root)
      const user3 = await registerUser({
        email: '<EMAIL>',
        firstName: 'User',
        lastName: 'Three',
        password: 'Password123!',
        referralCode: rootUser.referralId,
      });

      // Verify sponsor relationship
      const user3Data = await prisma.user.findUnique({
        where: { id: user3.id },
        select: { referrerId: true },
      });

      expect(user3Data?.referrerId).toBe(rootUser.id);

      // Verify binary placement (might be under user1 or user2)
      const user3Referral = await prisma.referral.findFirst({
        where: { referredId: user3.id },
      });

      expect(user3Referral).toBeTruthy();
      // The referrerId might be different from the sponsor (rootUser.id)
    });

    it('should handle deep tree structures correctly', async () => {
      // Register root user
      const rootUser = await registerUser({
        email: '<EMAIL>',
        firstName: 'Root',
        lastName: 'User',
        password: 'Password123!',
      });

      // Register multiple generations
      const users = [];
      for (let i = 1; i <= 7; i++) {
        const user = await registerUser({
          email: `user${i}@test.com`,
          firstName: `User`,
          lastName: `${i}`,
          password: 'Password123!',
          referralCode: rootUser.referralId,
        });
        users.push(user);
      }

      // Verify tree structure
      const treeStructure = await getBinaryTreeStructure(rootUser.id, 5);
      expect(treeStructure).toBeTruthy();

      // Verify team stats
      const teamStats = await getDetailedTeamStats(rootUser.id);
      expect(teamStats.totalTeam).toBe(7);
      expect(teamStats.directReferrals).toBe(7);
      expect(teamStats.leftTeam + teamStats.rightTeam).toBe(7);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle invalid referral codes gracefully', async () => {
      await expect(
        registerUser({
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'Test',
          password: 'Password123!',
          referralCode: 'INVALID_CODE',
        })
      ).rejects.toThrow('Invalid referral code');
    });

    it('should handle duplicate email registration', async () => {
      await registerUser({
        email: '<EMAIL>',
        firstName: 'First',
        lastName: 'User',
        password: 'Password123!',
      });

      await expect(
        registerUser({
          email: '<EMAIL>',
          firstName: 'Second',
          lastName: 'User',
          password: 'Password123!',
        })
      ).rejects.toThrow('User already exists with this email');
    });

    it('should handle self-referral attempts', async () => {
      const user = await registerUser({
        email: '<EMAIL>',
        firstName: 'User',
        lastName: 'Test',
        password: 'Password123!',
      });

      await expect(
        registerUser({
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'Two',
          password: 'Password123!',
          referralCode: user.referralId,
        })
      ).resolves.toBeTruthy(); // Should work normally

      // But if we try to refer themselves (which shouldn't be possible in UI)
      // The system should handle it gracefully
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle large tree structures efficiently', async () => {
      const startTime = Date.now();

      // Register root user
      const rootUser = await registerUser({
        email: '<EMAIL>',
        firstName: 'Root',
        lastName: 'User',
        password: 'Password123!',
      });

      // Register 50 users to test performance
      const registrationPromises = [];
      for (let i = 1; i <= 50; i++) {
        registrationPromises.push(
          registerUser({
            email: `user${i}@test.com`,
            firstName: `User`,
            lastName: `${i}`,
            password: 'Password123!',
            referralCode: rootUser.referralId,
          })
        );
      }

      await Promise.all(registrationPromises);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(30000); // 30 seconds

      // Verify final tree structure
      const teamStats = await getDetailedTeamStats(rootUser.id);
      expect(teamStats.totalTeam).toBe(50);
      expect(teamStats.directReferrals).toBe(50);
    }, 60000); // 60 second timeout for this test
  });
});
