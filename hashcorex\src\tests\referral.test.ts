import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import {
  placeUserInBinaryTree,
  placeUserInSpecificSide,
  placeUserByReferralType,
  placeUserInLeftSideOnly,
  placeUserInRightSideOnly,
  calculateDownlineCount,
  getDetailedTeamStats,
  getTreeHealthStats,
  searchUsersInTree,
  getBinaryTreeStructure
} from '../lib/referral';
import { validateReferralCode, validatePlacementRequest } from '../lib/referralValidation';
import { prisma } from '../lib/prisma';

// Mock data for testing
const mockUsers = [
  {
    id: 'user1',
    email: '<EMAIL>',
    firstName: 'User',
    lastName: 'One',
    referralId: 'REF001',
    isActive: true,
  },
  {
    id: 'user2',
    email: '<EMAIL>',
    firstName: 'User',
    lastName: 'Two',
    referralId: 'REF002',
    isActive: true,
  },
  {
    id: 'user3',
    email: '<EMAIL>',
    firstName: 'User',
    lastName: 'Three',
    referralId: 'REF003',
    isActive: true,
  },
];

describe('Binary Tree Placement Algorithm', () => {
  beforeEach(async () => {
    // Clean up test data
    await prisma.referral.deleteMany({});
    await prisma.binaryPoints.deleteMany({});
    await prisma.user.deleteMany({});
    
    // Create test users
    for (const user of mockUsers) {
      await prisma.user.create({
        data: {
          ...user,
          password: 'hashedpassword',
        },
      });
    }
  });

  afterEach(async () => {
    // Clean up after tests
    await prisma.referral.deleteMany({});
    await prisma.binaryPoints.deleteMany({});
    await prisma.user.deleteMany({});
  });

  describe('placeUserInBinaryTree', () => {
    it('should place first user in left side', async () => {
      const placementSide = await placeUserInBinaryTree('user1', 'user2');
      expect(placementSide).toBe('LEFT');

      const referral = await prisma.referral.findFirst({
        where: { referrerId: 'user1', referredId: 'user2' },
      });
      expect(referral?.placementSide).toBe('LEFT');
    });

    it('should place second user in right side for balance', async () => {
      // Place first user
      await placeUserInBinaryTree('user1', 'user2');
      
      // Place second user
      const placementSide = await placeUserInBinaryTree('user1', 'user3');
      expect(placementSide).toBe('RIGHT');

      const referral = await prisma.referral.findFirst({
        where: { referrerId: 'user1', referredId: 'user3' },
      });
      expect(referral?.placementSide).toBe('RIGHT');
    });

    it('should use weaker leg logic for optimal placement', async () => {
      // Create a tree structure where left side has more users
      await placeUserInBinaryTree('user1', 'user2'); // Left
      await placeUserInBinaryTree('user1', 'user3'); // Right
      
      // Add more users to left side to make it heavier
      const user4 = await prisma.user.create({
        data: {
          id: 'user4',
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'Four',
          referralId: 'REF004',
          password: 'hashedpassword',
          isActive: true,
        },
      });
      
      await placeUserInBinaryTree('user2', 'user4'); // Under left side
      
      // Now place a new user - should go to right side (weaker leg)
      const user5 = await prisma.user.create({
        data: {
          id: 'user5',
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'Five',
          referralId: 'REF005',
          password: 'hashedpassword',
          isActive: true,
        },
      });
      
      const placementSide = await placeUserInBinaryTree('user1', 'user5');
      
      // Should place in right side or under right side (weaker leg)
      const referral = await prisma.referral.findFirst({
        where: { referredId: 'user5' },
      });
      
      expect(referral).toBeTruthy();
      // The exact placement depends on the algorithm, but it should balance the tree
    });
  });

  describe('placeUserInSpecificSide', () => {
    it('should place user in requested side when available', async () => {
      const placementSide = await placeUserInSpecificSide('user1', 'user2', 'RIGHT');
      expect(placementSide).toBe('RIGHT');

      const referral = await prisma.referral.findFirst({
        where: { referrerId: 'user1', referredId: 'user2' },
      });
      expect(referral?.placementSide).toBe('RIGHT');
    });

    it('should find next available spot in requested side when direct spot is occupied', async () => {
      // Fill direct right spot
      await placeUserInSpecificSide('user1', 'user2', 'RIGHT');
      
      // Try to place another user in right side
      const placementSide = await placeUserInSpecificSide('user1', 'user3', 'RIGHT');
      
      // Should still place in right side (under user2)
      const referral = await prisma.referral.findFirst({
        where: { referredId: 'user3' },
      });
      
      expect(referral).toBeTruthy();
      expect(placementSide).toBe('RIGHT');
    });
  });

  describe('calculateDownlineCount', () => {
    it('should return 0 for user with no downline', async () => {
      const leftCount = await calculateDownlineCount('user1', 'LEFT');
      const rightCount = await calculateDownlineCount('user1', 'RIGHT');

      expect(leftCount).toBe(0);
      expect(rightCount).toBe(0);
    });

    it('should correctly count downline users', async () => {
      // Create a tree structure
      await placeUserInBinaryTree('user1', 'user2'); // Left
      await placeUserInBinaryTree('user1', 'user3'); // Right

      const leftCount = await calculateDownlineCount('user1', 'LEFT');
      const rightCount = await calculateDownlineCount('user1', 'RIGHT');

      expect(leftCount).toBe(1);
      expect(rightCount).toBe(1);
    });
  });

  describe('NEW: Three-Type Referral Placement System', () => {
    beforeEach(async () => {
      // Create additional test users for comprehensive testing
      const additionalUsers = [
        { id: 'user4', email: '<EMAIL>', firstName: 'User', lastName: 'Four', referralId: 'REF004' },
        { id: 'user5', email: '<EMAIL>', firstName: 'User', lastName: 'Five', referralId: 'REF005' },
        { id: 'user6', email: '<EMAIL>', firstName: 'User', lastName: 'Six', referralId: 'REF006' },
        { id: 'user7', email: '<EMAIL>', firstName: 'User', lastName: 'Seven', referralId: 'REF007' },
      ];

      for (const user of additionalUsers) {
        await prisma.user.create({
          data: {
            ...user,
            password: 'hashedpassword',
            isActive: true,
          },
        });
      }
    });

    describe('placeUserByReferralType', () => {
      it('should use general placement (weaker leg) for "general" type', async () => {
        const placementSide = await placeUserByReferralType('user1', 'user2', 'general');
        expect(['LEFT', 'RIGHT']).toContain(placementSide);

        const referral = await prisma.referral.findFirst({
          where: { referrerId: 'user1', referredId: 'user2' },
        });
        expect(referral?.placementSide).toBe(placementSide);
      });

      it('should use left-side-only placement for "left" type', async () => {
        const placementSide = await placeUserByReferralType('user1', 'user2', 'left');
        expect(placementSide).toBe('LEFT');

        const referral = await prisma.referral.findFirst({
          where: { referredId: 'user2' },
        });
        expect(referral?.placementSide).toBe('LEFT');
      });

      it('should use right-side-only placement for "right" type', async () => {
        const placementSide = await placeUserByReferralType('user1', 'user2', 'right');
        expect(placementSide).toBe('RIGHT');

        const referral = await prisma.referral.findFirst({
          where: { referredId: 'user2' },
        });
        expect(referral?.placementSide).toBe('RIGHT');
      });
    });

    describe('placeUserInLeftSideOnly', () => {
      it('should place user directly in left side when available', async () => {
        const placementSide = await placeUserInLeftSideOnly('user1', 'user2');
        expect(placementSide).toBe('LEFT');

        const referral = await prisma.referral.findFirst({
          where: { referrerId: 'user1', referredId: 'user2' },
        });
        expect(referral?.placementSide).toBe('LEFT');
      });

      it('should find deepest left position when direct left is occupied', async () => {
        // Fill direct left spot
        await placeUserInLeftSideOnly('user1', 'user2');

        // Place another user - should go deeper in left side
        const placementSide = await placeUserInLeftSideOnly('user1', 'user3');
        expect(placementSide).toBe('LEFT');

        const referral = await prisma.referral.findFirst({
          where: { referredId: 'user3' },
        });
        expect(referral?.placementSide).toBe('LEFT');
        // Should be placed under user2 (not directly under user1)
        expect(referral?.referrerId).toBe('user2');
      });

      it('should never place in right side even if left is heavily loaded', async () => {
        // Create a heavily loaded left side
        await placeUserInLeftSideOnly('user1', 'user2'); // user1 -> user2 (LEFT)
        await placeUserInLeftSideOnly('user1', 'user3'); // user2 -> user3 (LEFT)
        await placeUserInLeftSideOnly('user1', 'user4'); // user3 -> user4 (LEFT)

        // Place another user - should still go to left side
        const placementSide = await placeUserInLeftSideOnly('user1', 'user5');
        expect(placementSide).toBe('LEFT');

        const referral = await prisma.referral.findFirst({
          where: { referredId: 'user5' },
        });
        expect(referral?.placementSide).toBe('LEFT');
      });
    });

    describe('placeUserInRightSideOnly', () => {
      it('should place user directly in right side when available', async () => {
        const placementSide = await placeUserInRightSideOnly('user1', 'user2');
        expect(placementSide).toBe('RIGHT');

        const referral = await prisma.referral.findFirst({
          where: { referrerId: 'user1', referredId: 'user2' },
        });
        expect(referral?.placementSide).toBe('RIGHT');
      });

      it('should find deepest right position when direct right is occupied', async () => {
        // Fill direct right spot
        await placeUserInRightSideOnly('user1', 'user2');

        // Place another user - should go deeper in right side
        const placementSide = await placeUserInRightSideOnly('user1', 'user3');
        expect(placementSide).toBe('RIGHT');

        const referral = await prisma.referral.findFirst({
          where: { referredId: 'user3' },
        });
        expect(referral?.placementSide).toBe('RIGHT');
        // Should be placed under user2 (not directly under user1)
        expect(referral?.referrerId).toBe('user2');
      });

      it('should never place in left side even if right is heavily loaded', async () => {
        // Create a heavily loaded right side
        await placeUserInRightSideOnly('user1', 'user2'); // user1 -> user2 (RIGHT)
        await placeUserInRightSideOnly('user1', 'user3'); // user2 -> user3 (RIGHT)
        await placeUserInRightSideOnly('user1', 'user4'); // user3 -> user4 (RIGHT)

        // Place another user - should still go to right side
        const placementSide = await placeUserInRightSideOnly('user1', 'user5');
        expect(placementSide).toBe('RIGHT');

        const referral = await prisma.referral.findFirst({
          where: { referredId: 'user5' },
        });
        expect(referral?.placementSide).toBe('RIGHT');
      });
    });

    describe('Comprehensive Placement Comparison', () => {
      it('should demonstrate different behaviors of all three placement types', async () => {
        // Create a scenario where all three types behave differently
        // Initial tree: user1 has user2 on LEFT, user3 on RIGHT
        await placeUserInBinaryTree('user1', 'user2'); // Should go LEFT (first placement)
        await placeUserInBinaryTree('user1', 'user3'); // Should go RIGHT (balance)

        // Add one more user under user2 to make left side heavier
        await placeUserInBinaryTree('user2', 'user4'); // Should go LEFT under user2

        // Now test all three placement types with user5, user6, user7

        // 1. General placement (weaker leg) - should go to right side
        const generalPlacement = await placeUserByReferralType('user1', 'user5', 'general');
        const generalReferral = await prisma.referral.findFirst({
          where: { referredId: 'user5' },
        });

        // 2. Left-side-only placement - should go to left side (deepest available)
        const leftPlacement = await placeUserByReferralType('user1', 'user6', 'left');
        const leftReferral = await prisma.referral.findFirst({
          where: { referredId: 'user6' },
        });

        // 3. Right-side-only placement - should go to right side (deepest available)
        const rightPlacement = await placeUserByReferralType('user1', 'user7', 'right');
        const rightReferral = await prisma.referral.findFirst({
          where: { referredId: 'user7' },
        });

        // Verify different behaviors
        expect(generalPlacement).toBe('RIGHT'); // Weaker leg logic
        expect(leftPlacement).toBe('LEFT');     // Strict left placement
        expect(rightPlacement).toBe('RIGHT');   // Strict right placement

        expect(generalReferral?.placementSide).toBe('RIGHT');
        expect(leftReferral?.placementSide).toBe('LEFT');
        expect(rightReferral?.placementSide).toBe('RIGHT');

        // Verify that left and right placements went to different parents
        // (demonstrating depth-first placement within their respective sides)
        expect(leftReferral?.referrerId).not.toBe(rightReferral?.referrerId);
      });
    });
  });
});

describe('Tree Statistics and Health', () => {
  beforeEach(async () => {
    await prisma.referral.deleteMany({});
    await prisma.binaryPoints.deleteMany({});
    await prisma.user.deleteMany({});
    
    for (const user of mockUsers) {
      await prisma.user.create({
        data: {
          ...user,
          password: 'hashedpassword',
        },
      });
    }
  });

  describe('getDetailedTeamStats', () => {
    it('should return correct team statistics', async () => {
      // Create some referrals
      await placeUserInBinaryTree('user1', 'user2');
      await placeUserInBinaryTree('user1', 'user3');
      
      const stats = await getDetailedTeamStats('user1');
      
      expect(stats.totalTeam).toBeGreaterThanOrEqual(2);
      expect(stats.leftTeam).toBeGreaterThanOrEqual(0);
      expect(stats.rightTeam).toBeGreaterThanOrEqual(0);
      expect(stats.directReferrals).toBeGreaterThanOrEqual(0);
    });
  });

  describe('getTreeHealthStats', () => {
    it('should calculate tree balance ratio correctly', async () => {
      // Create balanced tree
      await placeUserInBinaryTree('user1', 'user2'); // Left
      await placeUserInBinaryTree('user1', 'user3'); // Right
      
      const health = await getTreeHealthStats('user1');
      
      expect(health.balanceRatio).toBeGreaterThan(0);
      expect(health.balanceRatio).toBeLessThanOrEqual(1);
      expect(health.totalUsers).toBeGreaterThanOrEqual(2);
    });
  });
});

describe('Referral Validation', () => {
  beforeEach(async () => {
    await prisma.user.deleteMany({});
    
    await prisma.user.create({
      data: {
        id: 'user1',
        email: '<EMAIL>',
        firstName: 'User',
        lastName: 'One',
        referralId: 'VALID001',
        password: 'hashedpassword',
        isActive: true,
      },
    });

    await prisma.user.create({
      data: {
        id: 'user2',
        email: '<EMAIL>',
        firstName: 'User',
        lastName: 'Two',
        referralId: 'INACTIVE001',
        password: 'hashedpassword',
        isActive: false,
      },
    });
  });

  describe('validateReferralCode', () => {
    it('should validate correct referral code', async () => {
      const result = await validateReferralCode('VALID001');
      
      expect(result.isValid).toBe(true);
      expect(result.referrer).toBeTruthy();
      expect(result.referrer?.id).toBe('user1');
    });

    it('should reject invalid referral code', async () => {
      const result = await validateReferralCode('INVALID001');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Invalid referral code');
    });

    it('should reject inactive referrer', async () => {
      const result = await validateReferralCode('INACTIVE001');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Referrer account is inactive');
    });

    it('should reject empty referral code', async () => {
      const result = await validateReferralCode('');
      
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Referral code is required');
    });
  });

  describe('validatePlacementRequest', () => {
    it('should validate placement request for active referrer', async () => {
      const result = await validatePlacementRequest('user1');
      
      expect(result.isValid).toBe(true);
      expect(result.canPlace).toBe(true);
    });

    it('should reject placement request for inactive referrer', async () => {
      const result = await validatePlacementRequest('user2');
      
      expect(result.isValid).toBe(false);
      expect(result.canPlace).toBe(false);
      expect(result.error).toBe('Referrer account is inactive');
    });

    it('should reject placement request for non-existent referrer', async () => {
      const result = await validatePlacementRequest('nonexistent');
      
      expect(result.isValid).toBe(false);
      expect(result.canPlace).toBe(false);
      expect(result.error).toBe('Referrer not found');
    });
  });
});

describe('Tree Search Functionality', () => {
  beforeEach(async () => {
    await prisma.referral.deleteMany({});
    await prisma.user.deleteMany({});
    
    // Create test users with searchable data
    await prisma.user.create({
      data: {
        id: 'user1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        referralId: 'REF001',
        password: 'hashedpassword',
        isActive: true,
      },
    });

    await prisma.user.create({
      data: {
        id: 'user2',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        referralId: 'REF002',
        password: 'hashedpassword',
        isActive: true,
      },
    });

    // Create tree structure
    await placeUserInBinaryTree('user1', 'user2');
  });

  describe('searchUsersInTree', () => {
    it('should find users by first name', async () => {
      const results = await searchUsersInTree('user1', 'Jane');
      
      expect(results.length).toBeGreaterThan(0);
      expect(results[0].firstName).toBe('Jane');
    });

    it('should find users by email', async () => {
      const results = await searchUsersInTree('user1', 'jane.smith');
      
      expect(results.length).toBeGreaterThan(0);
      expect(results[0].email).toBe('<EMAIL>');
    });

    it('should return empty array for non-matching search', async () => {
      const results = await searchUsersInTree('user1', 'nonexistent');
      
      expect(results.length).toBe(0);
    });
  });
});
