import { NextRequest, NextResponse } from 'next/server';
import { adminSettingsDb } from '@/lib/database';

// GET - Test binary settings retrieval
export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing binary settings retrieval...');

    // Get the raw database value
    const rawValue = await adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE');
    console.log(`Raw database value: "${rawValue}" (type: ${typeof rawValue})`);

    // Parse it like the system does
    const parsedValue = parseFloat(rawValue || '10');
    console.log(`Parsed value: ${parsedValue} (type: ${typeof parsedValue})`);

    // Get all settings like the admin API does
    const allSettings = await adminSettingsDb.getAll();
    const settingsObject: any = {};
    
    allSettings.forEach(setting => {
      try {
        settingsObject[setting.key] = JSON.parse(setting.value);
      } catch {
        settingsObject[setting.key] = setting.value;
      }
    });

    // Map database keys to frontend keys
    if (settingsObject['MAX_BINARY_POINTS_PER_SIDE']) {
      settingsObject.maxBinaryPointsPerSide = parseFloat(settingsObject['MAX_BINARY_POINTS_PER_SIDE']);
    }

    console.log('Settings object:', {
      raw: settingsObject['MAX_BINARY_POINTS_PER_SIDE'],
      mapped: settingsObject.maxBinaryPointsPerSide
    });

    return NextResponse.json({
      success: true,
      data: {
        rawDatabaseValue: rawValue,
        parsedValue: parsedValue,
        settingsObjectRaw: settingsObject['MAX_BINARY_POINTS_PER_SIDE'],
        settingsObjectMapped: settingsObject.maxBinaryPointsPerSide,
        allBinarySettings: {
          'MAX_BINARY_POINTS_PER_SIDE': settingsObject['MAX_BINARY_POINTS_PER_SIDE'],
          'BINARY_POINT_VALUE': settingsObject['BINARY_POINT_VALUE'],
          'BINARY_MATCHING_ENABLED': settingsObject['BINARY_MATCHING_ENABLED'],
          'BINARY_MATCHING_SCHEDULE': settingsObject['BINARY_MATCHING_SCHEDULE'],
        }
      }
    });

  } catch (error) {
    console.error('Test error:', error);
    return NextResponse.json(
      { success: false, error: 'Test failed' },
      { status: 500 }
    );
  }
}

// POST - Test binary settings update
export async function POST(request: NextRequest) {
  try {
    const { testValue } = await request.json();
    console.log(`🧪 Testing binary settings update with value: ${testValue}`);

    // Update the setting
    await adminSettingsDb.set('MAX_BINARY_POINTS_PER_SIDE', String(testValue));

    // Verify the update
    const updatedValue = await adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE');
    console.log(`Updated value: "${updatedValue}"`);

    return NextResponse.json({
      success: true,
      data: {
        originalValue: testValue,
        storedValue: updatedValue,
        parsedValue: parseFloat(updatedValue || '10')
      }
    });

  } catch (error) {
    console.error('Test update error:', error);
    return NextResponse.json(
      { success: false, error: 'Test update failed' },
      { status: 500 }
    );
  }
}
