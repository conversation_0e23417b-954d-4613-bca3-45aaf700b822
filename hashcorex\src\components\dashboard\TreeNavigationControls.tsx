'use client';

import React from 'react';
import { Button } from '@/components/ui';
import { 
  ZoomIn, ZoomOut, Maximize2, RotateCcw, Download, 
  Filter, Settings, Eye, EyeOff 
} from 'lucide-react';

interface TreeNavigationControlsProps {
  zoom: number;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  onResetView: () => void;
  onExportTree: () => void;
  showInactive: boolean;
  onToggleInactive: () => void;
  maxDepth: number;
  onDepthChange: (depth: number) => void;
  onRefresh: () => void;
}

export const TreeNavigationControls: React.FC<TreeNavigationControlsProps> = ({
  zoom,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onResetView,
  onExportTree,
  showInactive,
  onToggleInactive,
  maxDepth,
  onDepthChange,
  onRefresh,
}) => {
  return (
    <div className="flex flex-wrap items-center gap-2 p-4 bg-gray-50 rounded-lg border">
      {/* Zoom Controls */}
      <div className="flex items-center space-x-1 border-r border-gray-300 pr-3">
        <Button
          size="sm"
          variant="outline"
          onClick={onZoomOut}
          disabled={zoom <= 0.5}
          title="Zoom Out"
        >
          <ZoomOut className="h-4 w-4" />
        </Button>
        <span className="text-sm text-gray-600 min-w-[60px] text-center">
          {Math.round(zoom * 100)}%
        </span>
        <Button
          size="sm"
          variant="outline"
          onClick={onZoomIn}
          disabled={zoom >= 3}
          title="Zoom In"
        >
          <ZoomIn className="h-4 w-4" />
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={onResetZoom}
          title="Reset Zoom"
        >
          <Maximize2 className="h-4 w-4" />
        </Button>
      </div>

      {/* View Controls */}
      <div className="flex items-center space-x-1 border-r border-gray-300 pr-3">
        <Button
          size="sm"
          variant="outline"
          onClick={onResetView}
          title="Reset View"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={onRefresh}
          title="Refresh Tree"
        >
          <Settings className="h-4 w-4" />
        </Button>
      </div>

      {/* Display Options */}
      <div className="flex items-center space-x-2 border-r border-gray-300 pr-3">
        <label className="text-sm font-medium text-gray-700">Depth:</label>
        <select
          value={maxDepth}
          onChange={(e) => onDepthChange(parseInt(e.target.value))}
          className="px-2 py-1 border border-gray-300 rounded text-sm"
        >
          <option value={3}>3 Levels</option>
          <option value={5}>5 Levels</option>
          <option value={7}>7 Levels</option>
          <option value={10}>10 Levels</option>
          <option value={15}>15 Levels</option>
          <option value={20}>20 Levels (Unlimited)</option>
        </select>
      </div>

      {/* Filter Controls */}
      <div className="flex items-center space-x-2 border-r border-gray-300 pr-3">
        <Button
          size="sm"
          variant={showInactive ? "default" : "outline"}
          onClick={onToggleInactive}
          title={showInactive ? "Hide Inactive Users" : "Show Inactive Users"}
        >
          {showInactive ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
          <span className="ml-1 text-xs">Inactive</span>
        </Button>
      </div>

      {/* Export Controls */}
      <div className="flex items-center space-x-1">
        <Button
          size="sm"
          variant="outline"
          onClick={onExportTree}
          title="Export Tree"
        >
          <Download className="h-4 w-4" />
          <span className="ml-1 text-xs">Export</span>
        </Button>
      </div>
    </div>
  );
};

// Tree Statistics Panel Component
interface TreeStatsPanelProps {
  stats: {
    totalUsers: number;
    balanceRatio: number;
    averageDepth: number;
    maxDepth: number;
    emptyPositions: number;
  };
  teamStats: {
    directReferrals: number;
    leftTeam: number;
    rightTeam: number;
    totalTeam: number;
    activeMembers: number;
    recentJoins: number;
  };
}

export const TreeStatsPanel: React.FC<TreeStatsPanelProps> = ({ stats, teamStats }) => {
  const getBalanceColor = (ratio: number) => {
    if (ratio >= 0.8) return 'text-green-600';
    if (ratio >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-white rounded-lg border">
      <div className="text-center">
        <div className="text-2xl font-bold text-blue-600">{stats.totalUsers}</div>
        <div className="text-xs text-gray-500">Total Users</div>
      </div>
      
      <div className="text-center">
        <div className={`text-2xl font-bold ${getBalanceColor(stats.balanceRatio)}`}>
          {Math.round(stats.balanceRatio * 100)}%
        </div>
        <div className="text-xs text-gray-500">Balance Ratio</div>
      </div>
      
      <div className="text-center">
        <div className="text-2xl font-bold text-purple-600">{stats.maxDepth}</div>
        <div className="text-xs text-gray-500">Max Depth</div>
      </div>
      
      <div className="text-center">
        <div className="text-2xl font-bold text-orange-600">{teamStats.activeMembers}</div>
        <div className="text-xs text-gray-500">Active Members</div>
      </div>
    </div>
  );
};

// Tree Legend Component
export const TreeLegend: React.FC = () => {
  return (
    <div className="p-4 bg-gray-50 rounded-lg border">
      <h4 className="text-sm font-medium text-gray-700 mb-3">Tree Legend</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-eco-500 rounded-full"></div>
            <span>Active User</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
            <span>Inactive User</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-blue-500 rounded bg-white"></div>
            <span>Focused User</span>
          </div>
        </div>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <span className="text-yellow-500">👑</span>
            <span>Has Sponsor Info</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-green-500">📈</span>
            <span>High Team Count</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-blue-500">🔍</span>
            <span>Click to Focus</span>
          </div>
        </div>
      </div>
      
      <div className="mt-3 pt-3 border-t border-gray-200">
        <p className="text-xs text-gray-600">
          <strong>Navigation:</strong> Use zoom controls to scale the view. 
          Click on nodes to focus. Use search to find specific users.
          Expand/collapse nodes using the arrow buttons.
        </p>
      </div>
    </div>
  );
};
