import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Fetch referral commission statistics
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get('dateRange') || '30d';

    // Calculate date filter
    let dateFilter: any = {};
    if (dateRange !== 'all') {
      const days = parseInt(dateRange.replace('d', ''));
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      dateFilter = { gte: startDate };
    }

    // Build where clause
    const whereClause: any = {
      type: 'DIRECT_REFERRAL',
      status: 'COMPLETED',
    };

    if (dateRange !== 'all') {
      whereClause.createdAt = dateFilter;
    }

    // Get basic stats
    const commissionStats = await prisma.transaction.aggregate({
      where: whereClause,
      _count: { id: true },
      _sum: { amount: true },
      _avg: { amount: true },
    });

    // Get recent activity (last 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentActivity = await prisma.transaction.count({
      where: {
        ...whereClause,
        createdAt: { gte: sevenDaysAgo },
      },
    });

    // Get top earners
    const topEarnersData = await prisma.transaction.groupBy({
      by: ['userId'],
      where: whereClause,
      _sum: { amount: true },
      _count: { id: true },
      orderBy: { _sum: { amount: 'desc' } },
      take: 10,
    });

    // Get user details for top earners
    const topEarners = await Promise.all(
      topEarnersData.map(async (earner) => {
        const user = await prisma.user.findUnique({
          where: { id: earner.userId },
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        });

        return {
          userId: earner.userId,
          user: user || {
            id: earner.userId,
            email: 'Unknown',
            firstName: 'Unknown',
            lastName: 'User',
          },
          totalEarned: earner._sum.amount || 0,
          commissionCount: earner._count.id,
        };
      })
    );

    const stats = {
      totalCommissions: commissionStats._count.id || 0,
      totalAmount: commissionStats._sum.amount || 0,
      averageCommission: commissionStats._avg.amount || 0,
      topEarners,
      recentActivity,
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });

  } catch (error: any) {
    console.error('Referral commission stats fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch referral commission stats' },
      { status: 500 }
    );
  }
}
