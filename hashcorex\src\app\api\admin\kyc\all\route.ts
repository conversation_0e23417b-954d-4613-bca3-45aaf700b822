import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Fetch all KYC submissions for admin review
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get all KYC documents grouped by user
    const allDocuments = await prisma.kYCDocument.findMany({
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            referralId: true,
            kycStatus: true,
            createdAt: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Group documents by user
    const userDocuments = new Map();
    
    for (const doc of allDocuments) {
      const userId = doc.userId;
      if (!userDocuments.has(userId)) {
        userDocuments.set(userId, {
          userId,
          user: {
            firstName: doc.user.firstName,
            lastName: doc.user.lastName,
            email: doc.user.email,
            referralId: doc.user.referralId,
          },
          documents: [],
          status: doc.user.kycStatus,
          submittedAt: doc.createdAt.toISOString(),
        });
      }
      
      userDocuments.get(userId).documents.push({
        id: doc.id,
        userId: doc.userId,
        documentType: doc.documentType,
        idType: doc.idType,
        documentSide: doc.documentSide,
        documentUrl: doc.filePath,
        status: doc.status,
        submittedAt: doc.createdAt.toISOString(),
        reviewedAt: doc.reviewedAt?.toISOString(),
        rejectionReason: doc.rejectionReason,
      });
    }

    const result = Array.from(userDocuments.values());

    return NextResponse.json({
      success: true,
      data: result,
    });

  } catch (error: any) {
    console.error('KYC all fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch KYC data' },
      { status: 500 }
    );
  }
}
