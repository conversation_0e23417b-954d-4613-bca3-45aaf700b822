import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { searchUsersInTree } from '@/lib/referral';

// GET - Search users in the binary tree
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const searchTerm = searchParams.get('term');
    const maxResults = parseInt(searchParams.get('limit') || '20');

    if (!searchTerm || searchTerm.trim().length < 2) {
      return NextResponse.json(
        { success: false, error: 'Search term must be at least 2 characters' },
        { status: 400 }
      );
    }

    // Search users in the binary tree
    const searchResults = await searchUsersInTree(user.id, searchTerm.trim(), maxResults);

    return NextResponse.json({
      success: true,
      data: searchResults,
    });

  } catch (error: any) {
    console.error('Binary tree search error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to search binary tree' },
      { status: 500 }
    );
  }
}
