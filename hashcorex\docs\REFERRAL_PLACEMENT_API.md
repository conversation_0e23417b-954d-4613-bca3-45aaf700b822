# Referral Placement API Reference

## Quick Reference

### URL Formats
```
General:    /register?ref=REFERRAL_ID
Left Side:  /register?ref=REFERRAL_ID&side=left
Right Side: /register?ref=REFERRAL_ID&side=right
```

### Core Functions

#### `placeUserByReferralType(referrerId, newUserId, referralType)`
**Main placement function - USE THIS for new implementations**

```typescript
await placeUserByReferralType(
  'sponsor-user-id',
  'new-user-id', 
  'left' | 'right' | 'general'
);
```

#### `placeUserInLeftSideOnly(referrerId, newUserId)`
**Direct left-side placement**

```typescript
const placementSide = await placeUserInLeftSideOnly('sponsor-id', 'new-user-id');
// Always returns 'LEFT'
```

#### `placeUserInRightSideOnly(referrerId, newUserId)`
**Direct right-side placement**

```typescript
const placementSide = await placeUserInRightSideOnly('sponsor-id', 'new-user-id');
// Always returns 'RIGHT'
```

#### `placeUserInBinaryTree(referrerId, newUserId)`
**Legacy general placement (weaker leg)**

```typescript
const placementSide = await placeUserInBinaryTree('sponsor-id', 'new-user-id');
// Returns 'LEFT' or 'RIGHT' based on weaker leg logic
```

## Registration Integration

### Frontend (React)
```typescript
// Extract side from URL
const side = searchParams.get('side') as 'left' | 'right' | null;

// Pass to registration
await register(
  email,
  firstName,
  lastName,
  password,
  confirmPassword,
  referralCode,
  side || undefined
);
```

### Backend (API Route)
```typescript
// Extract side parameter
const url = new URL(request.url);
const side = url.searchParams.get('side') as 'left' | 'right' | null;

// Register with placement side
const user = await registerUser({
  email,
  firstName,
  lastName,
  password,
  referralCode,
  placementSide: side || undefined,
});
```

### Auth Service
```typescript
// Determine referral type
let referralType: 'general' | 'left' | 'right' = 'general';
if (data.placementSide === 'left') referralType = 'left';
else if (data.placementSide === 'right') referralType = 'right';

// Place user
await placeUserByReferralType(referrerId, user.id, referralType);
```

## Link Generation

### Generate All Three Link Types
```typescript
import { generateReferralLinks } from '@/lib/referralValidation';

const links = generateReferralLinks(user.referralId, baseUrl);
// Returns:
// {
//   general: "https://site.com/register?ref=ABC123",
//   left: "https://site.com/register?ref=ABC123&side=left",
//   right: "https://site.com/register?ref=ABC123&side=right"
// }
```

### Manual Link Construction
```typescript
const baseUrl = process.env.NEXT_PUBLIC_APP_URL;
const referralId = user.referralId;

const generalLink = `${baseUrl}/register?ref=${referralId}`;
const leftLink = `${baseUrl}/register?ref=${referralId}&side=left`;
const rightLink = `${baseUrl}/register?ref=${referralId}&side=right`;
```

## Validation & Utilities

### Extract Placement Side from URL
```typescript
import { extractPlacementSide } from '@/lib/referralValidation';

const side = extractPlacementSide(url);
// Returns: 'left' | 'right' | null
```

### Validate Referral Code
```typescript
import { validateReferralCode } from '@/lib/referralValidation';

const result = await validateReferralCode(referralCode);
if (result.isValid) {
  // Proceed with registration
  const referrer = result.referrer;
}
```

## Database Queries

### Find User's Placement in Tree
```typescript
const referral = await prisma.referral.findFirst({
  where: { referredId: userId },
  include: { referrer: true }
});

const placementSide = referral?.placementSide; // 'LEFT' | 'RIGHT'
const parent = referral?.referrer;
```

### Get Downline Count by Side
```typescript
import { calculateDownlineCount } from '@/lib/referral';

const leftCount = await calculateDownlineCount(userId, 'LEFT');
const rightCount = await calculateDownlineCount(userId, 'RIGHT');
```

### Get Tree Structure
```typescript
import { getBinaryTreeStructure } from '@/lib/referral';

const tree = await getBinaryTreeStructure(userId, depth);
```

## Error Handling

### Common Error Scenarios
```typescript
try {
  await placeUserByReferralType(referrerId, newUserId, type);
} catch (error) {
  if (error.message.includes('Foreign key constraint')) {
    // Referrer doesn't exist
    console.error('Invalid referrer ID');
  } else if (error.message.includes('User already exists')) {
    // Duplicate user
    console.error('User already registered');
  } else {
    // Other placement errors
    console.error('Placement failed:', error.message);
  }
}
```

### Validation Before Placement
```typescript
// Validate referrer exists
const referrer = await prisma.user.findUnique({
  where: { id: referrerId }
});

if (!referrer) {
  throw new Error('Invalid referrer');
}

// Validate new user doesn't already exist
const existingUser = await prisma.user.findUnique({
  where: { email: newUserEmail }
});

if (existingUser) {
  throw new Error('User already exists');
}
```

## Testing

### Test Placement Types
```typescript
import { 
  placeUserByReferralType,
  placeUserInLeftSideOnly,
  placeUserInRightSideOnly 
} from '@/lib/referral';

// Test general placement
const generalSide = await placeUserByReferralType('ref1', 'user1', 'general');

// Test left-only placement
const leftSide = await placeUserInLeftSideOnly('ref1', 'user2');
expect(leftSide).toBe('LEFT');

// Test right-only placement
const rightSide = await placeUserInRightSideOnly('ref1', 'user3');
expect(rightSide).toBe('RIGHT');
```

### Verify Placement Results
```typescript
// Check placement in database
const referral = await prisma.referral.findFirst({
  where: { referredId: newUserId }
});

expect(referral?.placementSide).toBe('LEFT'); // or 'RIGHT'
expect(referral?.referrerId).toBe(expectedParentId);
```

## Migration Notes

### From Old System
- Replace `placeUserInSpecificSide()` calls with `placeUserByReferralType()`
- Update link generation to use new format
- Test existing functionality remains unchanged

### Backward Compatibility
- Old general links continue to work
- Legacy functions still available
- No database schema changes required

## Performance Considerations

### Optimization Tips
1. Use cached downline counts when possible
2. Limit tree traversal depth (default: 20 levels)
3. Batch operations for multiple placements
4. Monitor database query performance

### Monitoring
```typescript
// Log placement performance
console.time('placement');
await placeUserByReferralType(referrerId, newUserId, type);
console.timeEnd('placement');
```

## Security Notes

### Input Validation
- Always validate referral codes
- Sanitize URL parameters
- Check user permissions
- Prevent self-referrals

### Rate Limiting
- Implement placement rate limits
- Monitor for abuse patterns
- Log suspicious activity
- Validate referrer activity status
