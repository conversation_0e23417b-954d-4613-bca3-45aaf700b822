import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';

// GET - Check if user is admin
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated', isAdmin: false },
        { status: 401 }
      );
    }

    const userIsAdmin = await isAdmin(user.id);

    return NextResponse.json({
      success: true,
      isAdmin: userIsAdmin,
      user: {
        id: user.id,
        email: user.email,
      },
    });

  } catch (error: any) {
    console.error('Admin check error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Admin check failed', isAdmin: false },
      { status: 500 }
    );
  }
}
