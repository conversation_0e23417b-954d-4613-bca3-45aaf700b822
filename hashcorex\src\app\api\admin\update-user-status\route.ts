import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { updateAllUsersActiveStatus } from '@/lib/mining';

// POST - Update all users' active status based on mining units
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Update all users' active status
    const results = await updateAllUsersActiveStatus();

    return NextResponse.json({
      success: true,
      message: 'User active status updated successfully',
      data: {
        totalUsers: results.length,
        activeUsers: results.filter(r => r.isActive).length,
        inactiveUsers: results.filter(r => !r.isActive).length,
      },
    });

  } catch (error: any) {
    console.error('User status update error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to update user status' },
      { status: 500 }
    );
  }
}
