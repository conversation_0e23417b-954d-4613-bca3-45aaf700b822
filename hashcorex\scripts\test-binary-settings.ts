import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testBinarySettings() {
  try {
    console.log('🧪 Testing Binary Settings Storage and Retrieval...\n');

    // Test 1: Check current database values
    console.log('📋 Current Database Values:');
    const currentSettings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: ['MAX_BINARY_POINTS_PER_SIDE', 'BINARY_POINT_VALUE', 'BINARY_MATCHING_ENABLED', 'BINARY_MATCHING_SCHEDULE']
        }
      }
    });

    currentSettings.forEach(setting => {
      console.log(`  ${setting.key}: ${setting.value}`);
    });

    // Test 2: Simulate SystemSettings save
    console.log('\n🔄 Simulating SystemSettings Save (maxBinaryPointsPerSide: 25)...');
    
    await prisma.adminSettings.upsert({
      where: { key: 'MAX_BINARY_POINTS_PER_SIDE' },
      update: { value: '25' },
      create: { key: 'MAX_BINARY_POINTS_PER_SIDE', value: '25' }
    });

    // Test 3: Verify the update
    console.log('\n✅ After Update:');
    const updatedSetting = await prisma.adminSettings.findUnique({
      where: { key: 'MAX_BINARY_POINTS_PER_SIDE' }
    });
    console.log(`  MAX_BINARY_POINTS_PER_SIDE: ${updatedSetting?.value}`);

    // Test 4: Test how other parts of the system read it
    console.log('\n🔍 Testing System Reading (how referral.ts reads it):');
    const maxPointsPerSide = parseFloat(updatedSetting?.value || '10');
    console.log(`  Parsed value: ${maxPointsPerSide}`);
    console.log(`  Type: ${typeof maxPointsPerSide}`);

    // Test 5: Test admin settings API format
    console.log('\n📡 Testing Admin Settings API Format:');
    const allSettings = await prisma.adminSettings.findMany();
    const settingsObject: any = {};
    
    allSettings.forEach(setting => {
      try {
        settingsObject[setting.key] = JSON.parse(setting.value);
      } catch {
        settingsObject[setting.key] = setting.value;
      }
    });

    // Map database keys to frontend keys
    if (settingsObject['MAX_BINARY_POINTS_PER_SIDE']) {
      settingsObject.maxBinaryPointsPerSide = parseFloat(settingsObject['MAX_BINARY_POINTS_PER_SIDE']);
    }

    console.log(`  Raw MAX_BINARY_POINTS_PER_SIDE: ${settingsObject['MAX_BINARY_POINTS_PER_SIDE']}`);
    console.log(`  Mapped maxBinaryPointsPerSide: ${settingsObject.maxBinaryPointsPerSide}`);
    console.log(`  Type: ${typeof settingsObject.maxBinaryPointsPerSide}`);

    // Test 6: Test different values
    console.log('\n🎯 Testing Different Values:');
    const testValues = [10, 50, 100, 500];
    
    for (const testValue of testValues) {
      await prisma.adminSettings.upsert({
        where: { key: 'MAX_BINARY_POINTS_PER_SIDE' },
        update: { value: testValue.toString() },
        create: { key: 'MAX_BINARY_POINTS_PER_SIDE', value: testValue.toString() }
      });

      const result = await prisma.adminSettings.findUnique({
        where: { key: 'MAX_BINARY_POINTS_PER_SIDE' }
      });

      const parsedValue = parseFloat(result?.value || '10');
      console.log(`  Set: ${testValue} → Stored: "${result?.value}" → Parsed: ${parsedValue} ✅`);
    }

    console.log('\n🎉 Binary Settings Test Completed Successfully!');
    console.log('\n💡 Key Findings:');
    console.log('  - Settings are stored as strings in the database');
    console.log('  - Frontend uses camelCase keys (maxBinaryPointsPerSide)');
    console.log('  - Backend uses UPPER_CASE keys (MAX_BINARY_POINTS_PER_SIDE)');
    console.log('  - Values need to be parsed as float when reading');

  } catch (error) {
    console.error('❌ Error testing binary settings:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testBinarySettings();
