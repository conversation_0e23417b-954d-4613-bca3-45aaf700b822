import { fromHex, toHex } from 'tron-format-address';

async function analyzeTransaction() {
  console.log('🔍 Analyzing Transaction Structure...\n');

  const txId = '30a36a84f7d77e822f92ab36bb53d98835373a2e638601d1e321224b4b8c1114';
  
  try {
    // Get transaction details
    const response = await fetch('https://api.shasta.trongrid.io/walletsolidity/gettransactionbyid', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        value: txId
      })
    });

    const transaction = await response.json();
    console.log('📋 Full Transaction Details:');
    console.log(JSON.stringify(transaction, null, 2));

    // Get transaction info
    const infoResponse = await fetch('https://api.shasta.trongrid.io/walletsolidity/gettransactioninfobyid', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        value: txId
      })
    });

    const transactionInfo = await infoResponse.json();
    console.log('\n📋 Full Transaction Info:');
    console.log(JSON.stringify(transactionInfo, null, 2));

    // Analyze the contract call
    if (transaction.raw_data && transaction.raw_data.contract) {
      const contract = transaction.raw_data.contract[0];
      console.log('\n📋 Contract Details:');
      console.log(JSON.stringify(contract, null, 2));

      if (contract.parameter && contract.parameter.value) {
        const contractValue = contract.parameter.value;
        console.log('\n📋 Contract Parameters:');
        console.log(`  Owner Address: ${contractValue.owner_address}`);
        console.log(`  Contract Address: ${contractValue.contract_address}`);
        console.log(`  Function Selector: ${contractValue.function_selector}`);
        console.log(`  Data: ${contractValue.data}`);

        // Try to convert addresses
        if (contractValue.owner_address) {
          try {
            const ownerBase58 = fromHex('41' + contractValue.owner_address);
            console.log(`  Owner Address (Base58): ${ownerBase58}`);
          } catch (e) {
            console.log(`  Owner Address conversion failed: ${e}`);
          }
        }

        if (contractValue.contract_address) {
          try {
            const contractBase58 = fromHex('41' + contractValue.contract_address);
            console.log(`  Contract Address (Base58): ${contractBase58}`);
          } catch (e) {
            console.log(`  Contract Address conversion failed: ${e}`);
          }
        }

        // Parse the data field which contains the transfer parameters
        if (contractValue.data) {
          console.log('\n📋 Parsing Transfer Data:');
          const data = contractValue.data;
          console.log(`  Raw Data: ${data}`);
          
          // First 8 characters are function selector (a9059cbb = transfer)
          const functionSelector = data.slice(0, 8);
          console.log(`  Function Selector: ${functionSelector}`);
          
          // Next 64 characters are the recipient address (padded)
          const recipientHex = data.slice(8, 72);
          console.log(`  Recipient Hex (padded): ${recipientHex}`);
          
          // Remove padding (first 24 characters are zeros)
          const recipientAddress = recipientHex.slice(24);
          console.log(`  Recipient Address (hex): ${recipientAddress}`);
          
          try {
            const recipientBase58 = fromHex('41' + recipientAddress);
            console.log(`  Recipient Address (Base58): ${recipientBase58}`);
          } catch (e) {
            console.log(`  Recipient conversion failed: ${e}`);
          }
          
          // Last 64 characters are the amount
          const amountHex = data.slice(72);
          console.log(`  Amount Hex: ${amountHex}`);
          const amount = parseInt(amountHex, 16) / 1000000; // USDT has 6 decimals
          console.log(`  Amount: ${amount} USDT`);
        }
      }
    }

  } catch (error) {
    console.error('Error analyzing transaction:', error);
  }
}

analyzeTransaction();
