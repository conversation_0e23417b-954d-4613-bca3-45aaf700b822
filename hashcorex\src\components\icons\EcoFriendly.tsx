'use client';

import React from 'react';

interface IconProps {
  className?: string;
  size?: number;
}

export const Leaf: React.FC<IconProps> = ({ className, size = 24 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path d="M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z" fill="currentColor"/>
    </svg>
  );
};

export const Recycle: React.FC<IconProps> = ({ className, size = 24 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path d="M7 19H4.815a1.83 1.83 0 01-1.57-.881 1.785 1.785 0 01-.004-1.784L7.196 9.5" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M11 19h8.203a1.83 1.83 0 001.556-.89 1.784 1.784 0 000-1.775l-1.226-2.12" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M14 16l-3 3 3 3" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M8.293 13.596L7.196 9.5l3.1 1.598" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M9.344 5.811L11.271 2a1.784 1.784 0 011.57-.881c.65 0 1.235.361 1.556.881l3.68 6.361" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M16 8l3-3-3-3" stroke="currentColor" strokeWidth="2" fill="none"/>
    </svg>
  );
};

export const WindTurbine: React.FC<IconProps> = ({ className, size = 24 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <line x1="12" y1="12" x2="12" y2="22" stroke="currentColor" strokeWidth="2"/>
      <path d="M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z" stroke="currentColor" strokeWidth="2" fill="none"/>
      <circle cx="12" cy="12" r="1" fill="currentColor"/>
      <line x1="10" y1="22" x2="14" y2="22" stroke="currentColor" strokeWidth="2"/>
    </svg>
  );
};
