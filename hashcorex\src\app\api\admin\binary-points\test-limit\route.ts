import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { adminSettingsDb, binaryPointsDb } from '@/lib/database';

// POST - Test binary points limit functionality (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { testUserId, testSide, testPoints } = body;

    if (!testUserId || !testSide || !testPoints) {
      return NextResponse.json(
        { success: false, error: 'Missing required test parameters' },
        { status: 400 }
      );
    }

    // Get current settings
    const maxPointsPerSide = parseFloat(await adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE') || '10');
    
    // Get current binary points for test user
    const currentBinaryPoints = await binaryPointsDb.findByUserId(testUserId);
    const currentLeftPoints = currentBinaryPoints?.leftPoints || 0;
    const currentRightPoints = currentBinaryPoints?.rightPoints || 0;

    console.log('Binary Points Limit Test:');
    console.log(`Max Points Per Side: ${maxPointsPerSide}`);
    console.log(`Current Left Points: ${currentLeftPoints}`);
    console.log(`Current Right Points: ${currentRightPoints}`);
    console.log(`Test Side: ${testSide}`);
    console.log(`Test Points to Add: ${testPoints}`);

    let pointsToAdd = 0;
    let canAddPoints = false;
    let reason = '';

    if (testSide === 'LEFT') {
      if (currentLeftPoints >= maxPointsPerSide) {
        reason = `Left side has reached maximum (${currentLeftPoints}/${maxPointsPerSide}). No points can be added.`;
        canAddPoints = false;
      } else {
        pointsToAdd = Math.min(testPoints, maxPointsPerSide - currentLeftPoints);
        canAddPoints = true;
        reason = `Can add ${pointsToAdd} points to left side (${pointsToAdd < testPoints ? 'capped at limit' : 'full amount'})`;
      }
    } else {
      if (currentRightPoints >= maxPointsPerSide) {
        reason = `Right side has reached maximum (${currentRightPoints}/${maxPointsPerSide}). No points can be added.`;
        canAddPoints = false;
      } else {
        pointsToAdd = Math.min(testPoints, maxPointsPerSide - currentRightPoints);
        canAddPoints = true;
        reason = `Can add ${pointsToAdd} points to right side (${pointsToAdd < testPoints ? 'capped at limit' : 'full amount'})`;
      }
    }

    console.log(`Result: ${reason}`);

    // Actually add the points if requested
    if (canAddPoints && pointsToAdd > 0) {
      const updateData = testSide === 'LEFT'
        ? { leftPoints: pointsToAdd }
        : { rightPoints: pointsToAdd };

      await binaryPointsDb.upsert({
        userId: testUserId,
        ...updateData,
      });

      console.log(`Successfully added ${pointsToAdd} points to ${testSide} side for user ${testUserId}`);
    }

    // Get updated points
    const updatedBinaryPoints = await binaryPointsDb.findByUserId(testUserId);

    return NextResponse.json({
      success: true,
      message: 'Binary points limit test completed',
      data: {
        settings: {
          maxPointsPerSide,
        },
        before: {
          leftPoints: currentLeftPoints,
          rightPoints: currentRightPoints,
        },
        test: {
          side: testSide,
          requestedPoints: testPoints,
          pointsAdded: pointsToAdd,
          canAddPoints,
          reason,
        },
        after: {
          leftPoints: updatedBinaryPoints?.leftPoints || 0,
          rightPoints: updatedBinaryPoints?.rightPoints || 0,
        },
      },
    });

  } catch (error) {
    console.error('Binary points limit test error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to test binary points limit' },
      { status: 500 }
    );
  }
}
