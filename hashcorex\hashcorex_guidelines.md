### 🔆 HashCoreX UI/UX Design Specification (Cross-Platform)

This section defines the visual and interactive identity of the HashCoreX cloud mining investment platform. The design must reflect our commitment to **renewable energy, decentralization, and futuristic technology**, particularly solar-powered green crypto mining.

---

## 🌐 Thematic UI Vision

**Overall Theme**: Light, clean, modern, futuristic UI with a focus on\:Clean, modern, futuristic UI with a focus on:

- Renewable energy (solar, wind, green tech)
- Cryptocurrency (blockchain, mining, tech grids)
- Cross-platform responsiveness (Web, Android, iOS)

**Color Palette**:

- White (#ffffff) – Background
- Solar Yellow (#ffd60a) – Highlights / Call-to-Actions
- Emerald Green (#10b981) – Success / Eco / Verified
- Deep Blue (#0f172a) – Primary Text
- Grey (#6b7280) – Secondary UI
- Solar Yellow (#ffd60a) – Highlights / Call-to-Actions
- Emerald Green (#10b981) – Success / Eco / Verified
- White (#ffffff) – Text & clarity
- Grey (#6b7280) – Secondary UI

**Typography**:

- Primary font: Inter / Roboto
- Size: 14px base, 18px body, 24px headings
- UI scale ratio: 1.2 modular scale

---

## 📄 Public Landing Pages (Non-Authenticated)

These pages are accessible to everyone:

### 1. **Homepage**

- Hero section with animated background (solar grids + mining rigs)
- Intro text: "Invest in Solar-Powered Cloud Mining"
- CTA: "Start Mining Today"
- Features section with icons (KYC, USDT, Real ROI, Eco Mining)
- Referral and bonus program showcase
- Live stats (hashrate sold, users active, eco impact)
- Contact us form
- Footer with links (Privacy, Terms, Socials)

### 2. **About Us**

- Company story + mission (solar-powered mining data centers)
- Roadmap timeline with energy goals
- Sustainability badge section

### 3. **How It Works**

- Step-by-step process (Register > Verify > Buy TH/s > Earn)
- Illustrated explanation of ROI calculation & binary network

### 4. **Login / Register**

- Clean form
- Google 2FA optional at login
- Password strength and visibility toggle
- Redirect to dashboard upon login



---

## 🔐 Authenticated User Dashboard Pages

All below pages use the same design language as landing pages but now focus on secure user interactions and performance visibility.

### 1. **Dashboard Home**

- Total TH/s summary
- Estimated earnings card (next 7, 30, 365 days)
- Wallet balance
- Mini binary tree preview
- KYC status with upload link

### 2. **Mining Units Page**

- Table of active units
- ROI earned
- Status (Active / Expired)
- Purchase new TH/s section with real-time pricing

### 3. **Wallet & Earnings**

- Transaction history table
- Breakdown: Mining ROI, Referral Bonuses, Binary Bonuses
- Deposit option with USDT (TRC20) integration
  - Generate deposit address for user
  - Show pending and confirmed deposit status
  - Auto-credit deposits to wallet balance after confirmation
- Withdrawal form
- Input for USDT TRC20 address

### 4. **Referral & Network**
- Left & Right referral links clearly displayed
- Real-time referral tree visualizer
- Show commission breakdown from direct and binary referrals
- Display matched points (left vs right)
- Indicate pressure-out logic and flush/reset activity
- Visual indicators for active legs and earnings history

### 5. **KYC Verification**

- Upload field for ID + selfie
- Status tracker (Pending > Approved > Rejected)

### 6. **Support & Notifications**

- Ticket system
- Admin message center
- Push/Email preference settings

---

## 📱 Mobile Adaptation (Responsive First)

- Use mobile-first breakpoints
- Tab-based dashboard navigation
- Sticky bottom CTA buttons for purchasing, withdrawing
- Full feature parity with web

---

## 🎨 UI Notes for AI-Generated Elements

- Icons should feel futuristic yet friendly
- Use SVG for all illustrations (eco + mining themed)
- Buttons should be large, tactile, and contrast strongly
- Input fields with soft inner shadows and clear borders

---

## 🧠 HashCoreX – AI-Ready Development Checklist & Platform Implementation Plan

### Objective:

You are tasked with building HashCoreX — a full-featured, cross-platform cloud mining investment platform. This platform must support custom TH/s purchases, binary referral logic, weekly earnings, internal wallet tracking, admin oversight, and full KYC compliance. It will operate on both web and mobile platforms with shared functionality and real-time syncing.

---

### ✅ 1. General Requirements

- Build a fully cross-platform application (Web + Android + iOS).
- Use a shared codebase for frontend (web + mobile).
- Use a centralized PostgreSQL database for relational data.
- Support custom logic for mining earnings, referral commissions, and binary pairing.
- Every function must be reusable, modular, and documented.
- No framework or code-specific naming needed in output. Use logic and behavior descriptions.

---

### ✅ 2. Authentication & Account System

- Users register with email + password.
- Use Firebase Authentication for secure login and 2FA.
- Generate and store a unique referral ID per user.
- Display left and right referral links on dashboard.
- Allow users to upload KYC documents (photo + ID) for verification.
- Store KYC status (Pending, Approved, Rejected).
- KYC status must be required to process any withdrawal.

---

### ✅ 3. TH/s Mining Unit System

- Allow user to input custom TH/s amount.
- Minimum purchase = \$50 (calculate price per TH/s based on admin settings).
- Each purchase creates a mining unit with:
  - Unique ID
  - Start date
  - Expiry = 24 months from start
  - Daily ROI range
  - Current earned total
  - Status = Active or Expired
- Users can hold multiple mining units.
- Units expire automatically when:
  - 24 months pass OR
  - 5× the unit’s investment is earned (from mining + referrals)

---

### ✅ 4. ROI & Earnings Engine

- Admin can set ROI % or define randomized range per TH/s tier (e.g., 0.3%–0.7% based on TH/s amount).
- Each day:
  - Loop through active mining units.
  - Apply daily ROI % based on TH/s size.
  - Add to user’s PENDING earnings balance.
- Every Saturday 15:00 UTC:
  - Transfer all pending mining earnings to the user’s internal wallet.
  - Log transaction.

---

### ✅ 5. Internal Wallet System

- Display real-time balance from:
  - Mining earnings
  - Direct referral bonuses
  - Binary matching bonuses
- Users can:
  - View transaction history (credited/withdrawn)
  - Initiate withdrawal request
  - Enter USDT (TRC20) wallet address
- Admin must review and approve withdrawals manually.
- Minimum withdrawal threshold: \$10

---

### ✅ 6. Binary Referral System

- Each user has 2 legs: LEFT and RIGHT.
- Provide 2 unique referral links.
- New user placement:
  - Default to weaker leg (fewer points)
- Direct referral bonus: 10% of investment
- Binary pool: 30% of all global user investments
- Every \$1 invested = 1 binary point
- At 12:00 AM UTC daily:
  - Match left and right binary points (max 2,000 per side)
  - Payout matched amount from binary pool (30%)
  - Excess points are flushed/reset
  - Matched earnings added to internal wallet
- Referral income also counts toward mining unit 5× earning limit

---

### ✅ 7. Admin Dashboard Functions

- Login with 2FA
- View stats:
  - Active users
  - TH/s sold
  - Weekly mining summary
- Manage:
  - TH/s price
  - ROI %
  - Bonus percentages
  - Daily match caps
- Approve/Reject:
  - KYC submissions
  - Withdrawal requests
- Monitor:
  - Binary pool distribution
  - Each user’s earning/withdrawal log
- Broadcast system messages
- View audit logs (admin actions, payout records)

---

### ✅ 8. User Dashboard Functions

- Show:
  - Total TH/s active
  - Active/expired units
  - Estimated earnings (next 7/30/365 days)
  - Earnings tracker (mining + referral)
  - Binary pairing tracker
  - Wallet balance + log
- Show binary referral tree (left/right)
- KYC status and upload portal
- Referral links panel
- Withdraw funds
- View support messages, notifications

---

### ✅ 9. Security & Infrastructure

- PostgreSQL hosted on VPS
- Use AES-256 encryption for wallet, identity, and transaction data
- Enable 2FA on all sensitive actions
- Protect backend APIs with secure token system
- Monitor server health with Prometheus + Grafana
- Use Cloudflare WAF + DDoS protection
- Enable full disaster recovery backup system

---

### ✅ 10. Mobile App & Cross-Platform Delivery

- Use shared UI logic and components for both platforms
- Generate standalone apps using CapacitorJS
- Integrate push notifications for wallet, ROI, referrals
- Match web functionality 1:1 on mobile

---

✅ **All modules and designs must follow the above integrated UI structure and development specification to ensure a seamless AI-assisted rollout.**

