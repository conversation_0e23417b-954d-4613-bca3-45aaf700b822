# HashCoreX Prisma Database Guide

This comprehensive guide covers how to access, manage, and work with the HashCoreX database using Prisma ORM.

## Table of Contents

1. [Database Setup](#database-setup)
2. [Prisma Configuration](#prisma-configuration)
3. [Database Schema](#database-schema)
4. [Common Operations](#common-operations)
5. [Advanced Queries](#advanced-queries)
6. [Database Migrations](#database-migrations)
7. [Troubleshooting](#troubleshooting)

## Database Setup

### Prerequisites

- PostgreSQL 12+ installed and running
- Node.js 18+ installed
- HashCoreX project cloned and dependencies installed

### Environment Configuration

1. **Configure Database Connection**
   ```bash
   # .env.local
   DATABASE_URL="postgresql://username:password@localhost:5432/hashcorex?schema=public"
   DIRECT_URL="postgresql://username:password@localhost:5432/hashcorex?schema=public"

   # Admin Configuration
   ADMIN_EMAIL="<EMAIL>"
   ADMIN_PASSWORD="your_secure_admin_password"
   ```

2. **Create Database**
   ```sql
   -- Connect to PostgreSQL as superuser
   CREATE DATABASE hashcorex;
   CREATE USER hashcorex_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE hashcorex TO hashcorex_user;
   ```

3. **Initialize Database**
   ```bash
   # Run migrations
   npx prisma migrate dev

   # Generate Prisma client
   npx prisma generate

   # Seed database with initial data
   npx prisma db seed
   ```

### Creating Admin User

After setting up the database, create an admin user:

```typescript
// scripts/create-admin.ts
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createAdmin() {
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';

  // Check if admin already exists
  const existingAdmin = await prisma.user.findUnique({
    where: { email: adminEmail },
  });

  if (existingAdmin) {
    console.log('Admin user already exists');
    return;
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(adminPassword, 12);

  // Generate unique referral ID
  let referralId: string;
  let isUnique = false;
  do {
    referralId = Math.random().toString(36).substring(2, 8).toUpperCase();
    const existing = await prisma.user.findUnique({
      where: { referralId },
    });
    isUnique = !existing;
  } while (!isUnique);

  // Create admin user
  const admin = await prisma.user.create({
    data: {
      email: adminEmail,
      firstName: 'Admin',
      lastName: 'User',
      password: hashedPassword,
      referralId,
      role: 'ADMIN',
      kycStatus: 'APPROVED',
      isActive: true,
    },
  });

  console.log('Admin user created:', admin.email);
}

createAdmin()
  .catch(console.error)
  .finally(() => prisma.$disconnect());
```

Run the script:
```bash
npx tsx scripts/create-admin.ts
```

## Prisma Configuration

### Installation and Setup

```bash
# Install Prisma CLI globally (optional)
npm install -g prisma

# Generate Prisma Client
npx prisma generate

# Run database migrations
npx prisma migrate dev

# Seed the database (if seed file exists)
npx prisma db seed
```

### Prisma Studio (Database GUI)

```bash
# Launch Prisma Studio for visual database management
npx prisma studio
```

Access at: http://localhost:5555

## Database Schema

### Core Tables

#### Users Table
```prisma
model User {
  id              String    @id @default(cuid())
  email           String    @unique
  firstName       String
  lastName        String
  password        String
  referralId      String    @unique
  referrerId      String?
  role            UserRole  @default(USER)
  isActive        Boolean   @default(true)
  kycStatus       KYCStatus @default(PENDING)
  leftReferralId  String?
  rightReferralId String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}
```

#### Mining Units Table
```prisma
model MiningUnit {
  id               String           @id @default(cuid())
  userId           String
  thsAmount        Float
  investmentAmount Float
  status           MiningUnitStatus @default(ACTIVE)
  purchaseDate     DateTime         @default(now())
  expiryDate       DateTime?
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt
}
```

#### Transactions Table
```prisma
model Transaction {
  id          String            @id @default(cuid())
  userId      String
  type        TransactionType
  amount      Float
  description String?
  status      TransactionStatus @default(PENDING)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
}
```

## Common Operations

### 1. User Management

#### Create a New User
```typescript
import { prisma } from '@/lib/prisma';

const user = await prisma.user.create({
  data: {
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    password: 'hashedPassword',
    referralId: 'ABC123',
    role: 'USER',
  },
});
```

#### Find User by Email
```typescript
const user = await prisma.user.findUnique({
  where: {
    email: '<EMAIL>',
  },
});
```

#### Update User KYC Status
```typescript
const updatedUser = await prisma.user.update({
  where: { id: userId },
  data: {
    kycStatus: 'APPROVED',
  },
});
```

#### Get All Active Users
```typescript
const activeUsers = await prisma.user.findMany({
  where: {
    isActive: true,
  },
  select: {
    id: true,
    email: true,
    firstName: true,
    lastName: true,
    role: true,
    createdAt: true,
  },
});
```

### 2. Mining Units Management

#### Create Mining Unit
```typescript
const miningUnit = await prisma.miningUnit.create({
  data: {
    userId: 'user_id',
    thsAmount: 100.0,
    investmentAmount: 5000.0,
    status: 'ACTIVE',
    expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
  },
});
```

#### Get User's Mining Units
```typescript
const userMiningUnits = await prisma.miningUnit.findMany({
  where: {
    userId: 'user_id',
    status: 'ACTIVE',
  },
  orderBy: {
    createdAt: 'desc',
  },
});
```

#### Calculate Total THS for User
```typescript
const totalTHS = await prisma.miningUnit.aggregate({
  where: {
    userId: 'user_id',
    status: 'ACTIVE',
  },
  _sum: {
    thsAmount: true,
  },
});
```

### 3. Transaction Management

#### Create Transaction
```typescript
const transaction = await prisma.transaction.create({
  data: {
    userId: 'user_id',
    type: 'MINING_REWARD',
    amount: 50.0,
    description: 'Daily mining reward',
    status: 'COMPLETED',
  },
});
```

#### Get User Transaction History
```typescript
const transactions = await prisma.transaction.findMany({
  where: {
    userId: 'user_id',
  },
  orderBy: {
    createdAt: 'desc',
  },
  take: 50, // Limit to 50 records
});
```

#### Calculate User Balance
```typescript
const balance = await prisma.transaction.aggregate({
  where: {
    userId: 'user_id',
    status: 'COMPLETED',
  },
  _sum: {
    amount: true,
  },
});
```

## Advanced Queries

### 1. Complex Joins and Relations

#### Get Users with Their Mining Units
```typescript
const usersWithMiningUnits = await prisma.user.findMany({
  include: {
    miningUnits: {
      where: {
        status: 'ACTIVE',
      },
    },
  },
});
```

#### Get User with Total Investment and Earnings
```typescript
const userStats = await prisma.user.findUnique({
  where: { id: 'user_id' },
  include: {
    miningUnits: {
      select: {
        investmentAmount: true,
      },
    },
    transactions: {
      where: {
        type: 'MINING_REWARD',
        status: 'COMPLETED',
      },
      select: {
        amount: true,
      },
    },
  },
});
```

### 2. Aggregations and Analytics

#### Platform Statistics
```typescript
const platformStats = await prisma.$transaction([
  // Total users
  prisma.user.count(),
  
  // Active users
  prisma.user.count({
    where: { isActive: true },
  }),
  
  // Total investments
  prisma.miningUnit.aggregate({
    _sum: { investmentAmount: true },
  }),
  
  // Total THS sold
  prisma.miningUnit.aggregate({
    _sum: { thsAmount: true },
  }),
  
  // Total earnings distributed
  prisma.transaction.aggregate({
    where: {
      type: 'MINING_REWARD',
      status: 'COMPLETED',
    },
    _sum: { amount: true },
  }),
]);
```

#### Monthly Revenue Report
```typescript
const monthlyRevenue = await prisma.miningUnit.groupBy({
  by: ['createdAt'],
  where: {
    createdAt: {
      gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    },
  },
  _sum: {
    investmentAmount: true,
  },
});
```

### 3. Raw SQL Queries

For complex queries that Prisma doesn't handle well:

```typescript
// Raw query example
const result = await prisma.$queryRaw`
  SELECT 
    u.id,
    u.email,
    COUNT(mu.id) as mining_units_count,
    SUM(mu.investment_amount) as total_investment,
    SUM(CASE WHEN t.type = 'MINING_REWARD' THEN t.amount ELSE 0 END) as total_earnings
  FROM "User" u
  LEFT JOIN "MiningUnit" mu ON u.id = mu.user_id
  LEFT JOIN "Transaction" t ON u.id = t.user_id
  WHERE u.is_active = true
  GROUP BY u.id, u.email
  ORDER BY total_investment DESC
  LIMIT 10
`;
```

## Database Migrations

### Creating Migrations

```bash
# Create a new migration
npx prisma migrate dev --name add_new_feature

# Apply migrations to production
npx prisma migrate deploy

# Reset database (development only)
npx prisma migrate reset
```

### Migration Best Practices

1. **Always backup before migrations in production**
2. **Test migrations on staging environment first**
3. **Use descriptive migration names**
4. **Review generated SQL before applying**

### Example Migration Workflow

```bash
# 1. Modify schema.prisma
# 2. Generate migration
npx prisma migrate dev --name add_user_preferences

# 3. Review generated migration file
# 4. Test locally
# 5. Apply to staging
# 6. Apply to production
npx prisma migrate deploy
```

## Troubleshooting

### Common Issues

#### 1. Connection Issues
```bash
# Test database connection
npx prisma db pull

# Check environment variables
echo $DATABASE_URL
```

#### 2. Schema Sync Issues
```bash
# Reset and regenerate
npx prisma migrate reset
npx prisma generate
```

#### 3. Type Generation Issues
```bash
# Regenerate Prisma client
npx prisma generate

# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Performance Optimization

#### 1. Indexing
```prisma
model User {
  email String @unique @db.VarChar(255)
  
  @@index([createdAt])
  @@index([isActive, role])
}
```

#### 2. Connection Pooling
```typescript
// lib/prisma.ts
import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: ['query'],
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;
```

### Monitoring and Logging

#### Enable Query Logging
```typescript
const prisma = new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'stdout',
      level: 'error',
    },
    {
      emit: 'stdout',
      level: 'info',
    },
    {
      emit: 'stdout',
      level: 'warn',
    },
  ],
});

prisma.$on('query', (e) => {
  console.log('Query: ' + e.query);
  console.log('Duration: ' + e.duration + 'ms');
});
```

## Useful Commands Reference

```bash
# Database operations
npx prisma db pull          # Pull schema from database
npx prisma db push          # Push schema to database
npx prisma db seed          # Run seed script

# Migration operations
npx prisma migrate dev      # Create and apply migration
npx prisma migrate deploy   # Apply pending migrations
npx prisma migrate status   # Check migration status
npx prisma migrate reset    # Reset database

# Client operations
npx prisma generate         # Generate Prisma client
npx prisma studio          # Open Prisma Studio

# Validation
npx prisma validate        # Validate schema
npx prisma format          # Format schema file
```

---

For more detailed information, refer to the [official Prisma documentation](https://www.prisma.io/docs/).
