import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { 
  getDetailedTeamStats, 
  getTreeHealthStats, 
  getUsersByGeneration,
  getTotalTeamCount 
} from '@/lib/referral';

// GET - Get detailed referral statistics
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const statsType = searchParams.get('type') || 'all';
    const generation = searchParams.get('generation');

    let responseData: any = {};

    switch (statsType) {
      case 'team':
        responseData.teamStats = await getDetailedTeamStats(user.id);
        break;
        
      case 'health':
        responseData.treeHealth = await getTreeHealthStats(user.id);
        break;
        
      case 'generation':
        if (generation) {
          const genNumber = parseInt(generation);
          if (genNumber > 0 && genNumber <= 10) {
            responseData.generationUsers = await getUsersByGeneration(user.id, genNumber);
          } else {
            return NextResponse.json(
              { success: false, error: 'Generation must be between 1 and 10' },
              { status: 400 }
            );
          }
        } else {
          return NextResponse.json(
            { success: false, error: 'Generation parameter is required' },
            { status: 400 }
          );
        }
        break;
        
      case 'counts':
        responseData.teamCounts = await getTotalTeamCount(user.id);
        break;
        
      case 'all':
      default:
        responseData.teamStats = await getDetailedTeamStats(user.id);
        responseData.treeHealth = await getTreeHealthStats(user.id);
        responseData.teamCounts = await getTotalTeamCount(user.id);
        break;
    }

    return NextResponse.json({
      success: true,
      data: responseData,
    });

  } catch (error: any) {
    console.error('Referral stats fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch referral statistics' },
      { status: 500 }
    );
  }
}
