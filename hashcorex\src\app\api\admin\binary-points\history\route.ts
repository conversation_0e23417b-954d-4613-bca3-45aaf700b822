import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Fetch binary matching history
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build where clause
    const whereClause: any = {
      type: 'BINARY_BONUS',
      status: 'COMPLETED',
    };

    if (userId) {
      whereClause.userId = userId;
    }

    // Fetch binary matching history from transactions
    const matchHistory = await prisma.transaction.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
    });

    // Get total count for pagination
    const totalCount = await prisma.transaction.count({
      where: whereClause,
    });

    // Transform data to include additional binary matching details
    const transformedHistory = await Promise.all(
      matchHistory.map(async (transaction) => {
        // Try to get additional details from system logs
        const logEntry = await prisma.systemLog.findFirst({
          where: {
            action: {
              in: ['BINARY_MATCHING_PROCESSED', 'MANUAL_BINARY_MATCHING_TRIGGERED'],
            },
            createdAt: {
              gte: new Date(transaction.createdAt.getTime() - 60000), // Within 1 minute
              lte: new Date(transaction.createdAt.getTime() + 60000),
            },
          },
          orderBy: { createdAt: 'desc' },
        });

        // Extract matching type from log or transaction description
        const isManual = transaction.description?.includes('Manual') || 
                        logEntry?.action === 'MANUAL_BINARY_MATCHING_TRIGGERED';

        return {
          id: transaction.id,
          userId: transaction.userId,
          user: transaction.user,
          matchedPoints: transaction.amount / 10, // Assuming $10 per point
          payout: transaction.amount,
          matchDate: transaction.createdAt,
          type: isManual ? 'MANUAL' : 'WEEKLY',
          description: transaction.description,
          // Note: We don't have before/after points in current schema
          // These would need to be added to track properly
          leftPointsBefore: 0,
          rightPointsBefore: 0,
          leftPointsAfter: 0,
          rightPointsAfter: 0,
        };
      })
    );

    return NextResponse.json({
      success: true,
      data: transformedHistory,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount,
      },
    });

  } catch (error: any) {
    console.error('Binary points history fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch binary points history' },
      { status: 500 }
    );
  }
}
