'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent, Button, Input } from '@/components/ui';
import { useAuth } from '@/hooks/useAuth';
import { Shield, Upload, CheckCircle, XCircle, Clock, AlertCircle, FileText, Camera } from 'lucide-react';
import { DocumentType, IDType, DocumentSide } from '@/types';

interface KYCDocument {
  id: string;
  documentType: DocumentType;
  idType?: IDType;
  documentSide?: DocumentSide;
  filePath: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  reviewedAt?: string;
  rejectionReason?: string;
  createdAt: string;
}

interface KYCSubmission {
  idType: IDType;
  documents: {
    [key: string]: File;
  };
  isComplete: boolean;
}

export const KYCPortal: React.FC = () => {
  const { user, refreshUser } = useAuth();
  const [documents, setDocuments] = useState<KYCDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState('');
  const [submitting, setSubmitting] = useState(false);

  // Enhanced KYC flow state
  const [selectedIdType, setSelectedIdType] = useState<IDType | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<{[key: string]: File}>({});
  const [step, setStep] = useState<'select' | 'upload' | 'review'>('select');

  useEffect(() => {
    fetchKYCDocuments();
  }, []);

  const fetchKYCDocuments = async () => {
    try {
      const response = await fetch('/api/kyc/documents', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setDocuments(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch KYC documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelect = (documentKey: string, file: File) => {
    // Validate file
    if (!file.type.startsWith('image/')) {
      setUploadError('Please upload an image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      setUploadError('File size must be less than 5MB');
      return;
    }

    setUploadError('');
    setUploadedFiles(prev => ({
      ...prev,
      [documentKey]: file
    }));
  };

  const getRequiredDocuments = (idType: IDType): string[] => {
    const docs = ['selfie'];

    if (idType === IDType.PASSPORT) {
      docs.push('id_front');
    } else {
      docs.push('id_front', 'id_back');
    }

    return docs;
  };

  const isSubmissionComplete = (): boolean => {
    if (!selectedIdType) return false;

    const requiredDocs = getRequiredDocuments(selectedIdType);
    return requiredDocs.every(doc => uploadedFiles[doc]);
  };

  const handleSubmitKYC = async () => {
    if (!selectedIdType || !isSubmissionComplete()) {
      setUploadError('Please complete all required uploads');
      return;
    }

    setSubmitting(true);
    setUploadError('');

    try {
      const requiredDocs = getRequiredDocuments(selectedIdType);

      for (const docKey of requiredDocs) {
        const file = uploadedFiles[docKey];
        if (!file) continue;

        const formData = new FormData();
        formData.append('file', file);
        formData.append('idType', selectedIdType);

        if (docKey === 'selfie') {
          formData.append('documentType', DocumentType.SELFIE);
        } else {
          formData.append('documentType', DocumentType.ID_DOCUMENT);
          formData.append('documentSide', docKey === 'id_front' ? DocumentSide.FRONT : DocumentSide.BACK);
        }

        const response = await fetch('/api/kyc/upload', {
          method: 'POST',
          credentials: 'include',
          body: formData,
        });

        const data = await response.json();
        if (!data.success) {
          throw new Error(data.error || 'Upload failed');
        }
      }

      // Refresh documents and user data
      await fetchKYCDocuments();
      await refreshUser();

      // Reset form
      setUploadedFiles({});
      setSelectedIdType(null);
      setStep('select');

    } catch (err: any) {
      setUploadError(err.message || 'Submission failed');
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircle className="h-5 w-5 text-eco-500" />;
      case 'REJECTED':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'PENDING':
        return <Clock className="h-5 w-5 text-solar-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-eco-100 text-eco-700';
      case 'REJECTED':
        return 'bg-red-100 text-red-700';
      case 'PENDING':
        return 'bg-solar-100 text-solar-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getDocumentByType = (type: DocumentType, side?: DocumentSide) => {
    return documents.find(doc =>
      doc.documentType === type &&
      (side ? doc.documentSide === side : true)
    );
  };

  const hasExistingKYC = () => {
    return documents.length > 0;
  };

  const getIdTypeLabel = (idType: IDType): string => {
    switch (idType) {
      case IDType.NATIONAL_ID:
        return 'National ID';
      case IDType.PASSPORT:
        return 'Passport';
      case IDType.DRIVING_LICENSE:
        return 'Driving License';
      default:
        return '';
    }
  };

  // ID Type Selection Component
  const IDTypeSelection: React.FC = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <FileText className="h-5 w-5 text-solar-500" />
          <span>Select ID Document Type</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-600 mb-6">
          Choose the type of government-issued ID you want to upload for verification.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Object.values(IDType).map((idType) => (
            <button
              key={idType}
              onClick={() => {
                setSelectedIdType(idType);
                setStep('upload');
              }}
              className="p-4 border-2 border-gray-200 rounded-lg hover:border-solar-500 hover:bg-solar-50 transition-colors text-left"
            >
              <div className="flex items-center space-x-3">
                <FileText className="h-6 w-6 text-solar-500" />
                <div>
                  <h3 className="font-medium text-dark-900">{getIdTypeLabel(idType)}</h3>
                  <p className="text-xs text-gray-500">
                    {idType === IDType.PASSPORT ? 'Front side only' : 'Front and back required'}
                  </p>
                </div>
              </div>
            </button>
          ))}
        </div>
      </CardContent>
    </Card>
  );

  // Document Upload Component
  const DocumentUpload: React.FC<{
    documentKey: string;
    title: string;
    description: string;
    required: boolean;
  }> = ({ documentKey, title, description, required }) => {
    const inputId = `file-${documentKey}`;
    const hasFile = uploadedFiles[documentKey];

    return (
      <div className="border border-gray-200 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-dark-900">
              {title} {required && <span className="text-red-500">*</span>}
            </h3>
            <p className="text-sm text-gray-600">{description}</p>
          </div>
          {hasFile && (
            <CheckCircle className="h-5 w-5 text-eco-500" />
          )}
        </div>

        {hasFile ? (
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <img
                src={URL.createObjectURL(hasFile)}
                alt={title}
                className="w-16 h-16 object-cover rounded-lg"
              />
              <div className="flex-1">
                <p className="text-sm font-medium text-dark-900">{hasFile.name}</p>
                <p className="text-xs text-gray-500">
                  {(hasFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setUploadedFiles(prev => {
                    const newFiles = { ...prev };
                    delete newFiles[documentKey];
                    return newFiles;
                  });
                }}
              >
                Remove
              </Button>
            </div>
          </div>
        ) : (
          <div>
            <input
              id={inputId}
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  handleFileSelect(documentKey, file);
                }
              }}
              className="hidden"
            />
            <label
              htmlFor={inputId}
              className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
            >
              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                <Upload className="h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500">
                  <span className="font-semibold">Click to upload</span> or drag and drop
                </p>
                <p className="text-xs text-gray-500">PNG, JPG up to 5MB</p>
              </div>
            </label>
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-solar-500" />
            <span>KYC Verification</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-32 bg-gray-200 rounded-lg"></div>
            <div className="h-32 bg-gray-200 rounded-lg"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* KYC Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-solar-500" />
            <span>KYC Verification Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            {getStatusIcon(user?.kycStatus || 'PENDING')}
            <div>
              <p className="text-lg font-semibold text-dark-900">
                Status: <span className={`${
                  user?.kycStatus === 'APPROVED' ? 'text-eco-600' :
                  user?.kycStatus === 'REJECTED' ? 'text-red-600' :
                  'text-solar-600'
                }`}>
                  {user?.kycStatus || 'PENDING'}
                </span>
              </p>
              <p className="text-sm text-gray-600">
                {user?.kycStatus === 'APPROVED' && 'Your identity has been verified. You can now make withdrawals.'}
                {user?.kycStatus === 'PENDING' && 'Your documents are being reviewed. This usually takes 1-3 business days.'}
                {user?.kycStatus === 'REJECTED' && 'Your verification was rejected. Please re-upload your documents.'}
              </p>
            </div>
          </div>

          {user?.kycStatus !== 'APPROVED' && (
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Why do we need KYC verification?</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Comply with international financial regulations</li>
                <li>• Protect your account from unauthorized access</li>
                <li>• Enable secure withdrawals to your wallet</li>
                <li>• Prevent fraud and money laundering</li>
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced KYC Flow */}
      {user?.kycStatus !== 'APPROVED' && (
        <>
          {/* Show existing documents if any */}
          {hasExistingKYC() && (
            <Card>
              <CardHeader>
                <CardTitle>Current Documents</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {documents.map((doc) => (
                    <div key={doc.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <img
                        src={doc.filePath}
                        alt={`${doc.documentType} document`}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-dark-900">
                          {doc.documentType === DocumentType.SELFIE ? 'Selfie Photo' :
                           `${getIdTypeLabel(doc.idType!)} - ${doc.documentSide}`}
                        </p>
                        <div className="flex items-center space-x-2 mt-1">
                          {getStatusIcon(doc.status)}
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(doc.status)}`}>
                            {doc.status}
                          </span>
                        </div>
                        {doc.rejectionReason && (
                          <p className="text-xs text-red-600 mt-1">
                            Rejection reason: {doc.rejectionReason}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* New KYC Submission Flow */}
          {(user?.kycStatus === 'REJECTED' || !hasExistingKYC()) && (
            <>
              {step === 'select' && <IDTypeSelection />}

              {step === 'upload' && selectedIdType && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>Upload {getIdTypeLabel(selectedIdType)} Documents</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setStep('select');
                          setSelectedIdType(null);
                          setUploadedFiles({});
                        }}
                      >
                        Change ID Type
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {uploadError && (
                      <div className="mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
                        {uploadError}
                      </div>
                    )}

                    <div className="space-y-6">
                      {/* ID Document Front */}
                      <DocumentUpload
                        documentKey="id_front"
                        title={`${getIdTypeLabel(selectedIdType)} - Front`}
                        description="Upload a clear photo of the front side of your ID"
                        required={true}
                      />

                      {/* ID Document Back (only for non-passport) */}
                      {selectedIdType !== IDType.PASSPORT && (
                        <DocumentUpload
                          documentKey="id_back"
                          title={`${getIdTypeLabel(selectedIdType)} - Back`}
                          description="Upload a clear photo of the back side of your ID"
                          required={true}
                        />
                      )}

                      {/* Selfie */}
                      <DocumentUpload
                        documentKey="selfie"
                        title="Selfie with ID"
                        description="Take a selfie holding your ID document next to your face"
                        required={true}
                      />
                    </div>

                    {/* Submit Button */}
                    <div className="mt-6 flex justify-end space-x-3">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setStep('select');
                          setSelectedIdType(null);
                          setUploadedFiles({});
                        }}
                        disabled={submitting}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleSubmitKYC}
                        disabled={!isSubmissionComplete() || submitting}
                        loading={submitting}
                      >
                        Submit KYC Documents
                      </Button>
                    </div>

                    {/* Requirements */}
                    <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <h4 className="text-sm font-medium text-yellow-900 mb-2">Document Requirements:</h4>
                      <ul className="text-sm text-yellow-700 space-y-1">
                        <li>• Documents must be clear and readable</li>
                        <li>• All four corners of the ID must be visible</li>
                        <li>• No blurred, cropped, or edited images</li>
                        <li>• Selfie must clearly show your face and the ID</li>
                        <li>• File formats: JPG, PNG (max 5MB each)</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
};
