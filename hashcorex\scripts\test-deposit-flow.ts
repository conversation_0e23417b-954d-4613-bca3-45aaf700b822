import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000';

// Test user credentials (you'll need to create a test user first)
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

// Test transaction ID (use a real Tron testnet transaction)
const TEST_TRANSACTION_ID = '30a36a84f7d77e822f92ab36bb53d98835373a2e638601d1e321224b4b8c1114';

async function testDepositFlow() {
  console.log('🧪 Testing Complete Automated Deposit Flow...\n');

  try {
    // Step 1: Initialize services
    console.log('1. Initializing background services...');
    const initResponse = await fetch(`${BASE_URL}/api/init`);
    const initData = await initResponse.json();
    console.log('Init Response:', initData);

    // Step 2: Login (you'll need to implement this based on your auth system)
    console.log('\n2. Testing deposit verification API directly...');
    
    // Step 3: Test deposit verification with a test transaction
    console.log('\n3. Testing deposit verification...');
    const depositResponse = await fetch(`${BASE_URL}/api/wallet/deposit/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // You'll need to add proper authentication headers here
      },
      body: JSON.stringify({
        transactionId: TEST_TRANSACTION_ID
      })
    });

    const depositData = await depositResponse.json();
    console.log('Deposit Response:', depositData);

    if (depositData.success) {
      console.log('✅ Deposit submitted successfully');
      console.log('Status:', depositData.data.status);
      console.log('Message:', depositData.message);
      
      // Step 4: Monitor status changes
      console.log('\n4. Monitoring deposit status...');
      await monitorDepositStatus(TEST_TRANSACTION_ID);
    } else {
      console.log('❌ Deposit submission failed:', depositData.error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

async function monitorDepositStatus(transactionId: string) {
  console.log(`Monitoring transaction: ${transactionId}`);
  
  let attempts = 0;
  const maxAttempts = 12; // Monitor for 2 minutes (10 seconds * 12)
  
  while (attempts < maxAttempts) {
    try {
      // Check deposit status (you'll need to implement a status check endpoint)
      console.log(`\nAttempt ${attempts + 1}/${maxAttempts} - Checking status...`);
      
      // For now, just wait and show that we're monitoring
      await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
      
      attempts++;
      
      // In a real implementation, you would:
      // 1. Call an API to get the current deposit status
      // 2. Check if status has changed
      // 3. Break the loop when status is final (COMPLETED, FAILED, etc.)
      
    } catch (error) {
      console.error('Error checking status:', error);
      break;
    }
  }
  
  console.log('\n✅ Monitoring completed');
}

// Test the deposit verification service directly
async function testServiceDirectly() {
  console.log('🔧 Testing Deposit Verification Service Directly...\n');
  
  try {
    const { depositVerificationService } = await import('../src/lib/depositVerificationService');
    
    // Start the service
    await depositVerificationService.start();
    console.log('✅ Service started');
    
    // Add a test transaction for verification
    const testAddress = 'TBtMPmTkz4f8xDn2E6mP1wvGtWnzzRwQeK';
    await depositVerificationService.addTransactionForVerification(TEST_TRANSACTION_ID, testAddress);
    console.log('✅ Test transaction added for verification');
    
    // Monitor service status
    for (let i = 0; i < 6; i++) {
      await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
      const status = depositVerificationService.getStatus();
      console.log(`Status check ${i + 1}:`, status);
    }
    
  } catch (error) {
    console.error('❌ Direct service test failed:', error);
  }
}

// Run tests
async function runAllTests() {
  console.log('🚀 Starting Automated Deposit System Tests\n');
  
  // Test 1: Service functionality
  await testServiceDirectly();
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 2: API flow (commented out as it requires authentication)
  // await testDepositFlow();
  
  console.log('\n🎉 All tests completed!');
  console.log('\nTo test the complete flow:');
  console.log('1. Open http://localhost:3000 in your browser');
  console.log('2. Login to your account');
  console.log('3. Go to Wallet > Deposit');
  console.log('4. Submit a test transaction ID');
  console.log('5. Watch the real-time status updates');
}

runAllTests()
  .then(() => {
    console.log('\n✅ Test suite completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test suite failed:', error);
    process.exit(1);
  });
