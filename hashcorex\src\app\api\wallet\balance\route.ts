import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { transactionDb, walletBalanceDb, depositTransactionDb } from '@/lib/database';

// GET - Fetch user's wallet balance
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get wallet balance from dedicated wallet balance table
    const walletBalance = await walletBalanceDb.findByUserId(user.id);

    // Get recent transactions for transaction history
    const transactions = await transactionDb.findByUserId(user.id, { limit: 10 });

    // Get recent deposits for deposit history
    const recentDeposits = await depositTransactionDb.findByUserId(user.id, { limit: 5 });

    // Calculate legacy balance from transactions (for verification/backup)
    let legacyBalance = 0;
    const allTransactions = await transactionDb.findByUserId(user.id);

    for (const transaction of allTransactions) {
      if (transaction.status === 'COMPLETED') {
        switch (transaction.type) {
          case 'MINING_EARNINGS':
          case 'DIRECT_REFERRAL':
          case 'BINARY_BONUS':
          case 'DEPOSIT':
            legacyBalance += transaction.amount;
            break;
          case 'WITHDRAWAL':
          case 'PURCHASE':
            legacyBalance -= transaction.amount;
            break;
        }
      }
    }

    // Get pending earnings
    const pendingEarnings = transactions
      .filter(t => t.status === 'PENDING' && (
        t.type === 'MINING_EARNINGS' || 
        t.type === 'DIRECT_REFERRAL' || 
        t.type === 'BINARY_BONUS'
      ))
      .reduce((sum, t) => sum + t.amount, 0);

    // Get recent transactions (last 20)
    const recentTransactions = transactions
      .slice(0, 20)
      .map(t => ({
        id: t.id,
        type: t.type,
        amount: t.amount,
        description: t.description,
        status: t.status,
        createdAt: t.createdAt,
      }));

    return NextResponse.json({
      success: true,
      data: {
        balance: Math.max(0, walletBalance.availableBalance), // Primary balance from wallet table
        availableBalance: walletBalance.availableBalance,
        pendingBalance: walletBalance.pendingBalance,
        totalDeposits: walletBalance.totalDeposits,
        totalWithdrawals: walletBalance.totalWithdrawals,
        totalEarnings: walletBalance.totalEarnings,
        lastUpdated: walletBalance.lastUpdated,

        // Legacy balance for verification (can be removed in production)
        legacyBalance: Math.max(0, legacyBalance),
        balanceMatch: Math.abs(walletBalance.availableBalance - legacyBalance) < 0.01,

        // Existing data
        pendingEarnings,
        recentTransactions,

        // Recent deposits
        recentDeposits: recentDeposits.map(deposit => ({
          id: deposit.id,
          transactionId: deposit.transactionId,
          amount: deposit.usdtAmount,
          status: deposit.status,
          createdAt: deposit.createdAt,
          processedAt: deposit.processedAt,
        })),

        // Summary stats
        stats: {
          totalTransactions: allTransactions.length,
          completedDeposits: recentDeposits.filter(d => d.status === 'COMPLETED' || d.status === 'CONFIRMED').length,
          pendingDeposits: recentDeposits.filter(d => d.status === 'PENDING' || d.status === 'PENDING_VERIFICATION').length,
        },
      },
    });

  } catch (error: any) {
    console.error('Wallet balance fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch wallet balance' },
      { status: 500 }
    );
  }
}
