import { NextRequest, NextResponse } from 'next/server';
import { calculateDailyROI, expireOldMiningUnits } from '@/lib/mining';
import { systemLogDb } from '@/lib/database';

// This endpoint should be called daily by a cron service
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (cron service)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'default-secret';
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log('Starting daily ROI calculation cron job...');

    // First, expire any old mining units
    const expiredCount = await expireOldMiningUnits();
    console.log(`Expired ${expiredCount} old mining units`);

    // Calculate daily ROI for all active units
    const roiResults = await calculateDailyROI();
    console.log(`Processed ${roiResults.length} mining units for daily ROI`);

    const totalEarnings = roiResults.reduce((sum, result) => sum + result.earnings, 0);
    const expiredUnits = roiResults.filter(result => result.expired).length;

    // Log the cron job execution
    await systemLogDb.create({
      action: 'DAILY_ROI_CRON_EXECUTED',
      details: {
        unitsProcessed: roiResults.length,
        totalEarnings,
        expiredUnits,
        oldUnitsExpired: expiredCount,
        executionTime: new Date().toISOString(),
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Daily ROI calculation completed',
      data: {
        unitsProcessed: roiResults.length,
        totalEarnings,
        expiredUnits,
        oldUnitsExpired: expiredCount,
      },
    });

  } catch (error: any) {
    console.error('Daily ROI cron job error:', error);

    // Log the error
    await systemLogDb.create({
      action: 'DAILY_ROI_CRON_ERROR',
      details: {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      },
    });

    return NextResponse.json(
      { success: false, error: 'Daily ROI calculation failed' },
      { status: 500 }
    );
  }
}
