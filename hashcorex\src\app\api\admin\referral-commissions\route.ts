import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Fetch referral commission data
export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get('dateRange') || '30d';
    const type = searchParams.get('type') || 'all';
    const status = searchParams.get('status') || 'all';
    const limit = parseInt(searchParams.get('limit') || '100');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Calculate date filter
    let dateFilter: any = {};
    if (dateRange !== 'all') {
      const days = parseInt(dateRange.replace('d', ''));
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      dateFilter = { gte: startDate };
    }

    // Build where clause
    const whereClause: any = {
      type: 'DIRECT_REFERRAL',
    };

    if (dateRange !== 'all') {
      whereClause.createdAt = dateFilter;
    }

    if (status !== 'all') {
      whereClause.status = status;
    }

    // Fetch referral commission transactions
    const commissions = await prisma.transaction.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
    });

    // Transform data to include from/to user information
    const transformedCommissions = await Promise.all(
      commissions.map(async (commission) => {
        // Extract purchaser information from the reference field
        let fromUser = null;

        // Method 1: Check if the reference field contains purchaser information
        if (commission.reference && commission.reference.startsWith('from_user:')) {
          const purchaserId = commission.reference.replace('from_user:', '');

          // Fetch the purchaser user details
          fromUser = await prisma.user.findUnique({
            where: { id: purchaserId },
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          });
        }

        // Method 2: If no fromUser found from reference, find direct referrals of the commission recipient
        // who made a purchase that matches the commission amount (10% rule)
        if (!fromUser && commission.user) {
          const directReferrals = await prisma.user.findMany({
            where: {
              referrerId: commission.userId, // Users referred by the commission recipient
            },
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              miningUnits: {
                select: {
                  id: true,
                  investmentAmount: true,
                  createdAt: true,
                },
                orderBy: { createdAt: 'desc' },
              },
            },
          });

          // Find the user whose purchase amount matches the commission (10% rule)
          // and the purchase was made around the same time as the commission
          for (const user of directReferrals) {
            for (const miningUnit of user.miningUnits) {
              const expectedCommission = miningUnit.investmentAmount * 0.1;
              const timeDifference = Math.abs(
                new Date(commission.createdAt).getTime() - new Date(miningUnit.createdAt).getTime()
              );

              // Check if commission amount matches (within 0.01 tolerance) and timing is close (within 30 minutes)
              if (
                Math.abs(expectedCommission - commission.amount) < 0.01 &&
                timeDifference <= 30 * 60 * 1000 // 30 minutes
              ) {
                fromUser = {
                  id: user.id,
                  email: user.email,
                  firstName: user.firstName,
                  lastName: user.lastName,
                };
                break;
              }
            }
            if (fromUser) break;
          }
        }

        // Calculate commission rate and original amount
        const commissionRate = 10; // Default 10% - should be configurable
        const originalAmount = commission.amount / (commissionRate / 100);

        return {
          id: commission.id,
          fromUserId: fromUser?.id || 'unknown',
          toUserId: commission.userId,
          fromUser: fromUser || {
            id: 'unknown',
            email: 'Unknown',
            firstName: 'Unknown',
            lastName: 'User',
          },
          toUser: commission.user,
          amount: commission.amount,
          commissionRate,
          originalAmount,
          type: 'DIRECT_REFERRAL',
          description: commission.description || '',
          createdAt: commission.createdAt,
          status: commission.status,
        };
      })
    );

    // Get total count for pagination
    const totalCount = await prisma.transaction.count({
      where: whereClause,
    });

    return NextResponse.json({
      success: true,
      data: transformedCommissions,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount,
      },
    });

  } catch (error: any) {
    console.error('Referral commissions fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch referral commissions' },
      { status: 500 }
    );
  }
}
