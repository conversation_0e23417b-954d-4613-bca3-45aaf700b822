import { useState, useEffect } from 'react';

/**
 * Hook to prevent hydration mismatches by ensuring content only renders on client
 * @returns boolean indicating if component is mounted on client
 */
export function useClientOnly(): boolean {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook for client-side only time calculations to prevent hydration mismatches
 * @param calculateTime Function that calculates time-based values
 * @param interval Update interval in milliseconds (default: 1000)
 * @returns The calculated time value or null if not on client
 */
export function useClientTime<T>(
  calculateTime: () => T,
  interval: number = 1000
): T | null {
  const [timeValue, setTimeValue] = useState<T | null>(null);
  const isClient = useClientOnly();

  useEffect(() => {
    if (!isClient) return;

    // Initial calculation
    setTimeValue(calculateTime());

    // Set up interval for updates
    const intervalId = setInterval(() => {
      setTimeValue(calculateTime());
    }, interval);

    return () => clearInterval(intervalId);
  }, [isClient, calculateTime, interval]);

  return timeValue;
}
