import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';

// GET - Fetch user's notification settings
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // For now, return default notification settings
    // In a real implementation, these would be stored in the database
    const defaultSettings = {
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      marketingEmails: false,
    };

    return NextResponse.json({
      success: true,
      data: defaultSettings,
    });

  } catch (error: any) {
    console.error('Notification settings fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch notification settings' },
      { status: 500 }
    );
  }
}

// PUT - Update user's notification settings
export async function PUT(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { emailNotifications, pushNotifications, smsNotifications, marketingEmails } = body;

    // For now, just return success
    // In a real implementation, these would be saved to the database
    const updatedSettings = {
      emailNotifications: emailNotifications ?? true,
      pushNotifications: pushNotifications ?? true,
      smsNotifications: smsNotifications ?? false,
      marketingEmails: marketingEmails ?? false,
    };

    return NextResponse.json({
      success: true,
      data: updatedSettings,
      message: 'Notification settings updated successfully',
    });

  } catch (error: any) {
    console.error('Notification settings update error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to update notification settings' },
      { status: 500 }
    );
  }
}
