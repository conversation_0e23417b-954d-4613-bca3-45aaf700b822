import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function initBinarySettings() {
  try {
    console.log('🔧 Initializing binary settings...');

    // Check current binary settings
    const currentSettings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: ['MAX_BINARY_POINTS_PER_SIDE', 'BINARY_POINT_VALUE', 'BINARY_MATCHING_ENABLED', 'BINARY_MATCHING_SCHEDULE']
        }
      }
    });

    console.log('\n📋 Current binary settings:');
    currentSettings.forEach(s => console.log(`  ${s.key}: ${s.value}`));

    // Initialize missing settings
    const requiredSettings = [
      { key: 'MAX_BINARY_POINTS_PER_SIDE', value: '10' },
      { key: 'BINARY_POINT_VALUE', value: '10' },
      { key: 'BINARY_MATCHING_ENABLED', value: 'true' },
      { key: 'BINARY_MATCHING_SCHEDULE', value: 'Weekly at 15:00 UTC' }
    ];

    console.log('\n🔄 Ensuring all binary settings exist...');
    for (const setting of requiredSettings) {
      const result = await prisma.adminSettings.upsert({
        where: { key: setting.key },
        update: {}, // Don't overwrite existing values
        create: setting
      });
      console.log(`✅ ${setting.key}: ${result.value}`);
    }

    // Test the admin settings API format
    console.log('\n🧪 Testing admin settings API format...');
    const allSettings = await prisma.adminSettings.findMany();
    const settingsObject: any = {};
    
    allSettings.forEach(setting => {
      try {
        settingsObject[setting.key] = JSON.parse(setting.value);
      } catch {
        settingsObject[setting.key] = setting.value;
      }
    });

    // Map database keys to frontend keys
    if (settingsObject['MAX_BINARY_POINTS_PER_SIDE']) {
      settingsObject.maxBinaryPointsPerSide = parseFloat(settingsObject['MAX_BINARY_POINTS_PER_SIDE']);
    }
    if (settingsObject['BINARY_POINT_VALUE']) {
      settingsObject.binaryPointValue = parseFloat(settingsObject['BINARY_POINT_VALUE']);
    }
    if (settingsObject['BINARY_MATCHING_ENABLED']) {
      settingsObject.binaryMatchingEnabled = settingsObject['BINARY_MATCHING_ENABLED'] === 'true';
    }
    if (settingsObject['BINARY_MATCHING_SCHEDULE']) {
      settingsObject.binaryMatchingSchedule = settingsObject['BINARY_MATCHING_SCHEDULE'];
    }

    console.log('\n📡 Frontend format:');
    console.log(`  maxBinaryPointsPerSide: ${settingsObject.maxBinaryPointsPerSide} (${typeof settingsObject.maxBinaryPointsPerSide})`);
    console.log(`  binaryPointValue: ${settingsObject.binaryPointValue} (${typeof settingsObject.binaryPointValue})`);
    console.log(`  binaryMatchingEnabled: ${settingsObject.binaryMatchingEnabled} (${typeof settingsObject.binaryMatchingEnabled})`);
    console.log(`  binaryMatchingSchedule: ${settingsObject.binaryMatchingSchedule} (${typeof settingsObject.binaryMatchingSchedule})`);

    console.log('\n✨ Binary settings initialization completed!');

  } catch (error) {
    console.error('❌ Error initializing binary settings:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
initBinarySettings();
