import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3000';

async function testWalletBalanceAPI() {
  console.log('🧪 Testing Wallet Balance API...\n');

  try {
    // Test wallet balance API
    console.log('1. Testing wallet balance API...');
    const balanceResponse = await fetch(`${BASE_URL}/api/wallet/balance`, {
      credentials: 'include',
    });

    console.log('Balance API Status:', balanceResponse.status);
    
    if (balanceResponse.status === 401) {
      console.log('❌ Not authenticated - need to login first');
      return;
    }

    const balanceData = await balanceResponse.json();
    console.log('Balance API Response:', JSON.stringify(balanceData, null, 2));

    // Test deposit info API
    console.log('\n2. Testing deposit info API...');
    const depositInfoResponse = await fetch(`${BASE_URL}/api/wallet/deposit/info`, {
      credentials: 'include',
    });

    console.log('Deposit Info API Status:', depositInfoResponse.status);
    const depositInfoData = await depositInfoResponse.json();
    console.log('Deposit Info Response:', JSON.stringify(depositInfoData, null, 2));

    console.log('\n✅ API tests completed!');
    console.log('\nIf the APIs are returning correct data but the UI shows old values,');
    console.log('the issue is likely browser caching or the UI not refreshing properly.');
    console.log('\nTry:');
    console.log('1. Hard refresh the browser (Ctrl+F5)');
    console.log('2. Clear browser cache');
    console.log('3. Check if the UI polling is working correctly');

  } catch (error) {
    console.error('❌ API test failed:', error);
  }
}

testWalletBalanceAPI()
  .then(() => {
    console.log('\n🎉 Test completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  });
