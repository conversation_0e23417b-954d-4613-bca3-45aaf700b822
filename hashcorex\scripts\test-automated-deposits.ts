import { depositVerificationService } from '../src/lib/depositVerificationService';
import { depositTransactionDb, adminSettingsDb } from '../src/lib/database';

async function testAutomatedDepositSystem() {
  console.log('🧪 Testing Automated Deposit Verification System...\n');

  try {
    // Test 1: Check service status
    console.log('1. Checking service status...');
    const status = depositVerificationService.getStatus();
    console.log('Service Status:', status);

    // Test 2: Start the service
    console.log('\n2. Starting verification service...');
    await depositVerificationService.start();
    console.log('✅ Service started successfully');

    // Test 3: Check admin settings
    console.log('\n3. Checking admin settings...');
    const minConfirmations = await adminSettingsDb.get('minConfirmations');
    const minDepositAmount = await adminSettingsDb.get('minDepositAmount');
    const maxDepositAmount = await adminSettingsDb.get('maxDepositAmount');
    const depositAddress = await adminSettingsDb.get('depositAddress');
    
    console.log('Settings:');
    console.log(`  Min Confirmations: ${minConfirmations || 'Not set'}`);
    console.log(`  Min Deposit: ${minDepositAmount || 'Not set'} USDT`);
    console.log(`  Max Deposit: ${maxDepositAmount || 'Not set'} USDT`);
    console.log(`  Deposit Address: ${depositAddress || 'Not set'}`);

    // Test 4: Check for pending deposits
    console.log('\n4. Checking for pending deposits...');
    const pendingVerification = await depositTransactionDb.getPendingVerificationDeposits();
    const waitingConfirmations = await depositTransactionDb.getWaitingForConfirmationsDeposits();
    
    console.log(`Pending Verification: ${pendingVerification.length}`);
    console.log(`Waiting for Confirmations: ${waitingConfirmations.length}`);

    if (pendingVerification.length > 0) {
      console.log('\nPending Verification Deposits:');
      pendingVerification.forEach((deposit, index) => {
        console.log(`  ${index + 1}. TX: ${deposit.transactionId.slice(0, 16)}... (${deposit.status})`);
      });
    }

    if (waitingConfirmations.length > 0) {
      console.log('\nWaiting for Confirmations:');
      waitingConfirmations.forEach((deposit, index) => {
        console.log(`  ${index + 1}. TX: ${deposit.transactionId.slice(0, 16)}... (${deposit.confirmations} confirmations)`);
      });
    }

    // Test 5: Service status after initialization
    console.log('\n5. Final service status...');
    const finalStatus = depositVerificationService.getStatus();
    console.log('Final Status:', finalStatus);

    console.log('\n✅ Automated deposit system test completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Submit a test transaction through the UI');
    console.log('2. Monitor the logs for verification progress');
    console.log('3. Check deposit status updates in real-time');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run the test
testAutomatedDepositSystem()
  .then(() => {
    console.log('\n🎉 Test completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error);
    process.exit(1);
  });
