const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testTeamCountFixes() {
  try {
    console.log('🧪 Testing Team Count Fixes...\n');

    // Find <EMAIL> user
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        directReferralCount: true,
        totalLeftDownline: true,
        totalRightDownline: true,
        lastTreeUpdate: true,
        isActive: true
      }
    });

    if (!user) {
      console.log('❌ User <EMAIL> not found');
      return;
    }

    console.log('👤 User Info:', {
      email: user.email,
      name: `${user.firstName} ${user.lastName}`,
      directReferralCount: user.directReferralCount,
      cachedLeft: user.totalLeftDownline,
      cachedRight: user.totalRightDownline,
      cachedTotal: user.totalLeftDownline + user.totalRightDownline,
      lastUpdate: user.lastTreeUpdate
    });

    // Force cache refresh by clearing lastTreeUpdate
    await prisma.user.update({
      where: { id: user.id },
      data: { lastTreeUpdate: null }
    });

    console.log('\n🔄 Cache cleared, testing updated functions...\n');

    // Test the API endpoint
    console.log('📡 Testing API endpoint...');
    
    // Since we can't directly import the functions, let's test via a simple calculation
    // Get all direct referrals (sponsored users)
    const directReferrals = await prisma.user.findMany({
      where: { referrerId: user.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        isActive: true
      }
    });

    console.log(`📊 Direct Referrals: ${directReferrals.length}`);
    directReferrals.forEach((ref, index) => {
      console.log(`   ${index + 1}. ${ref.email} - ${ref.firstName} ${ref.lastName} (${ref.isActive ? 'Active' : 'Inactive'})`);
    });

    // Get all binary tree referrals
    const binaryReferrals = await prisma.referral.findMany({
      where: { referrerId: user.id },
      include: {
        referred: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            isActive: true
          }
        }
      }
    });

    console.log(`\n🌳 Binary Tree Direct Placements: ${binaryReferrals.length}`);
    binaryReferrals.forEach((ref, index) => {
      console.log(`   ${index + 1}. ${ref.referred.email} - ${ref.placementSide} side (${ref.referred.isActive ? 'Active' : 'Inactive'})`);
    });

    // Calculate all downline users recursively
    async function getAllDownlineRecursive(userId, visited = new Set()) {
      if (visited.has(userId)) return [];
      visited.add(userId);

      const directPlacements = await prisma.referral.findMany({
        where: { referrerId: userId },
        include: {
          referred: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              isActive: true
            }
          }
        }
      });

      let allDownline = [];
      for (const placement of directPlacements) {
        allDownline.push(placement.referred);
        const subDownline = await getAllDownlineRecursive(placement.referredId, visited);
        allDownline = allDownline.concat(subDownline);
      }

      return allDownline;
    }

    const allDownlineUsers = await getAllDownlineRecursive(user.id);
    const activeDownlineCount = allDownlineUsers.filter(u => u.isActive).length;

    console.log(`\n📈 Total Downline Users: ${allDownlineUsers.length}`);
    console.log(`✅ Active Downline Users: ${activeDownlineCount}`);

    // Calculate left and right side counts
    async function getDownlineForSide(userId, side) {
      const downlineUsers = [];
      const visited = new Set();
      
      // Get initial placements on the specified side
      const initialReferrals = await prisma.referral.findMany({
        where: {
          referrerId: userId,
          placementSide: side,
        },
        select: {
          referredId: true,
        },
      });
      
      const queue = initialReferrals.map(r => r.referredId);
      
      while (queue.length > 0) {
        const currentUserId = queue.shift();
        
        if (visited.has(currentUserId)) continue;
        visited.add(currentUserId);
        
        downlineUsers.push({ id: currentUserId });
        
        const referrals = await prisma.referral.findMany({
          where: {
            referrerId: currentUserId,
          },
          select: {
            referredId: true,
          },
        });
        
        for (const referral of referrals) {
          if (!visited.has(referral.referredId)) {
            queue.push(referral.referredId);
          }
        }
      }
      
      return downlineUsers;
    }

    const leftDownline = await getDownlineForSide(user.id, 'LEFT');
    const rightDownline = await getDownlineForSide(user.id, 'RIGHT');

    console.log(`\n🌿 Left Side Downline: ${leftDownline.length}`);
    console.log(`🌿 Right Side Downline: ${rightDownline.length}`);
    console.log(`🌳 Total Binary Tree Count: ${leftDownline.length + rightDownline.length}`);

    // Validation
    console.log('\n🔍 Validation Results:');
    
    const expectedTotalTeam = allDownlineUsers.length;
    const calculatedBinaryTotal = leftDownline.length + rightDownline.length;
    
    console.log(`   Expected Total Team: ${expectedTotalTeam}`);
    console.log(`   Calculated Binary Total: ${calculatedBinaryTotal}`);
    
    if (expectedTotalTeam === calculatedBinaryTotal) {
      console.log('   ✅ Team count calculation is CORRECT!');
    } else {
      console.log('   ❌ Team count calculation mismatch!');
    }

    if (activeDownlineCount === 7) {
      console.log('   ✅ Active member count matches expected (7)');
    } else {
      console.log(`   ❌ Active member count mismatch. Expected: 7, Got: ${activeDownlineCount}`);
    }

    console.log('\n🎉 Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testTeamCountFixes();
