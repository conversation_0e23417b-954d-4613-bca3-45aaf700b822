import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function testWalletAdjustment() {
  console.log('🧪 Testing wallet adjustment functionality...');

  try {
    // Create a test user if not exists
    const testEmail = '<EMAIL>';
    let testUser = await prisma.user.findUnique({
      where: { email: testEmail }
    });

    if (!testUser) {
      console.log('📝 Creating test user...');
      const hashedPassword = await bcrypt.hash('password123', 12);
      
      testUser = await prisma.user.create({
        data: {
          email: testEmail,
          password: hashedPassword,
          firstName: 'Test',
          lastName: 'User',
          referralId: 'TEST_USER_001',
          kycStatus: 'APPROVED',
          isActive: true,
        },
      });
      console.log(`✅ Test user created: ${testUser.email} (ID: ${testUser.id})`);
    } else {
      console.log(`✅ Test user exists: ${testUser.email} (ID: ${testUser.id})`);
    }

    // Check current wallet balance
    const currentWallet = await prisma.walletBalance.findUnique({
      where: { userId: testUser.id }
    });

    if (currentWallet) {
      console.log(`💰 Current wallet balance: ${currentWallet.availableBalance} USDT`);
    } else {
      console.log('💰 No wallet balance record found - will be created automatically');
    }

    // Get admin user
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (!adminUser) {
      console.log('❌ No admin user found. Please create an admin user first.');
      return;
    }

    console.log(`👤 Admin user: ${adminUser.email} (ID: ${adminUser.id})`);

    console.log('\n🎯 Test data ready!');
    console.log('📋 You can now test the wallet adjustment functionality:');
    console.log(`   - Test User ID: ${testUser.id}`);
    console.log(`   - Test User Email: ${testUser.email}`);
    console.log(`   - Admin User ID: ${adminUser.id}`);
    console.log(`   - Admin User Email: ${adminUser.email}`);
    
    console.log('\n🌐 Access the admin panel at: http://localhost:3000/admin');
    console.log('📊 Navigate to User Management to test wallet adjustments');

  } catch (error) {
    console.error('❌ Error setting up test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testWalletAdjustment();
