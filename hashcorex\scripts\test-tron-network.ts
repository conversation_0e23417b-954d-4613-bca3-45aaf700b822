import { PrismaClient } from '@prisma/client';
import { getCurrentNetworkConfig } from '../src/lib/trongrid';

const prisma = new PrismaClient();

async function testTronNetworkSwitching() {
  console.log('🧪 Testing Tron Network Switching Functionality...\n');

  try {
    // Test 1: Get current network configuration
    console.log('📡 Test 1: Getting current network configuration...');
    const currentConfig = await getCurrentNetworkConfig();
    console.log(`✅ Current Network: ${currentConfig.network.toUpperCase()}`);
    console.log(`   API URL: ${currentConfig.apiUrl}`);
    console.log(`   USDT Contract: ${currentConfig.usdtContract}\n`);

    // Test 2: Switch to testnet
    console.log('🔄 Test 2: Switching to Testnet...');
    await prisma.adminSettings.upsert({
      where: { key: 'tronNetwork' },
      update: { value: 'testnet' },
      create: { key: 'tronNetwork', value: 'testnet' },
    });

    const testnetConfig = await getCurrentNetworkConfig();
    console.log(`✅ Switched to: ${testnetConfig.network.toUpperCase()}`);
    console.log(`   API URL: ${testnetConfig.apiUrl}`);
    console.log(`   USDT Contract: ${testnetConfig.usdtContract}`);
    
    // Verify testnet values
    const expectedTestnetUrl = 'https://api.shasta.trongrid.io';
    const expectedTestnetContract = 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs';
    
    if (testnetConfig.apiUrl === expectedTestnetUrl && testnetConfig.usdtContract === expectedTestnetContract) {
      console.log('✅ Testnet configuration is correct\n');
    } else {
      console.log('❌ Testnet configuration mismatch\n');
    }

    // Test 3: Switch to mainnet
    console.log('🔄 Test 3: Switching to Mainnet...');
    await prisma.adminSettings.upsert({
      where: { key: 'tronNetwork' },
      update: { value: 'mainnet' },
      create: { key: 'tronNetwork', value: 'mainnet' },
    });

    const mainnetConfig = await getCurrentNetworkConfig();
    console.log(`✅ Switched to: ${mainnetConfig.network.toUpperCase()}`);
    console.log(`   API URL: ${mainnetConfig.apiUrl}`);
    console.log(`   USDT Contract: ${mainnetConfig.usdtContract}`);
    
    // Verify mainnet values
    const expectedMainnetUrl = 'https://api.trongrid.io';
    const expectedMainnetContract = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';
    
    if (mainnetConfig.apiUrl === expectedMainnetUrl && mainnetConfig.usdtContract === expectedMainnetContract) {
      console.log('✅ Mainnet configuration is correct\n');
    } else {
      console.log('❌ Mainnet configuration mismatch\n');
    }

    // Test 4: Switch back to testnet for safety
    console.log('🔄 Test 4: Switching back to Testnet for safety...');
    await prisma.adminSettings.upsert({
      where: { key: 'tronNetwork' },
      update: { value: 'testnet' },
      create: { key: 'tronNetwork', value: 'testnet' },
    });

    const finalConfig = await getCurrentNetworkConfig();
    console.log(`✅ Final Network: ${finalConfig.network.toUpperCase()}`);
    console.log(`   API URL: ${finalConfig.apiUrl}`);
    console.log(`   USDT Contract: ${finalConfig.usdtContract}\n`);

    // Test 5: Verify all settings are in database
    console.log('📊 Test 5: Verifying all Tron settings in database...');
    const allTronSettings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: ['tronNetwork', 'tronMainnetApiUrl', 'tronTestnetApiUrl', 'usdtMainnetContract', 'usdtTestnetContract']
        }
      }
    });

    console.log('Database settings:');
    allTronSettings.forEach(setting => {
      console.log(`   ${setting.key}: ${setting.value}`);
    });

    if (allTronSettings.length === 5) {
      console.log('✅ All Tron network settings are present in database\n');
    } else {
      console.log(`❌ Missing settings. Expected 5, found ${allTronSettings.length}\n`);
    }

    console.log('🎉 Network switching tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Network configuration retrieval works');
    console.log('   ✅ Testnet switching works correctly');
    console.log('   ✅ Mainnet switching works correctly');
    console.log('   ✅ All settings are stored in database');
    console.log('   ✅ Network switched back to testnet for safety');

    console.log('\n⚠️  Next Steps:');
    console.log('   1. Test the admin UI network switching');
    console.log('   2. Test deposit verification with both networks');
    console.log('   3. Verify network status indicators in user interface');
    console.log('   4. Test the deposit processing cron job');

  } catch (error) {
    console.error('❌ Error during network switching tests:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the tests
testTronNetworkSwitching();
