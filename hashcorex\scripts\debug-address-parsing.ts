import { fromHex } from 'tron-format-address';

function hexToTronAddress(hex: string): string {
  try {
    // Remove '0x' prefix if present
    let cleanHex = hex.startsWith('0x') ? hex.slice(2) : hex;
    
    // Ensure we have a 40-character hex string (20 bytes)
    if (cleanHex.length < 40) {
      cleanHex = '0'.repeat(40 - cleanHex.length) + cleanHex;
    }
    
    // Add the Tron address prefix (0x41 for mainnet/testnet)
    const addressHex = '41' + cleanHex;
    
    // Convert to base58
    return fromHex(addressHex);
  } catch (error) {
    console.error('Error converting hex to Tron address:', error);
    // Fallback to a recognizable format if conversion fails
    return `T${hex.slice(-30)}`;
  }
}

async function debugAddressParsing() {
  console.log('🔍 Debugging Address Parsing...\n');

  // From the transaction log data
  const topics = [
    'ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',
    '000000000000000000000000f3988b1511c040cf4a042f65a422e3e5685e403e',
    '00000000000000000000000015052d38f09e60a2372af5f64e32409acc6700ac'
  ];

  console.log('Raw Topics:');
  topics.forEach((topic, index) => {
    console.log(`  Topic[${index}]: ${topic}`);
  });

  // Parse addresses from topics
  const fromAddressHex = topics[1].slice(26); // Remove padding
  const toAddressHex = topics[2].slice(26); // Remove padding

  console.log('\nHex Addresses (after removing padding):');
  console.log(`  From Hex: ${fromAddressHex}`);
  console.log(`  To Hex: ${toAddressHex}`);

  // Convert to Tron addresses
  const fromAddress = hexToTronAddress(fromAddressHex);
  const toAddress = hexToTronAddress(toAddressHex);

  console.log('\nConverted Addresses:');
  console.log(`  From Address: ${fromAddress}`);
  console.log(`  To Address: ${toAddress}`);

  // Expected addresses from TronScan
  console.log('\nExpected Addresses (from TronScan):');
  console.log(`  From: TYBDwZs1sSbT2bFgBCr1efqTW62JHroDK7 (Import-29)`);
  console.log(`  To: TBtMPmTkz4f8xDn2E6mP1wvGtWnzZRwQeK (Import-32)`);

  // Let's try to reverse engineer the expected address
  console.log('\nTrying to find the correct hex for TBtMPmTkz4f8xDn2E6mP1wvGtWnzZRwQeK...');
  
  // Let's check if there's a different way to parse the address
  // Maybe the address is in the raw transaction data instead of logs
  
  console.log('\nChecking raw transaction data...');
  const rawData = '0a0226a022081dac41aafb4e6d0940d09e8e88fb325aae01081f12a9010a31747970652e676f6f676c65617069732e636f6d2f70726f746f636f6c2e54726967676572536d617274436f6e747261637412740a1541f3988b1511c040cf4a042f65a422e3e5685e403e12154142a1e39aefa49290f2b3f9ed688d7cecf86cd6e02244a9059cbb00000000000000000000000015052d38f09e60a2372af5f64e32409acc6700ac00000000000000000000000000000000000000000000000000000000000bebc20070f0c98a88fb329001c0f4a46b';
  
  // Look for the recipient address in raw data
  // TBtMPmTkz4f8xDn2E6mP1wvGtWnzZRwQeK should be somewhere in the raw transaction
  
  console.log('Raw transaction hex length:', rawData.length);
  
  // Let's try to find patterns that might be addresses
  const possibleAddresses = [];
  for (let i = 0; i < rawData.length - 40; i += 2) {
    const hexChunk = rawData.slice(i, i + 42); // 41 prefix + 40 hex = 42 chars
    if (hexChunk.startsWith('41')) {
      try {
        const address = fromHex(hexChunk);
        if (address.startsWith('T') && address.length > 30) {
          possibleAddresses.push({ hex: hexChunk, address });
        }
      } catch (e) {
        // Ignore conversion errors
      }
    }
  }
  
  console.log('\nPossible addresses found in raw data:');
  possibleAddresses.forEach((addr, index) => {
    console.log(`  ${index + 1}. ${addr.address} (hex: ${addr.hex})`);
  });
}

debugAddressParsing();
