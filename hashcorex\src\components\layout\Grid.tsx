'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface GridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: number;
}

const Grid: React.FC<GridProps> = ({ 
  children, 
  className, 
  cols = { default: 1, md: 2, lg: 3 },
  gap = 6
}) => {
  const getGridClasses = () => {
    const classes = ['grid'];
    
    // Default columns
    if (cols.default) {
      classes.push(`grid-cols-${cols.default}`);
    }
    
    // Responsive columns
    if (cols.sm) {
      classes.push(`sm:grid-cols-${cols.sm}`);
    }
    if (cols.md) {
      classes.push(`md:grid-cols-${cols.md}`);
    }
    if (cols.lg) {
      classes.push(`lg:grid-cols-${cols.lg}`);
    }
    if (cols.xl) {
      classes.push(`xl:grid-cols-${cols.xl}`);
    }
    
    // Gap
    classes.push(`gap-${gap}`);
    
    return classes.join(' ');
  };

  return (
    <div className={cn(getGridClasses(), className)}>
      {children}
    </div>
  );
};

interface GridItemProps {
  children: React.ReactNode;
  className?: string;
  span?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}

const GridItem: React.FC<GridItemProps> = ({ 
  children, 
  className, 
  span 
}) => {
  const getSpanClasses = () => {
    if (!span) return '';
    
    const classes = [];
    
    if (span.default) {
      classes.push(`col-span-${span.default}`);
    }
    if (span.sm) {
      classes.push(`sm:col-span-${span.sm}`);
    }
    if (span.md) {
      classes.push(`md:col-span-${span.md}`);
    }
    if (span.lg) {
      classes.push(`lg:col-span-${span.lg}`);
    }
    if (span.xl) {
      classes.push(`xl:col-span-${span.xl}`);
    }
    
    return classes.join(' ');
  };

  return (
    <div className={cn(getSpanClasses(), className)}>
      {children}
    </div>
  );
};

export { Grid, GridItem };
