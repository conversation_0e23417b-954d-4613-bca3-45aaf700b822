import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { systemLogDb } from '@/lib/database';

// POST - Submit complete KYC application
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user already has a pending or approved KYC
    if (user.kycStatus === 'APPROVED') {
      return NextResponse.json(
        { success: false, error: 'KYC already approved' },
        { status: 400 }
      );
    }

    if (user.kycStatus === 'PENDING') {
      return NextResponse.json(
        { success: false, error: 'KYC submission already pending review' },
        { status: 400 }
      );
    }

    // Get user's uploaded documents
    const userDocuments = await prisma.kYCDocument.findMany({
      where: { userId: user.id },
    });

    if (userDocuments.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No documents uploaded' },
        { status: 400 }
      );
    }

    // Validate that all required documents are uploaded
    const hasSelfie = userDocuments.some(doc => doc.documentType === 'SELFIE');
    
    if (!hasSelfie) {
      return NextResponse.json(
        { success: false, error: 'Selfie document is required' },
        { status: 400 }
      );
    }

    // Check ID documents
    const idDocuments = userDocuments.filter(doc => doc.documentType === 'ID_DOCUMENT');
    
    if (idDocuments.length === 0) {
      return NextResponse.json(
        { success: false, error: 'ID document is required' },
        { status: 400 }
      );
    }

    // Validate ID document completeness based on type
    const idType = idDocuments[0].idType;
    let hasCompleteIdDocuments = false;

    if (idType === 'PASSPORT') {
      // Passport only needs front side
      hasCompleteIdDocuments = idDocuments.some(doc => doc.documentSide === 'FRONT');
    } else {
      // National ID and Driving License need both front and back
      const hasFront = idDocuments.some(doc => doc.documentSide === 'FRONT');
      const hasBack = idDocuments.some(doc => doc.documentSide === 'BACK');
      hasCompleteIdDocuments = hasFront && hasBack;
    }

    if (!hasCompleteIdDocuments) {
      return NextResponse.json(
        { 
          success: false, 
          error: idType === 'PASSPORT' 
            ? 'Passport front side is required' 
            : 'Both front and back sides of ID are required' 
        },
        { status: 400 }
      );
    }

    // Update user KYC status to PENDING
    await prisma.user.update({
      where: { id: user.id },
      data: { kycStatus: 'PENDING' },
    });

    // Update all user's documents to PENDING status
    await prisma.kYCDocument.updateMany({
      where: { userId: user.id },
      data: { status: 'PENDING' },
    });

    // Log the submission
    await systemLogDb.create({
      action: 'KYC_SUBMITTED',
      userId: user.id,
      details: {
        documentsCount: userDocuments.length,
        idType,
        hasCompleteIdDocuments,
        hasSelfie,
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: 'KYC submitted successfully for review',
      data: {
        status: 'PENDING',
        documentsCount: userDocuments.length,
        submittedAt: new Date().toISOString(),
      },
    });

  } catch (error: any) {
    console.error('KYC submission error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to submit KYC' },
      { status: 500 }
    );
  }
}
