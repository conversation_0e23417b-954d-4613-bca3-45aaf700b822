import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Fetch pending KYC documents for admin review
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get pending KYC documents with user information
    const pendingDocuments = await prisma.kYCDocument.findMany({
      where: { status: 'PENDING' },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            referralId: true,
            createdAt: true,
          },
        },
      },
      orderBy: { createdAt: 'asc' },
    });

    // Transform to match component interface
    const result = pendingDocuments.map(doc => ({
      id: doc.id,
      userId: doc.userId,
      user: {
        firstName: doc.user.firstName,
        lastName: doc.user.lastName,
        email: doc.user.email,
        referralId: doc.user.referralId,
      },
      documentType: doc.documentType,
      documentUrl: doc.filePath, // Map filePath to documentUrl for component compatibility
      status: doc.status,
      submittedAt: doc.createdAt.toISOString(),
      reviewedAt: doc.reviewedAt?.toISOString(),
      rejectionReason: doc.rejectionReason,
    }));

    return NextResponse.json({
      success: true,
      data: result,
    });

  } catch (error: any) {
    console.error('Pending KYC fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch pending KYC documents' },
      { status: 500 }
    );
  }
}
