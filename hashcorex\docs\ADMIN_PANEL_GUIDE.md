# HashCoreX Admin Panel Guide

This guide covers the complete admin panel functionality, access control, and management features.

## Table of Contents

1. [Admin Access Setup](#admin-access-setup)
2. [Admin Panel Features](#admin-panel-features)
3. [Role-Based Access Control](#role-based-access-control)
4. [Admin Dashboard](#admin-dashboard)
5. [User Management](#user-management)
6. [KYC Review System](#kyc-review-system)
7. [Withdrawal Management](#withdrawal-management)
8. [System Settings](#system-settings)
9. [System Logs](#system-logs)

## Admin Access Setup

### Creating Admin User

1. **Set Environment Variables**
   ```bash
   # .env.local
   ADMIN_EMAIL="<EMAIL>"
   ADMIN_PASSWORD="your_secure_password"
   ```

2. **Create Admin User**
   ```bash
   # Run the admin creation script
   npx tsx scripts/create-admin.ts
   ```

3. **Access Admin Panel**
   - Login with admin credentials at: http://localhost:3000/login
   - Navigate to admin panel at: http://localhost:3000/admin
   - Or click "Admin Panel" in the user dashboard sidebar (only visible to admin users)

### Admin Authentication Flow

The admin authentication system works as follows:

1. **User Login**: Admin logs in with regular login form
2. **Role Check**: System checks if user has `ADMIN` role
3. **Access Control**: Admin panel routes are protected by role-based middleware
4. **Session Management**: Admin sessions are managed with JWT tokens

## Admin Panel Features

### Dashboard Overview

The admin dashboard provides:

- **User Statistics**: Total users, active users, new registrations
- **Financial Metrics**: Total investments, earnings distributed, platform revenue
- **Mining Statistics**: Total THS sold, active mining units
- **KYC Status**: Pending and approved KYC applications
- **Withdrawal Status**: Pending and completed withdrawals

### Navigation Structure

```
Admin Panel
├── Dashboard (Overview & Statistics)
├── User Management (User CRUD operations)
├── KYC Review (Document verification)
├── Withdrawal Management (Process withdrawals)
├── System Settings (Platform configuration)
└── System Logs (Activity monitoring)
```

## Role-Based Access Control

### User Roles

```typescript
enum UserRole {
  USER = 'USER',     // Regular platform users
  ADMIN = 'ADMIN'    // Administrative users
}
```

### Access Control Implementation

#### Frontend Protection
```typescript
// Dashboard sidebar - Admin link only visible to admins
const navigationItems = user?.role === 'ADMIN'
  ? [...baseNavigationItems, { id: 'admin', label: 'Admin Panel', icon: Settings }]
  : baseNavigationItems;

// Admin page protection
useEffect(() => {
  if (user && user.role !== 'ADMIN') {
    router.push('/dashboard');
  }
}, [user]);
```

#### Backend Protection
```typescript
// API route protection
export async function GET(request: NextRequest) {
  const { authenticated, user } = await authenticateRequest(request);
  
  if (!authenticated || !user) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
  }
  
  // Check admin role
  const userIsAdmin = await isAdmin(user.id);
  if (!userIsAdmin) {
    return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
  }
  
  // Admin-only logic here...
}
```

## Admin Dashboard

### Key Metrics Displayed

1. **User Metrics**
   - Total registered users
   - Active users (last 30 days)
   - New registrations (today/week/month)
   - User growth trends

2. **Financial Metrics**
   - Total platform investments
   - Total earnings distributed
   - Platform revenue (fees collected)
   - Binary matching payouts

3. **Mining Metrics**
   - Total THS sold
   - Active THS (currently mining)
   - Mining unit sales trends
   - Average investment per user

4. **Operational Metrics**
   - Pending KYC applications
   - Pending withdrawal requests
   - System alerts and notifications
   - Platform health indicators

### Real-time Updates

The dashboard automatically refreshes data every 30 seconds to provide real-time insights.

## User Management

### Features

1. **User Search & Filtering**
   - Search by email, name, or referral ID
   - Filter by status (active/inactive)
   - Filter by KYC status
   - Filter by role

2. **User Actions**
   - Activate/Deactivate user accounts
   - Promote users to admin role
   - View user details and statistics
   - Reset user passwords (if needed)

3. **User Information Display**
   - Personal information
   - Account status and role
   - KYC verification status
   - Registration date
   - Total investments and earnings

### User Management Operations

```typescript
// Activate/Deactivate user
const handleUserAction = async (userId: string, action: 'activate' | 'deactivate') => {
  const response = await fetch('/api/admin/users/action', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ userId, action }),
  });
};

// Promote user to admin
const promoteToAdmin = async (userId: string) => {
  const response = await fetch('/api/admin/users/action', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ userId, action: 'promote' }),
  });
};
```

## KYC Review System

### Document Review Process

1. **Document Submission**: Users upload KYC documents
2. **Admin Review**: Documents appear in admin KYC review queue
3. **Verification**: Admin reviews documents and makes decision
4. **Status Update**: User KYC status updated (APPROVED/REJECTED)
5. **Notification**: User notified of decision

### KYC Review Features

- **Document Viewer**: View uploaded documents in browser
- **Approval/Rejection**: One-click approve or reject with reason
- **Rejection Reasons**: Mandatory rejection reason for transparency
- **Status Tracking**: Track review history and decisions
- **Bulk Operations**: Process multiple applications efficiently

### KYC Review Workflow

```typescript
// Approve KYC document
const approveKYC = async (documentId: string) => {
  const response = await fetch('/api/admin/kyc/review', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      documentId,
      action: 'APPROVE',
    }),
  });
};

// Reject KYC document
const rejectKYC = async (documentId: string, reason: string) => {
  const response = await fetch('/api/admin/kyc/review', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      documentId,
      action: 'REJECT',
      rejectionReason: reason,
    }),
  });
};
```

## Withdrawal Management

### Withdrawal Processing Workflow

1. **Request Submission**: User submits withdrawal request
2. **Admin Review**: Request appears in admin withdrawal queue
3. **Approval**: Admin approves or rejects request
4. **Processing**: Approved requests are processed
5. **Completion**: Transaction hash recorded, status updated

### Withdrawal Management Features

- **Request Queue**: View all pending withdrawal requests
- **Approval System**: Approve or reject requests with reasons
- **Transaction Tracking**: Record blockchain transaction hashes
- **Status Management**: Track request through entire lifecycle
- **Filtering**: Filter by status, amount, date, user

### Withdrawal Actions

```typescript
// Approve withdrawal
const approveWithdrawal = async (withdrawalId: string) => {
  const response = await fetch('/api/admin/withdrawals/action', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      withdrawalId,
      action: 'APPROVE',
    }),
  });
};

// Complete withdrawal with transaction hash
const completeWithdrawal = async (withdrawalId: string, txHash: string) => {
  const response = await fetch('/api/admin/withdrawals/action', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      withdrawalId,
      action: 'COMPLETE',
      transactionHash: txHash,
    }),
  });
};
```

## System Settings

### Configurable Parameters

1. **Mining Unit Pricing**
   - THS price in USD
   - Minimum purchase amount
   - Maximum purchase amount

2. **Earnings Configuration**
   - Daily ROI percentage
   - Binary bonus percentage
   - Referral bonus percentage

3. **Withdrawal Settings**
   - Minimum withdrawal amount
   - Withdrawal fee percentage
   - Processing time (days)

4. **Platform Settings**
   - Platform fee percentage
   - Maintenance mode toggle
   - Registration enabled/disabled
   - KYC requirement toggle

### Settings Management

```typescript
// Update system settings
const updateSettings = async (settings: SystemSettings) => {
  const response = await fetch('/api/admin/settings', {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(settings),
  });
};
```

## System Logs

### Activity Monitoring

The system logs track all important platform activities:

- **User Actions**: Login, registration, profile updates
- **Financial Transactions**: Investments, withdrawals, earnings
- **Administrative Actions**: KYC approvals, user management
- **System Events**: Settings changes, maintenance activities

### Log Features

- **Real-time Logging**: All activities logged immediately
- **Filtering**: Filter by action type, user, date range
- **Search**: Search logs by keywords
- **Export**: Export logs to CSV for analysis
- **Pagination**: Efficient handling of large log volumes

### Log Analysis

```typescript
// Fetch filtered logs
const fetchLogs = async (filters: LogFilters) => {
  const params = new URLSearchParams({
    action: filters.action,
    dateRange: filters.dateRange,
    search: filters.search,
  });
  
  const response = await fetch(`/api/admin/logs?${params}`);
  return response.json();
};
```

## Security Considerations

### Admin Security Best Practices

1. **Strong Authentication**
   - Use strong admin passwords
   - Consider implementing 2FA
   - Regular password rotation

2. **Access Control**
   - Principle of least privilege
   - Regular access reviews
   - Session timeout management

3. **Activity Monitoring**
   - Monitor admin actions
   - Log all administrative activities
   - Set up alerts for suspicious activities

4. **Data Protection**
   - Secure handling of sensitive data
   - Regular security audits
   - Compliance with data protection regulations

### Admin Session Management

```typescript
// Admin session validation
const validateAdminSession = async (token: string) => {
  const decoded = verifyToken(token);
  if (!decoded) return false;
  
  const user = await userDb.findByEmail(decoded.email);
  return user?.role === 'ADMIN' && user?.isActive;
};
```

---

For technical implementation details, refer to the source code in `/src/components/admin/` and `/src/app/api/admin/`.
