import { prisma } from '../src/lib/prisma';

async function cleanupDuplicateDeposits() {
  console.log('🧹 Cleaning up duplicate deposit transactions...\n');

  try {
    // Find duplicate deposit transactions (same transaction ID in description)
    console.log('1. Finding duplicate deposit transactions...');
    
    const duplicateTransactions = await prisma.transaction.findMany({
      where: {
        type: 'DEPOSIT',
        description: {
          contains: 'USDT TRC20 Deposit - TX:'
        }
      },
      orderBy: [
        { userId: 'asc' },
        { description: 'asc' },
        { createdAt: 'asc' }
      ]
    });

    console.log(`Found ${duplicateTransactions.length} deposit transactions`);

    // Group by user and transaction ID
    const groupedTransactions = new Map<string, typeof duplicateTransactions>();
    
    duplicateTransactions.forEach(tx => {
      const key = `${tx.userId}-${tx.description}`;
      if (!groupedTransactions.has(key)) {
        groupedTransactions.set(key, []);
      }
      groupedTransactions.get(key)!.push(tx);
    });

    // Find actual duplicates
    const duplicateGroups = Array.from(groupedTransactions.entries())
      .filter(([_, transactions]) => transactions.length > 1);

    console.log(`Found ${duplicateGroups.length} groups with duplicate transactions`);

    if (duplicateGroups.length === 0) {
      console.log('✅ No duplicate transactions found!');
      return;
    }

    // Process duplicates
    let totalRemoved = 0;
    let totalAmountAdjusted = 0;

    for (const [key, transactions] of duplicateGroups) {
      const [userId, description] = key.split('-', 2);
      const txId = description.split('TX: ')[1];
      
      console.log(`\n📋 Processing duplicates for user ${userId}, TX: ${txId?.slice(0, 16)}...`);
      console.log(`   Found ${transactions.length} duplicate transactions`);

      // Keep the first transaction, remove the rest
      const [keepTransaction, ...removeTransactions] = transactions.sort((a, b) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

      console.log(`   Keeping transaction: ${keepTransaction.id} (${keepTransaction.createdAt})`);

      for (const tx of removeTransactions) {
        console.log(`   Removing transaction: ${tx.id} (${tx.createdAt}) - Amount: ${tx.amount}`);
        
        // Remove the duplicate transaction
        await prisma.transaction.delete({
          where: { id: tx.id }
        });

        // Adjust wallet balance (subtract the duplicate amount)
        const wallet = await prisma.walletBalance.findUnique({
          where: { userId: tx.userId }
        });

        if (wallet) {
          await prisma.walletBalance.update({
            where: { userId: tx.userId },
            data: {
              availableBalance: wallet.availableBalance - tx.amount,
              totalDeposits: wallet.totalDeposits - tx.amount,
              lastUpdated: new Date(),
            }
          });

          console.log(`   Adjusted wallet balance: -${tx.amount} USDT`);
          totalAmountAdjusted += tx.amount;
        }

        totalRemoved++;
      }
    }

    console.log(`\n✅ Cleanup completed!`);
    console.log(`   Removed ${totalRemoved} duplicate transactions`);
    console.log(`   Adjusted wallet balances by -${totalAmountAdjusted} USDT total`);

    // Verify final state
    console.log('\n2. Verifying final state...');
    const remainingDuplicates = await prisma.transaction.groupBy({
      by: ['userId', 'description'],
      where: {
        type: 'DEPOSIT',
        description: {
          contains: 'USDT TRC20 Deposit - TX:'
        }
      },
      _count: {
        id: true
      },
      having: {
        id: {
          _count: {
            gt: 1
          }
        }
      }
    });

    if (remainingDuplicates.length === 0) {
      console.log('✅ No remaining duplicates found!');
    } else {
      console.log(`⚠️  Still found ${remainingDuplicates.length} groups with duplicates`);
    }

  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupDuplicateDeposits()
  .then(() => {
    console.log('\n🎉 Cleanup completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Cleanup failed:', error);
    process.exit(1);
  });
