import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Get detailed KYC status for user
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get user's KYC documents
    const documents = await prisma.kYCDocument.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' },
    });

    // Analyze document completeness
    const hasSelfie = documents.some(doc => doc.documentType === 'SELFIE');
    const idDocuments = documents.filter(doc => doc.documentType === 'ID_DOCUMENT');
    
    let hasCompleteIdDocuments = false;
    let idType = null;
    let requiredDocuments = [];

    if (idDocuments.length > 0) {
      idType = idDocuments[0].idType;
      
      if (idType === 'PASSPORT') {
        hasCompleteIdDocuments = idDocuments.some(doc => doc.documentSide === 'FRONT');
        requiredDocuments = ['passport_front', 'selfie'];
      } else {
        const hasFront = idDocuments.some(doc => doc.documentSide === 'FRONT');
        const hasBack = idDocuments.some(doc => doc.documentSide === 'BACK');
        hasCompleteIdDocuments = hasFront && hasBack;
        requiredDocuments = ['id_front', 'id_back', 'selfie'];
      }
    }

    // Calculate completion percentage
    const totalRequired = requiredDocuments.length;
    let completed = 0;
    
    if (hasSelfie) completed++;
    if (idDocuments.some(doc => doc.documentSide === 'FRONT')) completed++;
    if (idType !== 'PASSPORT' && idDocuments.some(doc => doc.documentSide === 'BACK')) completed++;
    
    const completionPercentage = totalRequired > 0 ? Math.round((completed / totalRequired) * 100) : 0;

    // Check if ready for submission
    const readyForSubmission = hasCompleteIdDocuments && hasSelfie && user.kycStatus !== 'PENDING' && user.kycStatus !== 'APPROVED';

    // Get rejection reasons if any
    const rejectedDocuments = documents.filter(doc => doc.status === 'REJECTED' && doc.rejectionReason);

    return NextResponse.json({
      success: true,
      data: {
        status: user.kycStatus,
        completionPercentage,
        readyForSubmission,
        documents: documents.map(doc => ({
          id: doc.id,
          documentType: doc.documentType,
          idType: doc.idType,
          documentSide: doc.documentSide,
          status: doc.status,
          rejectionReason: doc.rejectionReason,
          createdAt: doc.createdAt,
          reviewedAt: doc.reviewedAt,
        })),
        requirements: {
          idType,
          requiredDocuments,
          hasSelfie,
          hasCompleteIdDocuments,
        },
        rejectionReasons: rejectedDocuments.map(doc => ({
          documentType: doc.documentType,
          idType: doc.idType,
          documentSide: doc.documentSide,
          reason: doc.rejectionReason,
        })),
      },
    });

  } catch (error: any) {
    console.error('KYC status fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch KYC status' },
      { status: 500 }
    );
  }
}
