'use client';

import React from 'react';

interface IconProps {
  className?: string;
  size?: number;
}

export const Cryptocurrency: React.FC<IconProps> = ({ className, size = 24 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M8 12h8M12 8v8" stroke="currentColor" strokeWidth="2"/>
      <path d="M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4" stroke="currentColor" strokeWidth="2" fill="none"/>
      <line x1="12" y1="6" x2="12" y2="8" stroke="currentColor" strokeWidth="2"/>
      <line x1="12" y1="16" x2="12" y2="18" stroke="currentColor" strokeWidth="2"/>
    </svg>
  );
};

export const Bitcoin: React.FC<IconProps> = ({ className, size = 24 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path d="M8 12h4c1.1 0 2-.9 2-2s-.9-2-2-2H8v8h4c1.1 0 2-.9 2-2s-.9-2-2-2" stroke="currentColor" strokeWidth="2" fill="none"/>
      <line x1="10" y1="6" x2="10" y2="8" stroke="currentColor" strokeWidth="2"/>
      <line x1="10" y1="16" x2="10" y2="18" stroke="currentColor" strokeWidth="2"/>
      <line x1="14" y1="6" x2="14" y2="8" stroke="currentColor" strokeWidth="2"/>
      <line x1="14" y1="16" x2="14" y2="18" stroke="currentColor" strokeWidth="2"/>
    </svg>
  );
};
