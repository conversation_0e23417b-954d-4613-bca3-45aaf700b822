import { verifyUSDTTransaction } from '../src/lib/trongrid';

async function testDepositVerification() {
  console.log('🧪 Testing Deposit Verification...\n');

  // Test transaction from the user
  const testTxId = '30a36a84f7d77e822f92ab36bb53d98835373a2e638601d1e321224b4b8c1114';
  const expectedAddress = 'TBtMPmTkz4f8xDn2E6mP1wvGtWnzZRwQeK'; // From the new transaction details
  const minConfirmations = 1;

  console.log('Test Parameters:');
  console.log(`  Transaction ID: ${testTxId}`);
  console.log(`  Expected Address: ${expectedAddress}`);
  console.log(`  Min Confirmations: ${minConfirmations}\n`);

  try {
    console.log('🔍 Verifying USDT transaction...');
    const result = await verifyUSDTTransaction(testTxId, expectedAddress, minConfirmations);

    console.log('\n📊 Verification Result:');
    console.log(`  Is Valid: ${result.isValid}`);
    console.log(`  Amount: ${result.amount} USDT`);
    console.log(`  From Address: ${result.fromAddress}`);
    console.log(`  To Address: ${result.toAddress}`);
    console.log(`  Contract Address: ${result.contractAddress}`);
    console.log(`  Block Number: ${result.blockNumber}`);
    console.log(`  Block Timestamp: ${new Date(result.blockTimestamp)}`);
    console.log(`  Confirmations: ${result.confirmations}`);
    console.log(`  Transaction ID: ${result.transactionId}`);

    if (result.isValid) {
      console.log('\n✅ Transaction verification PASSED!');
      console.log('   The deposit should be processed successfully.');
    } else {
      console.log('\n❌ Transaction verification FAILED!');
      console.log('   Possible reasons:');
      console.log('   - Insufficient confirmations');
      console.log('   - Wrong recipient address');
      console.log('   - Transaction failed');
      console.log('   - Not a USDT transfer');
    }

  } catch (error) {
    console.error('\n❌ Error during verification:', error);
  }
}

// Run the test
testDepositVerification();
