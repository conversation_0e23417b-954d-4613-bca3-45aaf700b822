import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { adminSettingsDb, depositTransactionDb } from '@/lib/database';
import { getCurrentNetworkConfig } from '@/lib/trongrid';

// GET - Get deposit information and user's deposit history
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get deposit settings - try both camelCase and UPPER_CASE keys for compatibility
    let depositAddress = await adminSettingsDb.get('usdtDepositAddress');
    if (!depositAddress) {
      depositAddress = await adminSettingsDb.get('USDT_DEPOSIT_ADDRESS');
    }

    // Clean deposit address - remove quotes and extra characters
    if (depositAddress) {
      depositAddress = depositAddress.replace(/['"]/g, '').trim();
    }

    let minDepositAmount = await adminSettingsDb.get('minDepositAmount');
    if (!minDepositAmount) {
      minDepositAmount = await adminSettingsDb.get('MIN_DEPOSIT_AMOUNT');
    }
    minDepositAmount = parseFloat(minDepositAmount || '10');

    let maxDepositAmount = await adminSettingsDb.get('maxDepositAmount');
    if (!maxDepositAmount) {
      maxDepositAmount = await adminSettingsDb.get('MAX_DEPOSIT_AMOUNT');
    }
    maxDepositAmount = parseFloat(maxDepositAmount || '10000');

    let depositEnabled = await adminSettingsDb.get('depositEnabled');
    if (!depositEnabled) {
      depositEnabled = await adminSettingsDb.get('DEPOSIT_ENABLED');
    }
    depositEnabled = depositEnabled === 'true' || depositEnabled === true;

    let minConfirmations = await adminSettingsDb.get('minConfirmations');
    if (!minConfirmations) {
      minConfirmations = await adminSettingsDb.get('MIN_CONFIRMATIONS');
    }
    minConfirmations = parseInt(minConfirmations || '1');

    let depositFeePercentage = await adminSettingsDb.get('depositFeePercentage');
    if (!depositFeePercentage) {
      depositFeePercentage = await adminSettingsDb.get('DEPOSIT_FEE_PERCENTAGE');
    }
    depositFeePercentage = parseFloat(depositFeePercentage || '0');

    // Get user's deposit history
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    const deposits = await depositTransactionDb.findByUserId(user.id, {
      limit: Math.min(limit, 50), // Max 50 records per request
      offset,
    });

    // Calculate user's deposit statistics
    const completedDeposits = deposits.filter(d => d.status === 'COMPLETED' || d.status === 'CONFIRMED');
    const pendingDeposits = deposits.filter(d =>
      ['PENDING', 'PENDING_VERIFICATION', 'WAITING_FOR_CONFIRMATIONS'].includes(d.status)
    );
    const totalDeposited = completedDeposits.reduce((sum, d) => sum + d.usdtAmount, 0);
    const depositCount = completedDeposits.length;
    const pendingCount = pendingDeposits.length;

    // Get current network configuration
    const networkConfig = await getCurrentNetworkConfig();

    return NextResponse.json({
      success: true,
      data: {
        depositInfo: {
          depositAddress: depositAddress || null,
          minDepositAmount,
          maxDepositAmount,
          depositEnabled,
          minConfirmations,
          depositFeePercentage,
          network: 'TRC20',
          currency: 'USDT',
          tronNetwork: networkConfig.network,
          tronApiUrl: networkConfig.apiUrl,
          usdtContract: networkConfig.usdtContract,
        },
        userStats: {
          totalDeposited,
          depositCount,
          pendingDeposits: pendingCount,
        },
        deposits: deposits.map(deposit => ({
          id: deposit.id,
          transactionId: deposit.transactionId,
          amount: deposit.usdtAmount,
          status: deposit.status,
          confirmations: deposit.confirmations,
          blockNumber: deposit.blockNumber,
          senderAddress: deposit.senderAddress,
          createdAt: deposit.createdAt,
          verifiedAt: deposit.verifiedAt,
          processedAt: deposit.processedAt,
          failureReason: deposit.failureReason,
        })),
        pagination: {
          limit,
          offset,
          hasMore: deposits.length === limit, // Simple check for more records
        },
      },
    });

  } catch (error) {
    console.error('Deposit info error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch deposit information' },
      { status: 500 }
    );
  }
}
