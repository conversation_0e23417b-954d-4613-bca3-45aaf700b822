#!/usr/bin/env tsx

/**
 * Validation script for HashCoreX Binary MLM System
 * This script validates the implementation of the enhanced binary tree features
 */

import { prisma } from '../lib/prisma';
import { 
  placeUserInBinaryTree, 
  placeUserInSpecificSide,
  getBinaryTreeStructure,
  getDetailedTeamStats,
  getTreeHealthStats,
  searchUsersInTree,
  updateCachedDownlineCounts
} from '../lib/referral';
import { validateReferralCode, generateReferralLinks } from '../lib/referralValidation';

interface ValidationResult {
  test: string;
  passed: boolean;
  message: string;
  details?: any;
}

class ImplementationValidator {
  private results: ValidationResult[] = [];

  private addResult(test: string, passed: boolean, message: string, details?: any) {
    this.results.push({ test, passed, message, details });
    console.log(`${passed ? '✅' : '❌'} ${test}: ${message}`);
    if (details && !passed) {
      console.log('   Details:', details);
    }
  }

  async validateDatabaseSchema(): Promise<void> {
    console.log('\n🔍 Validating Database Schema...');
    
    try {
      // Check if new fields exist in User model
      const userSample = await prisma.user.findFirst({
        select: {
          directReferralCount: true,
          totalLeftDownline: true,
          totalRightDownline: true,
          lastTreeUpdate: true,
        },
      });
      
      this.addResult(
        'Database Schema - User Model',
        true,
        'Enhanced user fields are available'
      );
    } catch (error) {
      this.addResult(
        'Database Schema - User Model',
        false,
        'Enhanced user fields are missing',
        error
      );
    }

    try {
      // Check if new fields exist in Referral model
      const referralSample = await prisma.referral.findFirst({
        select: {
          isDirectSponsor: true,
        },
      });
      
      this.addResult(
        'Database Schema - Referral Model',
        true,
        'Enhanced referral fields are available'
      );
    } catch (error) {
      this.addResult(
        'Database Schema - Referral Model',
        false,
        'Enhanced referral fields are missing',
        error
      );
    }

    try {
      // Check if new fields exist in BinaryPoints model
      const binaryPointsSample = await prisma.binaryPoints.findFirst({
        select: {
          totalMatched: true,
          lastMatchDate: true,
        },
      });
      
      this.addResult(
        'Database Schema - BinaryPoints Model',
        true,
        'Enhanced binary points fields are available'
      );
    } catch (error) {
      this.addResult(
        'Database Schema - BinaryPoints Model',
        false,
        'Enhanced binary points fields are missing',
        error
      );
    }
  }

  async validatePlacementAlgorithm(): Promise<void> {
    console.log('\n🌳 Validating Placement Algorithm...');
    
    try {
      // Create test users
      const rootUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          firstName: 'Root',
          lastName: 'User',
          password: 'hashedpassword',
          referralId: 'VALIDATION_ROOT',
          isActive: true,
        },
      });

      const user1 = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'One',
          password: 'hashedpassword',
          referralId: 'VALIDATION_USER1',
          isActive: true,
        },
      });

      const user2 = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'Two',
          password: 'hashedpassword',
          referralId: 'VALIDATION_USER2',
          isActive: true,
        },
      });

      // Test general placement
      const placement1 = await placeUserInBinaryTree(rootUser.id, user1.id);
      const placement2 = await placeUserInBinaryTree(rootUser.id, user2.id);

      this.addResult(
        'Placement Algorithm - General',
        placement1 === 'LEFT' && placement2 === 'RIGHT',
        `First user placed in ${placement1}, second in ${placement2}`,
        { placement1, placement2 }
      );

      // Test specific side placement
      const user3 = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'Three',
          password: 'hashedpassword',
          referralId: 'VALIDATION_USER3',
          isActive: true,
        },
      });

      const specificPlacement = await placeUserInSpecificSide(rootUser.id, user3.id, 'LEFT');
      
      this.addResult(
        'Placement Algorithm - Specific Side',
        specificPlacement === 'LEFT',
        `User placed in requested side: ${specificPlacement}`
      );

      // Clean up test data
      await prisma.referral.deleteMany({
        where: {
          OR: [
            { referrerId: rootUser.id },
            { referredId: { in: [user1.id, user2.id, user3.id] } },
          ],
        },
      });
      await prisma.user.deleteMany({
        where: {
          id: { in: [rootUser.id, user1.id, user2.id, user3.id] },
        },
      });

    } catch (error) {
      this.addResult(
        'Placement Algorithm',
        false,
        'Placement algorithm validation failed',
        error
      );
    }
  }

  async validateTreeStatistics(): Promise<void> {
    console.log('\n📊 Validating Tree Statistics...');
    
    try {
      // Create a small test tree
      const rootUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          firstName: 'Stats',
          lastName: 'User',
          password: 'hashedpassword',
          referralId: 'VALIDATION_STATS',
          isActive: true,
        },
      });

      // Test tree statistics functions
      const treeStructure = await getBinaryTreeStructure(rootUser.id, 3);
      const teamStats = await getDetailedTeamStats(rootUser.id);
      const treeHealth = await getTreeHealthStats(rootUser.id);

      this.addResult(
        'Tree Statistics - Structure',
        treeStructure !== null,
        'Tree structure retrieval works'
      );

      this.addResult(
        'Tree Statistics - Team Stats',
        typeof teamStats.totalTeam === 'number',
        'Team statistics calculation works'
      );

      this.addResult(
        'Tree Statistics - Health Stats',
        typeof treeHealth.balanceRatio === 'number',
        'Tree health calculation works'
      );

      // Clean up
      await prisma.user.delete({ where: { id: rootUser.id } });

    } catch (error) {
      this.addResult(
        'Tree Statistics',
        false,
        'Tree statistics validation failed',
        error
      );
    }
  }

  async validateReferralValidation(): Promise<void> {
    console.log('\n🔗 Validating Referral Validation...');
    
    try {
      // Test referral code validation
      const testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          firstName: 'Referral',
          lastName: 'User',
          password: 'hashedpassword',
          referralId: 'VALIDATION_REF',
          isActive: true,
        },
      });

      const validResult = await validateReferralCode('VALIDATION_REF');
      const invalidResult = await validateReferralCode('INVALID_CODE');

      this.addResult(
        'Referral Validation - Valid Code',
        validResult.isValid === true,
        'Valid referral code accepted'
      );

      this.addResult(
        'Referral Validation - Invalid Code',
        invalidResult.isValid === false,
        'Invalid referral code rejected'
      );

      // Test referral link generation
      const links = generateReferralLinks('VALIDATION_REF', 'http://localhost:3000');
      
      this.addResult(
        'Referral Links Generation',
        links.general.includes('VALIDATION_REF') && 
        links.left.includes('side=left') && 
        links.right.includes('side=right'),
        'Referral links generated correctly'
      );

      // Clean up
      await prisma.user.delete({ where: { id: testUser.id } });

    } catch (error) {
      this.addResult(
        'Referral Validation',
        false,
        'Referral validation failed',
        error
      );
    }
  }

  async validateAPIEndpoints(): Promise<void> {
    console.log('\n🌐 Validating API Endpoints...');
    
    // Note: This would require setting up a test server
    // For now, we'll just check if the functions are importable
    try {
      const { getBinaryTreeStructure } = await import('../lib/referral');
      
      this.addResult(
        'API Functions',
        typeof getBinaryTreeStructure === 'function',
        'Core API functions are available'
      );
    } catch (error) {
      this.addResult(
        'API Functions',
        false,
        'API functions import failed',
        error
      );
    }
  }

  async runAllValidations(): Promise<void> {
    console.log('🚀 Starting HashCoreX Implementation Validation...\n');
    
    await this.validateDatabaseSchema();
    await this.validatePlacementAlgorithm();
    await this.validateTreeStatistics();
    await this.validateReferralValidation();
    await this.validateAPIEndpoints();
    
    this.printSummary();
  }

  private printSummary(): void {
    console.log('\n📋 Validation Summary:');
    console.log('=' .repeat(50));
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const percentage = Math.round((passed / total) * 100);
    
    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${total - passed}`);
    console.log(`Success Rate: ${percentage}%`);
    
    if (percentage === 100) {
      console.log('\n🎉 All validations passed! Implementation is ready.');
    } else {
      console.log('\n⚠️  Some validations failed. Please review the issues above.');
      
      const failedTests = this.results.filter(r => !r.passed);
      console.log('\nFailed Tests:');
      failedTests.forEach(test => {
        console.log(`  - ${test.test}: ${test.message}`);
      });
    }
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  const validator = new ImplementationValidator();
  validator.runAllValidations()
    .then(() => {
      console.log('\n✅ Validation completed.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Validation failed:', error);
      process.exit(1);
    });
}

export { ImplementationValidator };
