import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { adminSettingsDb } from '@/lib/database';

// POST - Update binary points limit setting (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { maxPointsPerSide } = body;

    if (!maxPointsPerSide || maxPointsPerSide <= 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid max points per side value' },
        { status: 400 }
      );
    }

    console.log(`Updating MAX_BINARY_POINTS_PER_SIDE to: ${maxPointsPerSide}`);

    // Update the setting
    await adminSettingsDb.set('MAX_BINARY_POINTS_PER_SIDE', maxPointsPerSide.toString(), user.id);

    // Verify the update
    const updatedValue = await adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE');

    console.log(`Updated MAX_BINARY_POINTS_PER_SIDE to: ${updatedValue}`);

    return NextResponse.json({
      success: true,
      message: `Successfully updated binary points limit to ${maxPointsPerSide}`,
      data: {
        maxPointsPerSide: parseFloat(updatedValue || '0'),
      },
    });

  } catch (error) {
    console.error('Binary points limit update error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update binary points limit' },
      { status: 500 }
    );
  }
}
