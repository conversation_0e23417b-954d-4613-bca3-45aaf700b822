'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button, Input } from '@/components/ui';
import { Container, Flex } from '@/components/layout';
import { SolarPanel } from '@/components/icons';
import { Eye, EyeOff, ArrowLeft } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

export default function LoginPage() {
  const router = useRouter();
  const { login, loading } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    try {
      await login(formData.email, formData.password);
      router.push('/dashboard');
    } catch (err: any) {
      setError(err.message || 'Login failed');
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="absolute inset-0 animated-gradient opacity-30"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-white/95 via-solar-50/90 to-eco-50/95"></div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-16 h-16 bg-solar-400/20 rounded-full animate-float"></div>
      <div className="absolute top-40 right-20 w-12 h-12 bg-eco-400/20 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
      <div className="absolute bottom-40 left-20 w-10 h-10 bg-purple-400/20 rounded-full animate-float" style={{animationDelay: '4s'}}></div>

      {/* Responsive Layout */}
      <div className="relative z-10 min-h-screen flex">
        {/* Left Side - Hidden on mobile, visible on desktop */}
        <div className="hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-solar-500/10 to-eco-500/10 items-center justify-center p-12">
          <div className="max-w-lg text-center">
            <div className="mb-8">
              <SolarPanel className="h-24 w-24 text-solar-500 mx-auto mb-6" />
              <h2 className="text-4xl xl:text-5xl font-black text-dark-900 mb-4">
                Welcome to the Future of Mining
              </h2>
              <p className="text-xl text-gray-600 leading-relaxed">
                Join thousands of users earning daily returns through our sustainable,
                solar-powered cryptocurrency mining platform.
              </p>
            </div>
            <div className="grid grid-cols-1 gap-6 text-left">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-eco-500/20 rounded-full flex items-center justify-center">
                  <span className="text-eco-600 font-bold">✓</span>
                </div>
                <div>
                  <h3 className="font-semibold text-dark-900">Eco-Friendly Mining</h3>
                  <p className="text-gray-600">100% solar-powered operations</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-solar-500/20 rounded-full flex items-center justify-center">
                  <span className="text-solar-600 font-bold">⚡</span>
                </div>
                <div>
                  <h3 className="font-semibold text-dark-900">Daily Returns</h3>
                  <p className="text-gray-600">Consistent 0.6% - 1.1% daily ROI</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 font-bold">🔒</span>
                </div>
                <div>
                  <h3 className="font-semibold text-dark-900">Secure Platform</h3>
                  <p className="text-gray-600">Bank-level security & encryption</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Form */}
        <div className="w-full lg:w-1/2 xl:w-2/5 flex items-center justify-center p-4 lg:p-8">
          <div className="w-full max-w-md">
            <div className="glass-morphism rounded-3xl p-8 lg:p-10 shadow-2xl">
              {/* Premium Header */}
              <div className="text-center mb-8 lg:mb-10">
                <Link href="/" className="inline-flex items-center space-x-3 mb-6 lg:mb-8 group lg:hidden">
                  <div className="relative">
                    <SolarPanel className="h-10 w-10 lg:h-12 lg:w-12 text-solar-500 group-hover:scale-110 transition-transform" />
                    <div className="absolute inset-0 bg-solar-500/20 rounded-full animate-ping"></div>
                  </div>
                  <span className="text-2xl lg:text-4xl font-black bg-gradient-to-r from-solar-600 to-eco-600 bg-clip-text text-transparent">
                    HashCoreX
                  </span>
                </Link>
                <h1 className="text-2xl lg:text-3xl xl:text-4xl font-black text-dark-900 mb-3 lg:mb-4">Welcome Back</h1>
                <p className="text-base lg:text-lg text-gray-600 font-medium">Sign in to your mining dashboard</p>
              </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
                {error}
              </div>
            )}

            <Input
              label="Email Address"
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter your email"
              required
            />

            <Input
              label="Password"
              type={showPassword ? 'text' : 'password'}
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Enter your password"
              required
              rightIcon={
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              }
            />

            <div className="flex items-center justify-between">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-solar-500 focus:ring-solar-500"
                />
                <span className="ml-2 text-sm text-gray-600">Remember me</span>
              </label>
              <Link href="/forgot-password" className="text-sm text-solar-500 hover:text-solar-600">
                Forgot password?
              </Link>
            </div>

            <Button
              type="submit"
              variant="primary"
              size="xl"
              className="w-full font-bold"
              loading={loading}
            >
              Sign In to Dashboard
            </Button>
          </form>

              {/* Footer */}
              <div className="mt-8 text-center">
                <p className="text-gray-600">
                  Don't have an account?{' '}
                  <Link href="/register" className="text-solar-500 hover:text-solar-600 font-medium">
                    Sign up
                  </Link>
                </p>
              </div>

              {/* Back to Home */}
              <div className="mt-6 text-center lg:hidden">
                <Link href="/" className="inline-flex items-center text-gray-500 hover:text-gray-700 text-sm">
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Back to Home
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
