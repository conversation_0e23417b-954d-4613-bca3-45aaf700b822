import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testWalletAPI() {
  console.log('🧪 Testing wallet adjustment API...');

  try {
    // Get test user and admin user
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (!testUser || !adminUser) {
      console.log('❌ Test user or admin user not found');
      return;
    }

    console.log(`📋 Test User: ${testUser.email} (ID: ${testUser.id})`);
    console.log(`👤 Admin User: ${adminUser.email} (ID: ${adminUser.id})`);

    // Check initial wallet balance
    let wallet = await prisma.walletBalance.findUnique({
      where: { userId: testUser.id }
    });

    console.log(`💰 Initial balance: ${wallet?.availableBalance || 0} USDT`);

    // Test credit operation
    console.log('\n🔄 Testing wallet credit...');
    
    // Simulate API call data
    const creditData = {
      userId: testUser.id,
      amount: 100.50,
      type: 'CREDIT',
      reason: 'Manual Adjustment',
      description: 'Test credit for wallet functionality'
    };

    // Manually perform the wallet adjustment (simulating the API)
    const currentWallet = await prisma.walletBalance.upsert({
      where: { userId: testUser.id },
      update: {},
      create: {
        userId: testUser.id,
        availableBalance: 0,
        pendingBalance: 0,
        totalDeposits: 0,
        totalWithdrawals: 0,
        totalEarnings: 0,
      }
    });

    const newBalance = currentWallet.availableBalance + creditData.amount;

    await prisma.walletBalance.update({
      where: { userId: testUser.id },
      data: {
        availableBalance: newBalance,
        lastUpdated: new Date(),
      },
    });

    // Create transaction record
    const transaction = await prisma.transaction.create({
      data: {
        userId: testUser.id,
        type: 'ADMIN_CREDIT',
        amount: creditData.amount,
        description: creditData.description,
        status: 'COMPLETED',
      },
    });

    // Create system log
    await prisma.systemLog.create({
      data: {
        action: 'WALLET_ADJUSTMENT',
        adminId: adminUser.id,
        userId: testUser.id,
        details: JSON.stringify({
          type: creditData.type,
          amount: creditData.amount,
          reason: creditData.reason,
          description: creditData.description,
          previousBalance: currentWallet.availableBalance,
          newBalance,
          transactionId: transaction.id,
        }),
        ipAddress: 'test-script',
        userAgent: 'test-script',
      },
    });

    console.log(`✅ Credit successful! New balance: ${newBalance} USDT`);
    console.log(`📝 Transaction ID: ${transaction.id}`);

    // Verify the changes
    const updatedWallet = await prisma.walletBalance.findUnique({
      where: { userId: testUser.id }
    });

    console.log(`🔍 Verified balance: ${updatedWallet?.availableBalance} USDT`);

    console.log('\n🎉 Wallet adjustment functionality test completed successfully!');
    console.log('🌐 You can now test the UI at: http://localhost:3000/admin');

  } catch (error) {
    console.error('❌ Error testing wallet API:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testWalletAPI();
