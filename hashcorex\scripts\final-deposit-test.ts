import { verifyUSDTTransaction } from '../src/lib/trongrid';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function finalDepositTest() {
  console.log('🎯 Final Deposit System Test...\n');

  try {
    // Get current settings
    const settings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: ['depositAddress', 'tronNetwork', 'minConfirmations']
        }
      }
    });

    const settingsMap = settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {} as Record<string, string>);

    console.log('📋 Current System Settings:');
    console.log(`  Deposit Address: ${settingsMap.depositAddress}`);
    console.log(`  Network: ${settingsMap.tronNetwork}`);
    console.log(`  Min Confirmations: ${settingsMap.minConfirmations}\n`);

    // Test with the working transaction
    const testTxId = '30a36a84f7d77e822f92ab36bb53d98835373a2e638601d1e321224b4b8c1114';
    const expectedAddress = settingsMap.depositAddress;
    const minConfirmations = parseInt(settingsMap.minConfirmations || '1');

    console.log('🔍 Testing deposit verification...');
    console.log(`  Transaction ID: ${testTxId}`);
    console.log(`  Expected Address: ${expectedAddress}`);
    console.log(`  Min Confirmations: ${minConfirmations}\n`);

    const result = await verifyUSDTTransaction(testTxId, expectedAddress, minConfirmations);

    console.log('📊 Verification Result:');
    console.log(`  ✅ Valid: ${result.isValid}`);
    console.log(`  💰 Amount: ${result.amount} USDT`);
    console.log(`  📤 From: ${result.fromAddress}`);
    console.log(`  📥 To: ${result.toAddress}`);
    console.log(`  🔗 Contract: ${result.contractAddress}`);
    console.log(`  📦 Block: ${result.blockNumber}`);
    console.log(`  ⏰ Time: ${new Date(result.blockTimestamp)}`);
    console.log(`  ✔️  Confirmations: ${result.confirmations}/${minConfirmations}`);

    if (result.isValid) {
      console.log('\n🎉 SUCCESS! The deposit system is working correctly!');
      console.log('   ✅ Transaction verified successfully');
      console.log('   ✅ Address matches deposit address');
      console.log('   ✅ Amount parsed correctly');
      console.log('   ✅ Sufficient confirmations');
      console.log('\n🚀 Ready for production use!');
      console.log('   - Users can now verify USDT deposits');
      console.log('   - System will automatically credit wallets');
      console.log('   - Background processing handles confirmations');
    } else {
      console.log('\n❌ FAILED! Issues found:');
      if (result.confirmations < minConfirmations) {
        console.log(`   - Insufficient confirmations (${result.confirmations}/${minConfirmations})`);
      }
      if (result.toAddress.toLowerCase() !== expectedAddress.toLowerCase()) {
        console.log(`   - Address mismatch (${result.toAddress} vs ${expectedAddress})`);
      }
      if (result.amount <= 0) {
        console.log(`   - Invalid amount (${result.amount})`);
      }
    }

    // Test with invalid transaction
    console.log('\n🧪 Testing with invalid transaction...');
    const invalidResult = await verifyUSDTTransaction('invalid_tx_id', expectedAddress, 1);
    
    if (!invalidResult.isValid) {
      console.log('✅ Invalid transaction correctly rejected');
    } else {
      console.log('❌ Invalid transaction was incorrectly accepted');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

finalDepositTest();
