import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { supportTicketDb } from '@/lib/database';

// GET - Fetch user's support tickets
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const tickets = await supportTicketDb.findByUserId(user.id);

    return NextResponse.json({
      success: true,
      data: tickets,
    });

  } catch (error: any) {
    console.error('Support tickets fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch support tickets' },
      { status: 500 }
    );
  }
}

// POST - Create new support ticket
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { subject, message, priority = 'MEDIUM', category } = body;

    // Validate required fields
    if (!subject || !message) {
      return NextResponse.json(
        { success: false, error: 'Subject and message are required' },
        { status: 400 }
      );
    }

    // Validate priority
    const validPriorities = ['LOW', 'MEDIUM', 'HIGH', 'URGENT'];
    if (!validPriorities.includes(priority)) {
      return NextResponse.json(
        { success: false, error: 'Invalid priority level' },
        { status: 400 }
      );
    }

    // Create the support ticket
    const ticket = await supportTicketDb.create({
      userId: user.id,
      subject: subject.trim(),
      message: message.trim(),
      priority,
      category: category?.trim() || null,
    });

    return NextResponse.json({
      success: true,
      data: ticket,
      message: 'Support ticket created successfully',
    });

  } catch (error: any) {
    console.error('Support ticket creation error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to create support ticket' },
      { status: 500 }
    );
  }
}
