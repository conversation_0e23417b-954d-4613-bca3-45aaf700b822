# HashCoreX Implementation Checklist

## Project Overview
Complete implementation of HashCoreX - a cross-platform cloud mining investment platform with:
- Solar-powered futuristic UI theme
- Binary referral system with left/right tree structure
- Custom TH/s mining units with 12-month expiry
- Internal wallet with USDT withdrawals
- KYC verification system
- Admin dashboard for complete platform management
- Cross-platform support (Web + Mobile via CapacitorJS)

## Color Palette & Design System
- **White**: #ffffff (Background)
- **Solar Yellow**: #ffd60a (Highlights/CTAs)
- **Emerald Green**: #10b981 (Success/Eco/Verified)
- **Deep Blue**: #0f172a (Primary Text)
- **Grey**: #6b7280 (Secondary UI)
- **Typography**: Inter/Roboto, 14px base, 18px body, 24px headings

---

## Implementation Tasks Breakdown

### 1. Project Setup & Architecture ✅
- [ ] Initialize Next.js Project with TypeScript & Tailwind CSS
- [ ] Configure Project Structure (components, pages, lib, types, hooks, utils)
- [ ] Install Core Dependencies (Firebase, PostgreSQL, crypto, UI libraries)
- [ ] Environment Configuration (database, Firebase, API configs)
- [ ] TypeScript Configuration with strict settings

### 2. Database Design & Setup ✅
- [ ] Design Database Schema (ERD with all required tables)
- [ ] Create Database Tables with relationships and constraints
- [ ] Set up Database Connection pool and query builder
- [ ] Create Database Migrations system
- [ ] Seed Initial Data (admin settings, configurations)

**Required Tables:**
- users (id, email, password_hash, referral_id, left_referral_id, right_referral_id, kyc_status, created_at)
- mining_units (id, user_id, ths_amount, investment_amount, start_date, expiry_date, daily_roi, total_earned, status)
- transactions (id, user_id, type, amount, description, status, created_at)
- referrals (id, referrer_id, referred_id, placement_side, commission_earned, created_at)
- binary_points (id, user_id, left_points, right_points, matched_points, flush_date)
- kyc_documents (id, user_id, document_type, file_path, status, reviewed_at)
- admin_settings (key, value, updated_at)
- withdrawal_requests (id, user_id, amount, usdt_address, status, processed_at)

### 3. Authentication System ✅
- [ ] Firebase Authentication setup
- [ ] User registration with email/password
- [ ] Login system with 2FA support
- [ ] Session management and protected routes
- [ ] Unique referral ID generation per user

### 4. Core UI Framework & Design System ✅
- [ ] Implement Color Palette & Theme in Tailwind config
- [ ] Typography System with modular scale
- [ ] Component Library Setup (buttons, cards, forms, modals)
- [ ] Solar-Themed Icons & Graphics (SVG icons)
- [ ] Responsive Grid System (mobile-first breakpoints)

### 5. Public Landing Pages ✅
- [ ] Homepage with animated solar grid background
- [ ] Hero section: "Invest in Solar-Powered Cloud Mining"
- [ ] Features section (KYC, USDT, Real ROI, Eco Mining)
- [ ] Live stats display (hashrate sold, users active)
- [ ] About Us page (company story, sustainability)
- [ ] How It Works page (step-by-step process)
- [ ] Login/Register pages with clean forms

### 6. TH/s Mining Unit System ✅
- [ ] Custom TH/s input with minimum $50 purchase
- [ ] Price calculation based on admin settings
- [ ] Mining unit creation with unique ID and dates
- [ ] 12-month expiry OR 5x investment earned logic
- [ ] Multiple units per user support
- [ ] Unit status tracking (Active/Expired)

### 7. ROI & Earnings Engine ✅
- [ ] Admin configurable ROI percentage/range
- [ ] Daily ROI calculation for active units
- [ ] Pending earnings accumulation
- [ ] Weekly payout system (Saturday 15:00 UTC)
- [ ] Transaction logging for all earnings

### 8. Internal Wallet System ✅
- [ ] Real-time balance calculation
- [ ] Transaction history display
- [ ] USDT (TRC20) withdrawal functionality
- [ ] Minimum withdrawal threshold ($10)
- [ ] Admin approval workflow for withdrawals

### 9. Binary Referral System ✅
- [ ] Left/Right referral link generation
- [ ] New user placement logic (weaker leg)
- [ ] Direct referral bonus (10% of investment)
- [ ] Binary pool management (30% of global investments)
- [ ] Daily binary matching (12:00 AM UTC, max 2,000 per side)
- [ ] Point flush/reset system
- [ ] Commission tracking and payout

### 10. User Dashboard ✅
- [ ] Total TH/s summary display
- [ ] Estimated earnings calculator (7/30/365 days)
- [ ] Wallet balance and transaction history
- [ ] Binary tree visualizer
- [ ] KYC status and upload portal
- [ ] Referral links panel
- [ ] Mining units table with ROI tracking

### 11. KYC Verification System ✅
- [ ] Document upload functionality (ID + selfie)
- [ ] Status tracking (Pending/Approved/Rejected)
- [ ] Admin review interface
- [ ] Withdrawal restriction based on KYC status

### 12. Admin Dashboard ✅
- [ ] Admin authentication with 2FA
- [ ] Platform statistics overview
- [ ] User management interface
- [ ] TH/s price and ROI configuration
- [ ] KYC approval/rejection system
- [ ] Withdrawal request processing
- [ ] Binary pool monitoring
- [ ] System message broadcasting
- [ ] Audit logs and reporting

### 13. Security Implementation ✅
- [ ] AES-256 encryption for sensitive data
- [ ] Secure API token system
- [ ] 2FA for sensitive actions
- [ ] Input validation and sanitization
- [ ] SQL injection prevention
- [ ] Rate limiting implementation

### 14. Mobile Responsiveness & Cross-Platform ✅
- [ ] Mobile-first responsive design
- [ ] Tab-based dashboard navigation
- [ ] Touch-friendly interface elements
- [ ] CapacitorJS integration preparation
- [ ] Push notification setup

### 15. Testing & Quality Assurance ✅
- [ ] Unit tests for core business logic
- [ ] Integration tests for API endpoints
- [ ] Security testing and penetration testing
- [ ] Performance optimization
- [ ] Cross-browser compatibility testing

### 16. Deployment & Infrastructure ✅
- [ ] Production environment setup
- [ ] PostgreSQL database hosting
- [ ] Cloudflare WAF and DDoS protection
- [ ] SSL certificate configuration
- [ ] Monitoring and logging setup
- [ ] Backup and disaster recovery

---

## Key Business Logic Requirements

### Mining Unit Expiry Logic:
- Units expire when EITHER condition is met:
  1. 12 months from start date
  2. Total earnings (mining + referrals) = 5x investment amount

### Binary Referral Matching:
- Weekly at 15:00 UTC on Saturdays
- Match left vs right points (max 2,000 per side)
- Fixed payout: $10 per matched point
- Flush excess points after matching

### Weekly Earnings Distribution:
- Every Saturday at 15:00 UTC
- Transfer pending mining earnings to wallet
- Log all transactions

### ROI Calculation:
- Dynamic ROI based on unit size (0.3%-0.8% daily)
- Small units (<5 TH/s): 0.3%-0.5%
- Medium units (5-10 TH/s): 0.4%-0.6%
- Large units (>10 TH/s): 0.5%-0.7%
- Monthly returns capped at 10%-15%
- Applied to active mining units only
- Accumulated in pending balance until weekly payout

---

## Technical Stack
- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Authentication**: Firebase Auth
- **Database**: PostgreSQL
- **Mobile**: CapacitorJS for cross-platform apps
- **Security**: AES-256 encryption, 2FA
- **Infrastructure**: VPS hosting, Cloudflare protection

This checklist ensures complete implementation of all features specified in the hashcorex_guidelines.md document.
