import { prisma } from './prisma';

// Validate referral code and extract placement information
export async function validateReferralCode(referralCode: string): Promise<{
  isValid: boolean;
  referrer?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    isActive: boolean;
  };
  error?: string;
}> {
  try {
    if (!referralCode || referralCode.trim().length === 0) {
      return { isValid: false, error: 'Referral code is required' };
    }

    const referrer = await prisma.user.findUnique({
      where: { referralId: referralCode.trim() },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        isActive: true,
      },
    });

    if (!referrer) {
      return { isValid: false, error: 'Invalid referral code' };
    }

    if (!referrer.isActive) {
      return { isValid: false, error: 'Referrer account is inactive' };
    }

    return { isValid: true, referrer };
  } catch (error) {
    console.error('Referral code validation error:', error);
    return { isValid: false, error: 'Validation failed' };
  }
}

// Extract placement side from URL parameters
export function extractPlacementSide(url: string): 'left' | 'right' | null {
  try {
    const urlObj = new URL(url);
    const side = urlObj.searchParams.get('side');
    
    if (side === 'left' || side === 'right') {
      return side;
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

// Generate referral links for a user
export function generateReferralLinks(referralId: string, baseUrl: string): {
  general: string;
  left: string;
  right: string;
} {
  const baseRegisterUrl = `${baseUrl}/register`;
  
  return {
    general: `${baseRegisterUrl}?ref=${referralId}`,
    left: `${baseRegisterUrl}?ref=${referralId}&side=left`,
    right: `${baseRegisterUrl}?ref=${referralId}&side=right`,
  };
}

// Validate placement request
export async function validatePlacementRequest(
  referrerId: string,
  placementSide?: 'left' | 'right'
): Promise<{
  isValid: boolean;
  canPlace: boolean;
  suggestedSide?: 'LEFT' | 'RIGHT';
  error?: string;
}> {
  try {
    const referrer = await prisma.user.findUnique({
      where: { id: referrerId },
      select: { isActive: true },
    });

    if (!referrer) {
      return { isValid: false, canPlace: false, error: 'Referrer not found' };
    }

    if (!referrer.isActive) {
      return { isValid: false, canPlace: false, error: 'Referrer account is inactive' };
    }

    // If no specific side requested, always valid (will use weaker leg logic)
    if (!placementSide) {
      return { isValid: true, canPlace: true };
    }

    // Check if specific side has available spots
    const { findNextAvailableSpotInLeg } = await import('./referral');
    const targetSide = placementSide.toUpperCase() as 'LEFT' | 'RIGHT';
    const availableSpot = await findNextAvailableSpotInLeg(referrerId, targetSide);

    if (availableSpot) {
      return { isValid: true, canPlace: true, suggestedSide: targetSide };
    }

    // If requested side is full, suggest the other side
    const alternateSide: 'LEFT' | 'RIGHT' = targetSide === 'LEFT' ? 'RIGHT' : 'LEFT';
    const alternateSpot = await findNextAvailableSpotInLeg(referrerId, alternateSide);

    if (alternateSpot) {
      return { 
        isValid: true, 
        canPlace: true, 
        suggestedSide: alternateSide,
        error: `Requested ${placementSide} side is full, placing in ${alternateSide.toLowerCase()} side`
      };
    }

    return { 
      isValid: true, 
      canPlace: true, 
      error: 'Both sides are full, using optimal placement algorithm'
    };

  } catch (error) {
    console.error('Placement validation error:', error);
    return { isValid: false, canPlace: false, error: 'Validation failed' };
  }
}

// Track referral registration for analytics
export async function trackReferralRegistration(data: {
  referrerId: string;
  newUserId: string;
  placementSide: 'LEFT' | 'RIGHT';
  requestedSide?: 'left' | 'right';
  ipAddress?: string;
  userAgent?: string;
}): Promise<void> {
  try {
    const { systemLogDb } = await import('./database');
    
    await systemLogDb.create({
      action: 'REFERRAL_REGISTRATION',
      userId: data.newUserId,
      details: {
        referrerId: data.referrerId,
        placementSide: data.placementSide,
        requestedSide: data.requestedSide,
        timestamp: new Date().toISOString(),
      },
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
    });
  } catch (error) {
    console.error('Referral registration tracking error:', error);
    // Don't throw error as this is supplementary
  }
}

// Get referral statistics for a user
export async function getReferralStats(userId: string): Promise<{
  totalReferrals: number;
  activeReferrals: number;
  recentReferrals: number; // Last 30 days
  conversionRate: number; // Active / Total
}> {
  try {
    const totalReferrals = await prisma.user.count({
      where: { referrerId: userId },
    });

    const activeReferrals = await prisma.user.count({
      where: { 
        referrerId: userId,
        isActive: true,
      },
    });

    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentReferrals = await prisma.user.count({
      where: { 
        referrerId: userId,
        createdAt: { gte: thirtyDaysAgo },
      },
    });

    const conversionRate = totalReferrals > 0 ? (activeReferrals / totalReferrals) * 100 : 0;

    return {
      totalReferrals,
      activeReferrals,
      recentReferrals,
      conversionRate,
    };
  } catch (error) {
    console.error('Referral stats error:', error);
    return {
      totalReferrals: 0,
      activeReferrals: 0,
      recentReferrals: 0,
      conversionRate: 0,
    };
  }
}
