'use client';

import React from 'react';

interface IconProps {
  className?: string;
  size?: number;
}

export const MiningRig: React.FC<IconProps> = ({ className, size = 24 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect x="2" y="6" width="20" height="12" rx="2" stroke="currentColor" strokeWidth="2" fill="none"/>
      <rect x="4" y="8" width="3" height="3" rx="0.5" fill="currentColor"/>
      <rect x="8" y="8" width="3" height="3" rx="0.5" fill="currentColor"/>
      <rect x="12" y="8" width="3" height="3" rx="0.5" fill="currentColor"/>
      <rect x="16" y="8" width="3" height="3" rx="0.5" fill="currentColor"/>
      <rect x="4" y="12" width="3" height="3" rx="0.5" fill="currentColor"/>
      <rect x="8" y="12" width="3" height="3" rx="0.5" fill="currentColor"/>
      <rect x="12" y="12" width="3" height="3" rx="0.5" fill="currentColor"/>
      <rect x="16" y="12" width="3" height="3" rx="0.5" fill="currentColor"/>
      <circle cx="20" cy="4" r="1" fill="currentColor"/>
      <line x1="20" y1="4" x2="20" y2="6" stroke="currentColor" strokeWidth="2"/>
      <line x1="18" y1="2" x2="22" y2="2" stroke="currentColor" strokeWidth="2"/>
      <line x1="19" y1="1" x2="21" y2="3" stroke="currentColor" strokeWidth="1"/>
      <line x1="21" y1="1" x2="19" y2="3" stroke="currentColor" strokeWidth="1"/>
    </svg>
  );
};
