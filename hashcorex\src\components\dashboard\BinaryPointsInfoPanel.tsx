'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardH<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui';
import { Grid } from '@/components/layout';
import { 
  Clock, 
  TrendingUp, 
  AlertTriangle, 
  Info, 
  Award,
  BarChart3,
  Target,
  Zap,
  Calendar,
  DollarSign
} from 'lucide-react';
import { formatCurrency, formatNumber, getTimeUntilBinaryPayout, formatDate } from '@/lib/utils';
import { useClientTime } from '@/hooks/useClientOnly';

interface BinaryPointsInfo {
  currentPoints: {
    left: number;
    right: number;
    leftCapped: number;
    rightCapped: number;
    matchable: number;
    matched: number;
    totalMatched: number;
  };
  limits: {
    maxPointsPerSide: number;
    pointValue: number;
  };
  progress: {
    leftProgress: number;
    rightProgress: number;
    leftNearCap: boolean;
    rightNearCap: boolean;
  };
  pressureOut: {
    leftAmount: number;
    rightAmount: number;
    totalAmount: number;
    willOccur: boolean;
  };
  earnings: {
    estimatedPayout: number;
    pointValue: number;
    matchablePoints: number;
  };
  history: {
    recentMatches: Array<{
      amount: number;
      date: string;
      description: string;
    }>;
    averageWeeklyEarnings: number;
  };
  warnings: string[];
  hasWarnings: boolean;
  nextMatching: {
    schedule: string;
    description: string;
  };
}

export const BinaryPointsInfoPanel: React.FC = () => {
  const [binaryInfo, setBinaryInfo] = useState<BinaryPointsInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const timeUntilMatching = useClientTime(getTimeUntilBinaryPayout) || { days: 0, hours: 0, minutes: 0, seconds: 0 };

  useEffect(() => {
    fetchBinaryInfo();
  }, []);

  const fetchBinaryInfo = async () => {
    try {
      const response = await fetch('/api/binary-points/info', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setBinaryInfo(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch binary points info:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-32 bg-gray-200 rounded-xl"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!binaryInfo) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">Failed to load binary points information</p>
        </CardContent>
      </Card>
    );
  }

  const ProgressBar = ({ value, max, color = 'bg-green-500', warningThreshold = 90 }: {
    value: number;
    max: number;
    color?: string;
    warningThreshold?: number;
  }) => {
    const percentage = (value / max) * 100;
    const isWarning = percentage >= warningThreshold;
    
    return (
      <div className="w-full bg-gray-200 rounded-full h-3">
        <div 
          className={`h-3 rounded-full transition-all duration-300 ${
            isWarning ? 'bg-red-500' : color
          }`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Warnings Section */}
      {binaryInfo.hasWarnings && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-800">
              <AlertTriangle className="h-5 w-5" />
              Important Notices
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {binaryInfo.warnings.map((warning, index) => (
                <li key={index} className="flex items-start gap-2 text-orange-700">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-sm">{warning}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Current Binary Points Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-green-600" />
            Current Binary Points Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Grid cols={{ default: 1, md: 2 }} gap={6}>
            {/* Left Side */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Left Side Points</span>
                <span className="text-lg font-bold text-green-600">
                  {formatNumber(binaryInfo.currentPoints.left, 0)}
                </span>
              </div>
              <ProgressBar 
                value={binaryInfo.currentPoints.leftCapped} 
                max={binaryInfo.limits.maxPointsPerSide}
                color="bg-green-500"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>0</span>
                <span>{formatNumber(binaryInfo.limits.maxPointsPerSide, 0)} (cap)</span>
              </div>
              {binaryInfo.pressureOut.leftAmount > 0 && (
                <p className="text-xs text-red-600">
                  {formatNumber(binaryInfo.pressureOut.leftAmount, 0)} points will be flushed
                </p>
              )}
            </div>

            {/* Right Side */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Right Side Points</span>
                <span className="text-lg font-bold text-blue-600">
                  {formatNumber(binaryInfo.currentPoints.right, 0)}
                </span>
              </div>
              <ProgressBar 
                value={binaryInfo.currentPoints.rightCapped} 
                max={binaryInfo.limits.maxPointsPerSide}
                color="bg-blue-500"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>0</span>
                <span>{formatNumber(binaryInfo.limits.maxPointsPerSide, 0)} (cap)</span>
              </div>
              {binaryInfo.pressureOut.rightAmount > 0 && (
                <p className="text-xs text-red-600">
                  {formatNumber(binaryInfo.pressureOut.rightAmount, 0)} points will be flushed
                </p>
              )}
            </div>
          </Grid>

          {/* Matchable Points */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Target className="h-5 w-5 text-purple-600" />
                <span className="font-medium text-gray-900">Points Available for Matching</span>
              </div>
              <span className="text-2xl font-bold text-purple-600">
                {formatNumber(binaryInfo.currentPoints.matchable, 0)}
              </span>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Minimum of left ({formatNumber(binaryInfo.currentPoints.leftCapped, 0)}) and right ({formatNumber(binaryInfo.currentPoints.rightCapped, 0)}) sides
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Next Matching Cycle */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-600" />
            Next Weekly Binary Matching
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center">
            <p className="text-sm text-gray-600 mb-4">
              {binaryInfo.nextMatching.schedule}
            </p>
            <div className="grid grid-cols-4 gap-4 mb-4" suppressHydrationWarning>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {timeUntilMatching.days}
                </div>
                <div className="text-xs text-gray-500">Days</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {timeUntilMatching.hours}
                </div>
                <div className="text-xs text-gray-500">Hours</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {timeUntilMatching.minutes}
                </div>
                <div className="text-xs text-gray-500">Minutes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {timeUntilMatching.seconds}
                </div>
                <div className="text-xs text-gray-500">Seconds</div>
              </div>
            </div>
            <p className="text-xs text-gray-500">
              {binaryInfo.nextMatching.description}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Earnings Estimation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5 text-green-600" />
            Earnings Estimation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <span className="font-medium text-green-800">Estimated Weekly Payout</span>
              <span className="text-xl font-bold text-green-600">
                {formatCurrency(binaryInfo.earnings.estimatedPayout)}
              </span>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Point Value:</span>
                <div className="font-semibold">{formatCurrency(binaryInfo.earnings.pointValue)} per point</div>
              </div>
              <div>
                <span className="text-gray-600">Your Matchable Points:</span>
                <div className="font-semibold">{formatNumber(binaryInfo.earnings.matchablePoints, 0)}</div>
              </div>
              <div>
                <span className="text-gray-600">Estimated Payout:</span>
                <div className="font-semibold">{formatCurrency(binaryInfo.earnings.estimatedPayout)}</div>
              </div>
              <div>
                <span className="text-gray-600">Max Points Per Side:</span>
                <div className="font-semibold">{formatNumber(binaryInfo.limits.maxPointsPerSide, 0)}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Historical Performance */}
      {binaryInfo.history.recentMatches.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              Recent Binary Matching History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <span className="font-medium text-purple-800">Average Weekly Earnings</span>
                <span className="text-lg font-bold text-purple-600">
                  {formatCurrency(binaryInfo.history.averageWeeklyEarnings)}
                </span>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Last 4 Weeks</h4>
                {binaryInfo.history.recentMatches.map((match, index) => (
                  <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                    <div>
                      <div className="font-medium">{formatCurrency(match.amount)}</div>
                      <div className="text-xs text-gray-500">
                        {formatDate(match.date)}
                      </div>
                    </div>
                    <div className="text-xs text-gray-600 max-w-xs text-right">
                      {match.description}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Binary Network Education */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5 text-blue-600" />
            How Binary Matching Works
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm text-gray-700">
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Point Accumulation</h4>
              <ul className="space-y-1 ml-4">
                <li>• Every $1 invested by your downline = 1 binary point</li>
                <li>• Points are added to left or right side based on placement</li>
                <li>• Maximum {formatNumber(binaryInfo.limits.maxPointsPerSide, 0)} points per side</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Weekly Matching Process</h4>
              <ul className="space-y-1 ml-4">
                <li>• Matching occurs every Saturday at 15:00 UTC</li>
                <li>• Matched points = minimum of left and right sides</li>
                <li>• Fixed earnings: {formatCurrency(binaryInfo.limits.pointValue)} per matched point</li>
                <li>• Excess points beyond cap are flushed (pressure-out)</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900">Pressure-Out System</h4>
              <ul className="space-y-1 ml-4">
                <li>• Points exceeding {formatNumber(binaryInfo.limits.maxPointsPerSide, 0)} per side are automatically flushed</li>
                <li>• Encourages balanced team building on both sides</li>
                <li>• Prevents point hoarding and ensures fair distribution</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
