import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { systemLogDb } from '@/lib/database';

// POST - Bulk KYC operations (approve/reject multiple users)
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { userIds, action, rejectionReason } = body;

    // Validation
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'User IDs array is required' },
        { status: 400 }
      );
    }

    if (!action || !['APPROVE', 'REJECT'].includes(action)) {
      return NextResponse.json(
        { success: false, error: 'Invalid action' },
        { status: 400 }
      );
    }

    if (action === 'REJECT' && !rejectionReason) {
      return NextResponse.json(
        { success: false, error: 'Rejection reason is required' },
        { status: 400 }
      );
    }

    const newStatus = action === 'APPROVE' ? 'APPROVED' : 'REJECTED';
    const reviewedAt = new Date();
    const results = [];

    // Process each user
    for (const userId of userIds) {
      try {
        // Get user's pending KYC documents
        const userDocuments = await prisma.kYCDocument.findMany({
          where: {
            userId,
            status: 'PENDING',
          },
        });

        if (userDocuments.length === 0) {
          results.push({
            userId,
            success: false,
            error: 'No pending documents found',
          });
          continue;
        }

        // Update all user's documents
        await prisma.kYCDocument.updateMany({
          where: {
            userId,
            status: 'PENDING',
          },
          data: {
            status: newStatus,
            reviewedAt,
            reviewedBy: user.email,
            rejectionReason: action === 'REJECT' ? rejectionReason : null,
          },
        });

        // Update user's KYC status
        await prisma.user.update({
          where: { id: userId },
          data: { kycStatus: newStatus },
        });

        // Log the review action
        await systemLogDb.create({
          action: 'KYC_BULK_REVIEWED',
          adminId: user.id,
          details: {
            reviewedUserId: userId,
            action,
            rejectionReason: action === 'REJECT' ? rejectionReason : null,
            documentsCount: userDocuments.length,
            reviewedBy: user.email,
            bulkOperation: true,
          },
          ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
        });

        results.push({
          userId,
          success: true,
          status: newStatus,
        });

      } catch (error) {
        console.error(`Error processing user ${userId}:`, error);
        results.push({
          userId,
          success: false,
          error: 'Processing failed',
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return NextResponse.json({
      success: true,
      message: `Bulk operation completed. ${successCount} successful, ${failureCount} failed.`,
      data: {
        action,
        totalProcessed: userIds.length,
        successCount,
        failureCount,
        results,
      },
    });

  } catch (error: any) {
    console.error('Bulk KYC operation error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}
