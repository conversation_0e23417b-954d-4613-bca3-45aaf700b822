/*
  Warnings:

  - The values [ID] on the enum `DocumentType` will be removed. If these variants are still used in the database, this will fail.

*/
-- CreateEnum
CREATE TYPE "IDType" AS ENUM ('NATIONAL_ID', 'PASSPORT', 'DRIVING_LICENSE');

-- CreateEnum
CREATE TYPE "DocumentSide" AS ENUM ('FRONT', 'BACK');

-- <PERSON><PERSON>Enum
CREATE TYPE "DepositStatus" AS ENUM ('PENDING', 'VERIFYING', 'CONFIRMED', 'COMPLETED', 'FAILED', 'REJECTED');

-- AlterEnum
BEGIN;
CREATE TYPE "DocumentType_new" AS ENUM ('ID_DOCUMENT', 'SELFIE');
ALTER TABLE "kyc_documents" ALTER COLUMN "documentType" TYPE "DocumentType_new" USING ("documentType"::text::"DocumentType_new");
ALTER TYPE "DocumentType" RENAME TO "DocumentType_old";
ALTER TYPE "DocumentType_new" RENAME TO "DocumentType";
DROP TYPE "DocumentType_old";
COMMIT;

-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "TransactionType" ADD VALUE 'ADMIN_CREDIT';
ALTER TYPE "TransactionType" ADD VALUE 'ADMIN_DEBIT';

-- AlterTable
ALTER TABLE "kyc_documents" ADD COLUMN     "documentSide" "DocumentSide",
ADD COLUMN     "idType" "IDType";

-- CreateTable
CREATE TABLE "wallet_balances" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "availableBalance" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "pendingBalance" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "totalDeposits" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "totalWithdrawals" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "totalEarnings" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "lastUpdated" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "wallet_balances_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "deposit_transactions" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "transactionId" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "usdtAmount" DOUBLE PRECISION NOT NULL,
    "tronAddress" TEXT NOT NULL,
    "senderAddress" TEXT,
    "status" "DepositStatus" NOT NULL DEFAULT 'PENDING',
    "blockNumber" TEXT,
    "blockTimestamp" TIMESTAMP(3),
    "confirmations" INTEGER NOT NULL DEFAULT 0,
    "verifiedAt" TIMESTAMP(3),
    "processedAt" TIMESTAMP(3),
    "failureReason" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "deposit_transactions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "wallet_balances_userId_key" ON "wallet_balances"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "deposit_transactions_transactionId_key" ON "deposit_transactions"("transactionId");

-- CreateIndex
CREATE INDEX "deposit_transactions_userId_idx" ON "deposit_transactions"("userId");

-- CreateIndex
CREATE INDEX "deposit_transactions_transactionId_idx" ON "deposit_transactions"("transactionId");

-- CreateIndex
CREATE INDEX "deposit_transactions_status_idx" ON "deposit_transactions"("status");

-- CreateIndex
CREATE INDEX "deposit_transactions_createdAt_idx" ON "deposit_transactions"("createdAt");

-- AddForeignKey
ALTER TABLE "wallet_balances" ADD CONSTRAINT "wallet_balances_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "deposit_transactions" ADD CONSTRAINT "deposit_transactions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
