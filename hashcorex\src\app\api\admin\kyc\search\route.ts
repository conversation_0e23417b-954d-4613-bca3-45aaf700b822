import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Search and filter KYC submissions for admin
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const searchTerm = url.searchParams.get('search') || '';
    const status = url.searchParams.get('status') || 'ALL';
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const sortBy = url.searchParams.get('sortBy') || 'createdAt';
    const sortOrder = url.searchParams.get('sortOrder') || 'desc';

    // Build where clause for users
    const userWhere: any = {};
    
    if (searchTerm) {
      userWhere.OR = [
        { firstName: { contains: searchTerm, mode: 'insensitive' } },
        { lastName: { contains: searchTerm, mode: 'insensitive' } },
        { email: { contains: searchTerm, mode: 'insensitive' } },
        { referralId: { contains: searchTerm, mode: 'insensitive' } },
      ];
    }

    if (status !== 'ALL') {
      userWhere.kycStatus = status;
    }

    // Get users with KYC documents
    const users = await prisma.user.findMany({
      where: {
        ...userWhere,
        kycDocuments: {
          some: {}, // Only users who have uploaded at least one document
        },
      },
      include: {
        kycDocuments: {
          orderBy: { createdAt: 'desc' },
        },
      },
      orderBy: {
        [sortBy]: sortOrder as 'asc' | 'desc',
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await prisma.user.count({
      where: {
        ...userWhere,
        kycDocuments: {
          some: {},
        },
      },
    });

    // Transform data
    const result = users.map(user => ({
      userId: user.id,
      user: {
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        referralId: user.referralId,
      },
      documents: user.kycDocuments.map(doc => ({
        id: doc.id,
        documentType: doc.documentType,
        idType: doc.idType,
        documentSide: doc.documentSide,
        documentUrl: doc.filePath,
        status: doc.status,
        submittedAt: doc.createdAt.toISOString(),
        reviewedAt: doc.reviewedAt?.toISOString(),
        rejectionReason: doc.rejectionReason,
      })),
      status: user.kycStatus,
      submittedAt: user.kycDocuments[0]?.createdAt.toISOString() || user.createdAt.toISOString(),
    }));

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      success: true,
      data: {
        users: result,
        pagination: {
          currentPage: page,
          totalPages,
          totalCount,
          hasNext: page < totalPages,
          hasPrev: page > 1,
          limit,
        },
        filters: {
          searchTerm,
          status,
          sortBy,
          sortOrder,
        },
      },
    });

  } catch (error: any) {
    console.error('KYC search error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to search KYC data' },
      { status: 500 }
    );
  }
}
