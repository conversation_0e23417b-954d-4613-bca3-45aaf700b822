import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { adminSettingsDb } from '@/lib/database';

// GET - Fetch withdrawal settings for users
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get withdrawal settings
    const minWithdrawal = parseFloat(await adminSettingsDb.get('minWithdrawalAmount') || '10');
    const fixedFee = parseFloat(await adminSettingsDb.get('withdrawalFeeFixed') || '3');
    const percentageFee = parseFloat(await adminSettingsDb.get('withdrawalFeePercentage') || '1');
    const processingDays = parseInt(await adminSettingsDb.get('withdrawalProcessingDays') || '3');

    return NextResponse.json({
      success: true,
      data: {
        minWithdrawalAmount: minWithdrawal,
        fixedFee: fixedFee,
        percentageFee: percentageFee,
        processingDays: processingDays,
      },
    });

  } catch (error: any) {
    console.error('Withdrawal settings fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch withdrawal settings' },
      { status: 500 }
    );
  }
}
