import {
  depositTransactionDb,
  walletBalanceDb,
  transactionDb,
  adminSettingsDb,
  systemLogDb
} from '@/lib/database';
import { prisma } from '@/lib/prisma';
import { verifyUSDTTransaction } from '@/lib/trongrid';
import { DepositStatus } from '@/types';

// Map to track active verification processes to prevent duplicates
const activeVerifications = new Map<string, boolean>();

// Map to track confirmation checking intervals
const confirmationIntervals = new Map<string, NodeJS.Timeout>();

/**
 * Background service for automated USDT deposit verification
 */
export class DepositVerificationService {
  private static instance: DepositVerificationService;
  private isRunning = false;

  static getInstance(): DepositVerificationService {
    if (!DepositVerificationService.instance) {
      DepositVerificationService.instance = new DepositVerificationService();
    }
    return DepositVerificationService.instance;
  }

  /**
   * Start the background verification service
   */
  async start() {
    if (this.isRunning) {
      console.log('Deposit verification service is already running');
      return;
    }

    this.isRunning = true;
    console.log('Starting deposit verification service...');

    // Process existing pending verification deposits
    await this.processPendingVerifications();

    // Process existing waiting for confirmations deposits
    await this.processWaitingForConfirmations();

    console.log('Deposit verification service started successfully');
  }

  /**
   * Stop the background verification service
   */
  stop() {
    this.isRunning = false;
    
    // Clear all active intervals
    confirmationIntervals.forEach((interval) => {
      clearTimeout(interval);
    });
    confirmationIntervals.clear();
    activeVerifications.clear();

    console.log('Deposit verification service stopped');
  }

  /**
   * Process deposits with PENDING_VERIFICATION status
   */
  private async processPendingVerifications() {
    try {
      const pendingDeposits = await depositTransactionDb.getPendingVerificationDeposits();
      console.log(`Found ${pendingDeposits.length} deposits pending verification`);

      for (const deposit of pendingDeposits) {
        if (!activeVerifications.has(deposit.transactionId)) {
          this.scheduleVerification(deposit.transactionId, deposit.tronAddress);
        }
      }
    } catch (error) {
      console.error('Error processing pending verifications:', error);
      await systemLogDb.create({
        action: 'DEPOSIT_VERIFICATION_ERROR',
        details: `Error processing pending verifications: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    }
  }

  /**
   * Process deposits with WAITING_FOR_CONFIRMATIONS status
   */
  private async processWaitingForConfirmations() {
    try {
      const waitingDeposits = await depositTransactionDb.getWaitingForConfirmationsDeposits();
      console.log(`Found ${waitingDeposits.length} deposits waiting for confirmations`);

      for (const deposit of waitingDeposits) {
        if (!confirmationIntervals.has(deposit.transactionId)) {
          this.scheduleConfirmationCheck(deposit.transactionId, deposit.tronAddress);
        }
      }
    } catch (error) {
      console.error('Error processing waiting for confirmations:', error);
      await systemLogDb.create({
        action: 'CONFIRMATION_CHECK_ERROR',
        details: `Error processing waiting for confirmations: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    }
  }

  /**
   * Schedule verification for a transaction (with 60-second retry)
   */
  private scheduleVerification(transactionId: string, tronAddress: string) {
    if (activeVerifications.has(transactionId)) {
      return; // Already being processed
    }

    activeVerifications.set(transactionId, true);
    console.log(`Scheduling verification for transaction: ${transactionId}`);

    // Immediate verification attempt
    this.verifyTransaction(transactionId, tronAddress, false);

    // Schedule retry after 60 seconds if not found
    setTimeout(() => {
      this.verifyTransaction(transactionId, tronAddress, true);
    }, 60000);
  }

  /**
   * Verify a single transaction
   */
  private async verifyTransaction(transactionId: string, tronAddress: string, isRetry: boolean) {
    try {
      console.log(`${isRetry ? 'Retrying' : 'Attempting'} verification for transaction: ${transactionId}`);

      // Get minimum confirmations setting
      const minConfirmations = parseInt(await adminSettingsDb.get('minConfirmations') || '10');

      // Get deposit settings for validation
      const minDepositAmount = parseFloat(await adminSettingsDb.get('minDepositAmount') || '10');
      const maxDepositAmount = parseFloat(await adminSettingsDb.get('maxDepositAmount') || '10000');

      // Verify the transaction with timeout
      const verificationPromise = verifyUSDTTransaction(transactionId, tronAddress, 1);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Verification timeout')), 30000)
      );

      const verificationResult = await Promise.race([verificationPromise, timeoutPromise]) as any;

      if (!verificationResult.isValid && verificationResult.confirmations === 0) {
        if (isRetry) {
          // Final attempt failed - mark as failed
          await depositTransactionDb.updateStatus(transactionId, 'FAILED', {
            failureReason: 'Transaction not found or invalid after verification attempts',
            processedAt: new Date(),
          });

          await systemLogDb.create({
            action: 'DEPOSIT_VERIFICATION_FAILED',
            details: `Transaction ${transactionId} failed verification after retry`,
          });

          activeVerifications.delete(transactionId);
        }
        return;
      }

      // Validate recipient address
      const hasValidRecipient = verificationResult.toAddress.toLowerCase().includes(tronAddress.toLowerCase().slice(1, 10)) ||
                                tronAddress.toLowerCase().includes(verificationResult.toAddress.toLowerCase().slice(1, 10));

      if (!hasValidRecipient) {
        await depositTransactionDb.updateStatus(transactionId, 'FAILED', {
          failureReason: 'Invalid recipient address',
          processedAt: new Date(),
        });
        activeVerifications.delete(transactionId);
        return;
      }

      // Validate deposit amount
      if (verificationResult.amount < minDepositAmount) {
        await depositTransactionDb.updateStatus(transactionId, 'FAILED', {
          failureReason: `Deposit amount ${verificationResult.amount} USDT is below minimum ${minDepositAmount} USDT`,
          processedAt: new Date(),
        });
        activeVerifications.delete(transactionId);
        return;
      }

      if (verificationResult.amount > maxDepositAmount) {
        await depositTransactionDb.updateStatus(transactionId, 'FAILED', {
          failureReason: `Deposit amount ${verificationResult.amount} USDT exceeds maximum ${maxDepositAmount} USDT`,
          processedAt: new Date(),
        });
        activeVerifications.delete(transactionId);
        return;
      }

      // Transaction found and validated - update with verification details
      // First update the deposit record with transaction details
      await prisma.depositTransaction.update({
        where: { transactionId },
        data: {
          amount: verificationResult.amount,
          usdtAmount: verificationResult.amount,
          senderAddress: verificationResult.fromAddress,
          blockNumber: verificationResult.blockNumber.toString(),
          blockTimestamp: new Date(verificationResult.blockTimestamp),
          confirmations: verificationResult.confirmations,
        },
      });

      await depositTransactionDb.updateStatus(transactionId, 'PENDING', {
        confirmations: verificationResult.confirmations,
      });

      console.log(`Transaction ${transactionId} verified with ${verificationResult.confirmations} confirmations (required: ${minConfirmations})`);

      // Check if it has enough confirmations
      if (verificationResult.confirmations >= minConfirmations) {
        await this.completeDeposit(transactionId, verificationResult.amount);
      } else {
        // Not enough confirmations - start confirmation checking
        await depositTransactionDb.updateStatus(transactionId, 'WAITING_FOR_CONFIRMATIONS');
        this.scheduleConfirmationCheck(transactionId, tronAddress);
      }

      activeVerifications.delete(transactionId);

    } catch (error) {
      console.error(`Error verifying transaction ${transactionId}:`, error);

      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const isNetworkError = errorMessage.includes('timeout') || errorMessage.includes('network') || errorMessage.includes('ECONNRESET');

      if (isRetry || !isNetworkError) {
        // Final attempt failed due to error or non-network error
        await depositTransactionDb.updateStatus(transactionId, 'FAILED', {
          failureReason: `Verification error: ${errorMessage}`,
          processedAt: new Date(),
        });
        activeVerifications.delete(transactionId);

        await systemLogDb.create({
          action: 'DEPOSIT_VERIFICATION_FAILED',
          details: `Transaction ${transactionId} failed verification: ${errorMessage}`,
        });
      } else {
        // Network error on first attempt - will retry in 60 seconds
        await systemLogDb.create({
          action: 'DEPOSIT_VERIFICATION_NETWORK_ERROR',
          details: `Network error verifying transaction ${transactionId}: ${errorMessage}. Will retry.`,
        });
      }
    }
  }

  /**
   * Schedule confirmation checking for a transaction
   */
  private scheduleConfirmationCheck(transactionId: string, tronAddress: string) {
    if (confirmationIntervals.has(transactionId)) {
      return; // Already being checked
    }

    console.log(`Starting confirmation checking for transaction: ${transactionId}`);

    const interval = setInterval(async () => {
      await this.checkConfirmations(transactionId, tronAddress);
    }, 60000); // Check every 60 seconds

    confirmationIntervals.set(transactionId, interval);

    // Also check immediately
    this.checkConfirmations(transactionId, tronAddress);
  }

  /**
   * Check confirmations for a transaction
   */
  private async checkConfirmations(transactionId: string, tronAddress: string) {
    try {
      console.log(`Checking confirmations for transaction: ${transactionId}`);

      // Get minimum confirmations setting
      const minConfirmations = parseInt(await adminSettingsDb.get('minConfirmations') || '10');

      // Re-verify to get current confirmation count
      const verificationResult = await verifyUSDTTransaction(transactionId, tronAddress, 1);

      if (!verificationResult.isValid) {
        console.log(`Transaction ${transactionId} is no longer valid during confirmation check`);
        return;
      }

      // Update confirmation count
      await depositTransactionDb.updateConfirmations(transactionId, verificationResult.confirmations);

      console.log(`Transaction ${transactionId} has ${verificationResult.confirmations} confirmations (required: ${minConfirmations})`);

      // Check if it now has enough confirmations
      if (verificationResult.confirmations >= minConfirmations) {
        await this.completeDeposit(transactionId, verificationResult.amount);
        
        // Stop checking confirmations for this transaction
        const interval = confirmationIntervals.get(transactionId);
        if (interval) {
          clearInterval(interval);
          confirmationIntervals.delete(transactionId);
        }
      }

    } catch (error) {
      console.error(`Error checking confirmations for transaction ${transactionId}:`, error);
      await systemLogDb.create({
        action: 'CONFIRMATION_CHECK_ERROR',
        details: `Error checking confirmations for ${transactionId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    }
  }

  /**
   * Complete a deposit by crediting the user's wallet
   */
  private async completeDeposit(transactionId: string, amount: number) {
    try {
      console.log(`Completing deposit for transaction: ${transactionId} with amount: ${amount}`);

      // Get deposit record to get user ID
      const deposit = await depositTransactionDb.findByTransactionId(transactionId);
      if (!deposit) {
        throw new Error('Deposit record not found');
      }

      // Check if deposit is already completed to prevent double processing
      if (deposit.status === 'CONFIRMED' || deposit.status === 'COMPLETED') {
        console.log(`Deposit ${transactionId} already completed, skipping...`);
        return;
      }

      // Use database transaction to ensure atomicity
      await prisma.$transaction(async (tx) => {
        // Update deposit status to confirmed
        await tx.depositTransaction.update({
          where: { transactionId },
          data: {
            status: 'CONFIRMED',
            verifiedAt: new Date(),
            processedAt: new Date(),
          },
        });

        // Get current wallet balance
        const currentWallet = await tx.walletBalance.findUnique({
          where: { userId: deposit.userId },
        });

        if (!currentWallet) {
          // Create wallet if it doesn't exist
          await tx.walletBalance.create({
            data: {
              userId: deposit.userId,
              availableBalance: amount,
              pendingBalance: 0,
              totalDeposits: amount,
              totalWithdrawals: 0,
              totalEarnings: 0,
            },
          });
        } else {
          // Update existing wallet
          await tx.walletBalance.update({
            where: { userId: deposit.userId },
            data: {
              availableBalance: currentWallet.availableBalance + amount,
              totalDeposits: currentWallet.totalDeposits + amount,
              lastUpdated: new Date(),
            },
          });
        }

        // Create transaction record for balance tracking
        await tx.transaction.create({
          data: {
            userId: deposit.userId,
            type: 'DEPOSIT',
            amount: amount,
            description: `USDT TRC20 Deposit - TX: ${transactionId}`,
            status: 'COMPLETED',
          },
        });
      });

      await systemLogDb.create({
        action: 'DEPOSIT_COMPLETED',
        userId: deposit.userId,
        details: `Deposit completed: ${amount} USDT from transaction ${transactionId}`,
      });

      console.log(`Deposit completed successfully for transaction: ${transactionId}`);

    } catch (error) {
      console.error(`Error completing deposit for transaction ${transactionId}:`, error);

      // Mark as failed if completion fails
      await depositTransactionDb.updateStatus(transactionId, 'FAILED', {
        failureReason: `Completion error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        processedAt: new Date(),
      });

      await systemLogDb.create({
        action: 'DEPOSIT_COMPLETION_ERROR',
        details: `Error completing deposit ${transactionId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    }
  }

  /**
   * Add a new transaction for verification
   */
  async addTransactionForVerification(transactionId: string, tronAddress: string) {
    if (!this.isRunning) {
      console.log('Deposit verification service is not running, starting verification manually');
    }

    this.scheduleVerification(transactionId, tronAddress);
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      activeVerifications: activeVerifications.size,
      confirmationChecks: confirmationIntervals.size,
    };
  }
}

// Export singleton instance
export const depositVerificationService = DepositVerificationService.getInstance();
