{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/database.ts"], "sourcesContent": ["import { prisma } from './prisma';\nimport { User, MiningUnit, Transaction, Referral, BinaryPoints, WalletBalance, DepositTransaction, DepositStatus } from '@/types';\n\n// User Database Operations\nexport const userDb = {\n  async create(data: {\n    email: string;\n    firstName: string;\n    lastName: string;\n    password: string;\n    referralId?: string;\n  }) {\n    return await prisma.user.create({\n      data: {\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: data.password,\n        referralId: data.referralId || undefined,\n      },\n    });\n  },\n\n  async findByEmail(email: string) {\n    return await prisma.user.findUnique({\n      where: { email },\n      include: {\n        miningUnits: true,\n        transactions: true,\n        binaryPoints: true,\n      },\n    });\n  },\n\n  async findById(id: string) {\n    return await prisma.user.findUnique({\n      where: { id },\n      include: {\n        miningUnits: true,\n        transactions: true,\n        binaryPoints: true,\n      },\n    });\n  },\n\n  async findByReferralId(referralId: string) {\n    return await prisma.user.findUnique({\n      where: { referralId },\n    });\n  },\n\n  async update(id: string, data: Partial<{\n    firstName: string;\n    lastName: string;\n    email: string;\n    role: 'USER' | 'ADMIN';\n    isActive: boolean;\n    kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';\n  }>) {\n    return await prisma.user.update({\n      where: { id },\n      data,\n    });\n  },\n\n  async updateKYCStatus(userId: string, status: 'PENDING' | 'APPROVED' | 'REJECTED') {\n    return await prisma.user.update({\n      where: { id: userId },\n      data: { kycStatus: status },\n    });\n  },\n};\n\n// Mining Unit Database Operations\nexport const miningUnitDb = {\n  async create(data: {\n    userId: string;\n    thsAmount: number;\n    investmentAmount: number;\n    dailyROI: number;\n  }) {\n    const expiryDate = new Date();\n    expiryDate.setFullYear(expiryDate.getFullYear() + 2); // 24 months from now\n\n    return await prisma.miningUnit.create({\n      data: {\n        userId: data.userId,\n        thsAmount: data.thsAmount,\n        investmentAmount: data.investmentAmount,\n        dailyROI: data.dailyROI,\n        expiryDate,\n      },\n    });\n  },\n\n  async findActiveByUserId(userId: string) {\n    return await prisma.miningUnit.findMany({\n      where: {\n        userId,\n        status: 'ACTIVE',\n        expiryDate: {\n          gt: new Date(),\n        },\n      },\n    });\n  },\n\n  async updateTotalEarned(unitId: string, amount: number) {\n    return await prisma.miningUnit.update({\n      where: { id: unitId },\n      data: {\n        totalEarned: {\n          increment: amount,\n        },\n      },\n    });\n  },\n\n  async expireUnit(unitId: string) {\n    return await prisma.miningUnit.update({\n      where: { id: unitId },\n      data: { status: 'EXPIRED' },\n    });\n  },\n};\n\n// Transaction Database Operations\nexport const transactionDb = {\n  async create(data: {\n    userId: string;\n    type: 'MINING_EARNINGS' | 'DIRECT_REFERRAL' | 'BINARY_BONUS' | 'DEPOSIT' | 'WITHDRAWAL' | 'PURCHASE' | 'ADMIN_CREDIT' | 'ADMIN_DEBIT';\n    amount: number;\n    description: string;\n    reference?: string;\n    status?: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';\n  }) {\n    return await prisma.transaction.create({\n      data: {\n        userId: data.userId,\n        type: data.type,\n        amount: data.amount,\n        description: data.description,\n        reference: data.reference,\n        status: data.status || 'PENDING',\n      },\n    });\n  },\n\n  async findByUserId(userId: string, filters?: {\n    types?: string[];\n    status?: string;\n    limit?: number;\n    offset?: number;\n    includeUser?: boolean;\n    search?: string;\n  }) {\n    const where: any = { userId };\n\n    if (filters?.types && filters.types.length > 0) {\n      where.type = { in: filters.types };\n    }\n\n    if (filters?.status) {\n      where.status = filters.status;\n    }\n\n    if (filters?.search) {\n      where.OR = [\n        { description: { contains: filters.search, mode: 'insensitive' } },\n        { type: { contains: filters.search, mode: 'insensitive' } },\n        { reference: { contains: filters.search, mode: 'insensitive' } },\n      ];\n    }\n\n    const include = filters?.includeUser ? {\n      user: {\n        select: {\n          id: true,\n          email: true,\n          firstName: true,\n          lastName: true,\n        },\n      },\n    } : undefined;\n\n    return await prisma.transaction.findMany({\n      where,\n      include,\n      orderBy: { createdAt: 'desc' },\n      take: filters?.limit || 50,\n      skip: filters?.offset,\n    });\n  },\n\n  async updateStatus(transactionId: string, status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED') {\n    return await prisma.transaction.update({\n      where: { id: transactionId },\n      data: { status },\n    });\n  },\n};\n\n// Referral Database Operations\nexport const referralDb = {\n  async create(data: {\n    referrerId: string;\n    referredId: string;\n    placementSide: 'LEFT' | 'RIGHT';\n  }) {\n    return await prisma.referral.create({\n      data: {\n        referrerId: data.referrerId,\n        referredId: data.referredId,\n        placementSide: data.placementSide,\n      },\n    });\n  },\n\n  async findByReferrerId(referrerId: string) {\n    return await prisma.referral.findMany({\n      where: { referrerId },\n      include: {\n        referred: {\n          select: {\n            id: true,\n            email: true,\n            createdAt: true,\n          },\n        },\n      },\n    });\n  },\n};\n\n// Binary Points Database Operations\nexport const binaryPointsDb = {\n  async upsert(data: {\n    userId: string;\n    leftPoints?: number;\n    rightPoints?: number;\n  }) {\n    // Round to 2 decimal places to ensure precision\n    const leftPoints = data.leftPoints !== undefined ? Math.round(data.leftPoints * 100) / 100 : undefined;\n    const rightPoints = data.rightPoints !== undefined ? Math.round(data.rightPoints * 100) / 100 : undefined;\n\n    return await prisma.binaryPoints.upsert({\n      where: { userId: data.userId },\n      update: {\n        leftPoints: leftPoints !== undefined ? { increment: leftPoints } : undefined,\n        rightPoints: rightPoints !== undefined ? { increment: rightPoints } : undefined,\n      },\n      create: {\n        userId: data.userId,\n        leftPoints: leftPoints || 0,\n        rightPoints: rightPoints || 0,\n      },\n    });\n  },\n\n  async findByUserId(userId: string) {\n    return await prisma.binaryPoints.findUnique({\n      where: { userId },\n    });\n  },\n\n  async resetPoints(userId: string, leftPoints: number, rightPoints: number) {\n    return await prisma.binaryPoints.update({\n      where: { userId },\n      data: {\n        leftPoints,\n        rightPoints,\n        flushDate: new Date(),\n      },\n    });\n  },\n};\n\n// Withdrawal Database Operations\nexport const withdrawalDb = {\n  async create(data: {\n    userId: string;\n    amount: number;\n    usdtAddress: string;\n  }) {\n    return await prisma.withdrawalRequest.create({\n      data: {\n        userId: data.userId,\n        amount: data.amount,\n        usdtAddress: data.usdtAddress,\n      },\n    });\n  },\n\n  async findPending() {\n    return await prisma.withdrawalRequest.findMany({\n      where: { status: 'PENDING' },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            kycStatus: true,\n          },\n        },\n      },\n      orderBy: { createdAt: 'asc' },\n    });\n  },\n\n  async updateStatus(\n    requestId: string, \n    status: 'APPROVED' | 'REJECTED' | 'COMPLETED',\n    processedBy?: string,\n    txid?: string,\n    rejectionReason?: string\n  ) {\n    return await prisma.withdrawalRequest.update({\n      where: { id: requestId },\n      data: {\n        status,\n        processedBy,\n        txid,\n        rejectionReason,\n        processedAt: new Date(),\n      },\n    });\n  },\n};\n\n// Admin Settings Database Operations\nexport const adminSettingsDb = {\n  async get(key: string) {\n    const setting = await prisma.adminSettings.findUnique({\n      where: { key },\n    });\n    return setting?.value;\n  },\n\n  async set(key: string, value: string, updatedBy?: string) {\n    return await prisma.adminSettings.upsert({\n      where: { key },\n      update: { value },\n      create: { key, value },\n    });\n  },\n\n  async getAll() {\n    return await prisma.adminSettings.findMany();\n  },\n};\n\n// System Logs\nexport const systemLogDb = {\n  async create(data: {\n    action: string;\n    userId?: string;\n    adminId?: string;\n    details?: any;\n    ipAddress?: string;\n    userAgent?: string;\n  }) {\n    return await prisma.systemLog.create({\n      data: {\n        action: data.action,\n        userId: data.userId,\n        adminId: data.adminId,\n        details: data.details ? JSON.stringify(data.details) : null,\n        ipAddress: data.ipAddress,\n        userAgent: data.userAgent,\n      },\n    });\n  },\n};\n\n// Wallet Balance Database Operations\nexport const walletBalanceDb = {\n  async getOrCreate(userId: string): Promise<WalletBalance> {\n    let walletBalance = await prisma.walletBalance.findUnique({\n      where: { userId },\n    });\n\n    if (!walletBalance) {\n      walletBalance = await prisma.walletBalance.create({\n        data: {\n          userId,\n          availableBalance: 0,\n          pendingBalance: 0,\n          totalDeposits: 0,\n          totalWithdrawals: 0,\n          totalEarnings: 0,\n        },\n      });\n    }\n\n    return walletBalance as WalletBalance;\n  },\n\n  async updateBalance(userId: string, updates: {\n    availableBalance?: number;\n    pendingBalance?: number;\n    totalDeposits?: number;\n    totalWithdrawals?: number;\n    totalEarnings?: number;\n  }) {\n    return await prisma.walletBalance.update({\n      where: { userId },\n      data: {\n        ...updates,\n        lastUpdated: new Date(),\n      },\n    });\n  },\n\n  async addDeposit(userId: string, amount: number) {\n    const wallet = await this.getOrCreate(userId);\n    return await prisma.walletBalance.update({\n      where: { userId },\n      data: {\n        availableBalance: wallet.availableBalance + amount,\n        totalDeposits: wallet.totalDeposits + amount,\n        lastUpdated: new Date(),\n      },\n    });\n  },\n\n  async addEarnings(userId: string, amount: number) {\n    const wallet = await this.getOrCreate(userId);\n    return await prisma.walletBalance.update({\n      where: { userId },\n      data: {\n        availableBalance: wallet.availableBalance + amount,\n        totalEarnings: wallet.totalEarnings + amount,\n        lastUpdated: new Date(),\n      },\n    });\n  },\n\n  async deductWithdrawal(userId: string, amount: number) {\n    const wallet = await this.getOrCreate(userId);\n    if (wallet.availableBalance < amount) {\n      throw new Error('Insufficient balance');\n    }\n\n    return await prisma.walletBalance.update({\n      where: { userId },\n      data: {\n        availableBalance: wallet.availableBalance - amount,\n        totalWithdrawals: wallet.totalWithdrawals + amount,\n        lastUpdated: new Date(),\n      },\n    });\n  },\n\n  async findByUserId(userId: string) {\n    return await this.getOrCreate(userId);\n  },\n};\n\n// Deposit Transaction Database Operations\nexport const depositTransactionDb = {\n  async create(data: {\n    userId: string;\n    transactionId: string;\n    amount: number;\n    usdtAmount: number;\n    tronAddress: string;\n    senderAddress?: string;\n    blockNumber?: string;\n    blockTimestamp?: Date;\n    confirmations?: number;\n  }) {\n    return await prisma.depositTransaction.create({\n      data: {\n        userId: data.userId,\n        transactionId: data.transactionId,\n        amount: data.amount,\n        usdtAmount: data.usdtAmount,\n        tronAddress: data.tronAddress,\n        senderAddress: data.senderAddress,\n        blockNumber: data.blockNumber,\n        blockTimestamp: data.blockTimestamp,\n        confirmations: data.confirmations || 0,\n        status: 'PENDING',\n      },\n    });\n  },\n\n  async findByTransactionId(transactionId: string) {\n    return await prisma.depositTransaction.findUnique({\n      where: { transactionId },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n      },\n    });\n  },\n\n  async findByUserId(userId: string, filters?: {\n    status?: DepositStatus;\n    limit?: number;\n    offset?: number;\n  }) {\n    const where: any = { userId };\n\n    if (filters?.status) {\n      where.status = filters.status;\n    }\n\n    return await prisma.depositTransaction.findMany({\n      where,\n      orderBy: { createdAt: 'desc' },\n      take: filters?.limit || 50,\n      skip: filters?.offset,\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n      },\n    });\n  },\n\n  async findAll(filters?: {\n    status?: DepositStatus;\n    limit?: number;\n    offset?: number;\n  }) {\n    const where: any = {};\n\n    if (filters?.status) {\n      where.status = filters.status;\n    }\n\n    return await prisma.depositTransaction.findMany({\n      where,\n      orderBy: { createdAt: 'desc' },\n      take: filters?.limit || 100,\n      skip: filters?.offset,\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n      },\n    });\n  },\n\n  async updateStatus(\n    transactionId: string,\n    status: DepositStatus,\n    updates?: {\n      verifiedAt?: Date;\n      processedAt?: Date;\n      failureReason?: string;\n      confirmations?: number;\n    }\n  ) {\n    const updateData: any = { status };\n\n    if (updates?.verifiedAt) updateData.verifiedAt = updates.verifiedAt;\n    if (updates?.processedAt) updateData.processedAt = updates.processedAt;\n    if (updates?.failureReason) updateData.failureReason = updates.failureReason;\n    if (updates?.confirmations !== undefined) updateData.confirmations = updates.confirmations;\n\n    return await prisma.depositTransaction.update({\n      where: { transactionId },\n      data: updateData,\n    });\n  },\n\n  async markAsCompleted(transactionId: string) {\n    return await this.updateStatus(transactionId, 'COMPLETED', {\n      processedAt: new Date(),\n    });\n  },\n\n  async markAsFailed(transactionId: string, reason: string) {\n    return await this.updateStatus(transactionId, 'FAILED', {\n      failureReason: reason,\n      processedAt: new Date(),\n    });\n  },\n\n  async getPendingDeposits() {\n    return await this.findAll({ status: 'PENDING' });\n  },\n\n  async getPendingVerificationDeposits() {\n    return await this.findAll({ status: 'PENDING_VERIFICATION' });\n  },\n\n  async getWaitingForConfirmationsDeposits() {\n    return await this.findAll({ status: 'WAITING_FOR_CONFIRMATIONS' });\n  },\n\n  async findByStatus(status: DepositStatus) {\n    return await prisma.depositTransaction.findMany({\n      where: { status },\n      orderBy: { createdAt: 'desc' },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n      },\n    });\n  },\n\n  async updateConfirmations(transactionId: string, confirmations: number) {\n    return await prisma.depositTransaction.update({\n      where: { transactionId },\n      data: { confirmations },\n    });\n  },\n\n  async getDepositStats() {\n    const stats = await prisma.depositTransaction.aggregate({\n      _count: {\n        id: true,\n      },\n      _sum: {\n        usdtAmount: true,\n      },\n      where: {\n        status: { in: ['COMPLETED', 'CONFIRMED'] },\n      },\n    });\n\n    const pendingCount = await prisma.depositTransaction.count({\n      where: {\n        status: {\n          in: ['PENDING', 'PENDING_VERIFICATION', 'WAITING_FOR_CONFIRMATIONS']\n        }\n      },\n    });\n\n    return {\n      totalDeposits: stats._count.id || 0,\n      totalAmount: stats._sum.usdtAmount || 0,\n      pendingDeposits: pendingCount,\n    };\n  },\n};\n\n// Support Ticket Database Operations\nexport const supportTicketDb = {\n  create: async (data: any) => {\n    return await prisma.supportTicket.create({\n      data,\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n        responses: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n              },\n            },\n          },\n          orderBy: { createdAt: 'asc' },\n        },\n      },\n    });\n  },\n\n  findByUserId: async (userId: string) => {\n    return await prisma.supportTicket.findMany({\n      where: { userId },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n        responses: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n              },\n            },\n          },\n          orderBy: { createdAt: 'asc' },\n        },\n      },\n      orderBy: { createdAt: 'desc' },\n    });\n  },\n\n  findById: async (id: string) => {\n    return await prisma.supportTicket.findUnique({\n      where: { id },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n        responses: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n              },\n            },\n          },\n          orderBy: { createdAt: 'asc' },\n        },\n      },\n    });\n  },\n\n  findAll: async () => {\n    return await prisma.supportTicket.findMany({\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n        responses: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n              },\n            },\n          },\n          orderBy: { createdAt: 'asc' },\n        },\n      },\n      orderBy: { createdAt: 'desc' },\n    });\n  },\n\n  updateStatus: async (id: string, status: any) => {\n    return await prisma.supportTicket.update({\n      where: { id },\n      data: { status, updatedAt: new Date() },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n        responses: {\n          include: {\n            user: {\n              select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n              },\n            },\n          },\n          orderBy: { createdAt: 'asc' },\n        },\n      },\n    });\n  },\n};\n\n// Ticket Response Database Operations\nexport const ticketResponseDb = {\n  create: async (data: any) => {\n    return await prisma.ticketResponse.create({\n      data,\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n      },\n    });\n  },\n\n  findByTicketId: async (ticketId: string) => {\n    return await prisma.ticketResponse.findMany({\n      where: { ticketId },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n          },\n        },\n      },\n      orderBy: { createdAt: 'asc' },\n    });\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAIO,MAAM,SAAS;IACpB,MAAM,QAAO,IAMZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC9B,MAAM;gBACJ,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU,IAAI;YACjC;QACF;IACF;IAEA,MAAM,aAAY,KAAa;QAC7B,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAClC,OAAO;gBAAE;YAAM;YACf,SAAS;gBACP,aAAa;gBACb,cAAc;gBACd,cAAc;YAChB;QACF;IACF;IAEA,MAAM,UAAS,EAAU;QACvB,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAClC,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,aAAa;gBACb,cAAc;gBACd,cAAc;YAChB;QACF;IACF;IAEA,MAAM,kBAAiB,UAAkB;QACvC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAClC,OAAO;gBAAE;YAAW;QACtB;IACF;IAEA,MAAM,QAAO,EAAU,EAAE,IAOvB;QACA,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC9B,OAAO;gBAAE;YAAG;YACZ;QACF;IACF;IAEA,MAAM,iBAAgB,MAAc,EAAE,MAA2C;QAC/E,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC9B,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBAAE,WAAW;YAAO;QAC5B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,MAAM,QAAO,IAKZ;QACC,MAAM,aAAa,IAAI;QACvB,WAAW,WAAW,CAAC,WAAW,WAAW,KAAK,IAAI,qBAAqB;QAE3E,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,WAAW,KAAK,SAAS;gBACzB,kBAAkB,KAAK,gBAAgB;gBACvC,UAAU,KAAK,QAAQ;gBACvB;YACF;QACF;IACF;IAEA,MAAM,oBAAmB,MAAc;QACrC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACtC,OAAO;gBACL;gBACA,QAAQ;gBACR,YAAY;oBACV,IAAI,IAAI;gBACV;YACF;QACF;IACF;IAEA,MAAM,mBAAkB,MAAc,EAAE,MAAc;QACpD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YACpC,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBACJ,aAAa;oBACX,WAAW;gBACb;YACF;QACF;IACF;IAEA,MAAM,YAAW,MAAc;QAC7B,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YACpC,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBAAE,QAAQ;YAAU;QAC5B;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,QAAO,IAOZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YACrC,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,MAAM,KAAK,IAAI;gBACf,QAAQ,KAAK,MAAM;gBACnB,aAAa,KAAK,WAAW;gBAC7B,WAAW,KAAK,SAAS;gBACzB,QAAQ,KAAK,MAAM,IAAI;YACzB;QACF;IACF;IAEA,MAAM,cAAa,MAAc,EAAE,OAOlC;QACC,MAAM,QAAa;YAAE;QAAO;QAE5B,IAAI,SAAS,SAAS,QAAQ,KAAK,CAAC,MAAM,GAAG,GAAG;YAC9C,MAAM,IAAI,GAAG;gBAAE,IAAI,QAAQ,KAAK;YAAC;QACnC;QAEA,IAAI,SAAS,QAAQ;YACnB,MAAM,MAAM,GAAG,QAAQ,MAAM;QAC/B;QAEA,IAAI,SAAS,QAAQ;YACnB,MAAM,EAAE,GAAG;gBACT;oBAAE,aAAa;wBAAE,UAAU,QAAQ,MAAM;wBAAE,MAAM;oBAAc;gBAAE;gBACjE;oBAAE,MAAM;wBAAE,UAAU,QAAQ,MAAM;wBAAE,MAAM;oBAAc;gBAAE;gBAC1D;oBAAE,WAAW;wBAAE,UAAU,QAAQ,MAAM;wBAAE,MAAM;oBAAc;gBAAE;aAChE;QACH;QAEA,MAAM,UAAU,SAAS,cAAc;YACrC,MAAM;gBACJ,QAAQ;oBACN,IAAI;oBACJ,OAAO;oBACP,WAAW;oBACX,UAAU;gBACZ;YACF;QACF,IAAI;QAEJ,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;YACvC;YACA;YACA,SAAS;gBAAE,WAAW;YAAO;YAC7B,MAAM,SAAS,SAAS;YACxB,MAAM,SAAS;QACjB;IACF;IAEA,MAAM,cAAa,aAAqB,EAAE,MAAwD;QAChG,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YACrC,OAAO;gBAAE,IAAI;YAAc;YAC3B,MAAM;gBAAE;YAAO;QACjB;IACF;AACF;AAGO,MAAM,aAAa;IACxB,MAAM,QAAO,IAIZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;gBAC3B,eAAe,KAAK,aAAa;YACnC;QACF;IACF;IAEA,MAAM,kBAAiB,UAAkB;QACvC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACpC,OAAO;gBAAE;YAAW;YACpB,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;oBACb;gBACF;YACF;QACF;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,QAAO,IAIZ;QACC,gDAAgD;QAChD,MAAM,aAAa,KAAK,UAAU,KAAK,YAAY,KAAK,KAAK,CAAC,KAAK,UAAU,GAAG,OAAO,MAAM;QAC7F,MAAM,cAAc,KAAK,WAAW,KAAK,YAAY,KAAK,KAAK,CAAC,KAAK,WAAW,GAAG,OAAO,MAAM;QAEhG,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACtC,OAAO;gBAAE,QAAQ,KAAK,MAAM;YAAC;YAC7B,QAAQ;gBACN,YAAY,eAAe,YAAY;oBAAE,WAAW;gBAAW,IAAI;gBACnE,aAAa,gBAAgB,YAAY;oBAAE,WAAW;gBAAY,IAAI;YACxE;YACA,QAAQ;gBACN,QAAQ,KAAK,MAAM;gBACnB,YAAY,cAAc;gBAC1B,aAAa,eAAe;YAC9B;QACF;IACF;IAEA,MAAM,cAAa,MAAc;QAC/B,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE;YAAO;QAClB;IACF;IAEA,MAAM,aAAY,MAAc,EAAE,UAAkB,EAAE,WAAmB;QACvE,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACtC,OAAO;gBAAE;YAAO;YAChB,MAAM;gBACJ;gBACA;gBACA,WAAW,IAAI;YACjB;QACF;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,MAAM,QAAO,IAIZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC3C,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,QAAQ,KAAK,MAAM;gBACnB,aAAa,KAAK,WAAW;YAC/B;QACF;IACF;IAEA,MAAM;QACJ,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBAAE,QAAQ;YAAU;YAC3B,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;oBACb;gBACF;YACF;YACA,SAAS;gBAAE,WAAW;YAAM;QAC9B;IACF;IAEA,MAAM,cACJ,SAAiB,EACjB,MAA6C,EAC7C,WAAoB,EACpB,IAAa,EACb,eAAwB;QAExB,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC3C,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBACJ;gBACA;gBACA;gBACA;gBACA,aAAa,IAAI;YACnB;QACF;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,MAAM,KAAI,GAAW;QACnB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YACpD,OAAO;gBAAE;YAAI;QACf;QACA,OAAO,SAAS;IAClB;IAEA,MAAM,KAAI,GAAW,EAAE,KAAa,EAAE,SAAkB;QACtD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAI;YACb,QAAQ;gBAAE;YAAM;YAChB,QAAQ;gBAAE;gBAAK;YAAM;QACvB;IACF;IAEA,MAAM;QACJ,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,QAAQ;IAC5C;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,QAAO,IAOZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACnC,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,QAAQ,KAAK,MAAM;gBACnB,SAAS,KAAK,OAAO;gBACrB,SAAS,KAAK,OAAO,GAAG,KAAK,SAAS,CAAC,KAAK,OAAO,IAAI;gBACvD,WAAW,KAAK,SAAS;gBACzB,WAAW,KAAK,SAAS;YAC3B;QACF;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,MAAM,aAAY,MAAc;QAC9B,IAAI,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YACxD,OAAO;gBAAE;YAAO;QAClB;QAEA,IAAI,CAAC,eAAe;YAClB,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gBAChD,MAAM;oBACJ;oBACA,kBAAkB;oBAClB,gBAAgB;oBAChB,eAAe;oBACf,kBAAkB;oBAClB,eAAe;gBACjB;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAc,MAAc,EAAE,OAMnC;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAO;YAChB,MAAM;gBACJ,GAAG,OAAO;gBACV,aAAa,IAAI;YACnB;QACF;IACF;IAEA,MAAM,YAAW,MAAc,EAAE,MAAc;QAC7C,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAO;YAChB,MAAM;gBACJ,kBAAkB,OAAO,gBAAgB,GAAG;gBAC5C,eAAe,OAAO,aAAa,GAAG;gBACtC,aAAa,IAAI;YACnB;QACF;IACF;IAEA,MAAM,aAAY,MAAc,EAAE,MAAc;QAC9C,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAO;YAChB,MAAM;gBACJ,kBAAkB,OAAO,gBAAgB,GAAG;gBAC5C,eAAe,OAAO,aAAa,GAAG;gBACtC,aAAa,IAAI;YACnB;QACF;IACF;IAEA,MAAM,kBAAiB,MAAc,EAAE,MAAc;QACnD,MAAM,SAAS,MAAM,IAAI,CAAC,WAAW,CAAC;QACtC,IAAI,OAAO,gBAAgB,GAAG,QAAQ;YACpC,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAO;YAChB,MAAM;gBACJ,kBAAkB,OAAO,gBAAgB,GAAG;gBAC5C,kBAAkB,OAAO,gBAAgB,GAAG;gBAC5C,aAAa,IAAI;YACnB;QACF;IACF;IAEA,MAAM,cAAa,MAAc;QAC/B,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC;IAChC;AACF;AAGO,MAAM,uBAAuB;IAClC,MAAM,QAAO,IAUZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,eAAe,KAAK,aAAa;gBACjC,QAAQ,KAAK,MAAM;gBACnB,YAAY,KAAK,UAAU;gBAC3B,aAAa,KAAK,WAAW;gBAC7B,eAAe,KAAK,aAAa;gBACjC,aAAa,KAAK,WAAW;gBAC7B,gBAAgB,KAAK,cAAc;gBACnC,eAAe,KAAK,aAAa,IAAI;gBACrC,QAAQ;YACV;QACF;IACF;IAEA,MAAM,qBAAoB,aAAqB;QAC7C,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE;YAAc;YACvB,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;IACF;IAEA,MAAM,cAAa,MAAc,EAAE,OAIlC;QACC,MAAM,QAAa;YAAE;QAAO;QAE5B,IAAI,SAAS,QAAQ;YACnB,MAAM,MAAM,GAAG,QAAQ,MAAM;QAC/B;QAEA,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC9C;YACA,SAAS;gBAAE,WAAW;YAAO;YAC7B,MAAM,SAAS,SAAS;YACxB,MAAM,SAAS;YACf,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;IACF;IAEA,MAAM,SAAQ,OAIb;QACC,MAAM,QAAa,CAAC;QAEpB,IAAI,SAAS,QAAQ;YACnB,MAAM,MAAM,GAAG,QAAQ,MAAM;QAC/B;QAEA,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC9C;YACA,SAAS;gBAAE,WAAW;YAAO;YAC7B,MAAM,SAAS,SAAS;YACxB,MAAM,SAAS;YACf,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;IACF;IAEA,MAAM,cACJ,aAAqB,EACrB,MAAqB,EACrB,OAKC;QAED,MAAM,aAAkB;YAAE;QAAO;QAEjC,IAAI,SAAS,YAAY,WAAW,UAAU,GAAG,QAAQ,UAAU;QACnE,IAAI,SAAS,aAAa,WAAW,WAAW,GAAG,QAAQ,WAAW;QACtE,IAAI,SAAS,eAAe,WAAW,aAAa,GAAG,QAAQ,aAAa;QAC5E,IAAI,SAAS,kBAAkB,WAAW,WAAW,aAAa,GAAG,QAAQ,aAAa;QAE1F,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC5C,OAAO;gBAAE;YAAc;YACvB,MAAM;QACR;IACF;IAEA,MAAM,iBAAgB,aAAqB;QACzC,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,aAAa;YACzD,aAAa,IAAI;QACnB;IACF;IAEA,MAAM,cAAa,aAAqB,EAAE,MAAc;QACtD,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,UAAU;YACtD,eAAe;YACf,aAAa,IAAI;QACnB;IACF;IAEA,MAAM;QACJ,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;YAAE,QAAQ;QAAU;IAChD;IAEA,MAAM;QACJ,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;YAAE,QAAQ;QAAuB;IAC7D;IAEA,MAAM;QACJ,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC;YAAE,QAAQ;QAA4B;IAClE;IAEA,MAAM,cAAa,MAAqB;QACtC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;YAC9C,OAAO;gBAAE;YAAO;YAChB,SAAS;gBAAE,WAAW;YAAO;YAC7B,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;IACF;IAEA,MAAM,qBAAoB,aAAqB,EAAE,aAAqB;QACpE,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC5C,OAAO;gBAAE;YAAc;YACvB,MAAM;gBAAE;YAAc;QACxB;IACF;IAEA,MAAM;QACJ,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC;YACtD,QAAQ;gBACN,IAAI;YACN;YACA,MAAM;gBACJ,YAAY;YACd;YACA,OAAO;gBACL,QAAQ;oBAAE,IAAI;wBAAC;wBAAa;qBAAY;gBAAC;YAC3C;QACF;QAEA,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;YACzD,OAAO;gBACL,QAAQ;oBACN,IAAI;wBAAC;wBAAW;wBAAwB;qBAA4B;gBACtE;YACF;QACF;QAEA,OAAO;YACL,eAAe,MAAM,MAAM,CAAC,EAAE,IAAI;YAClC,aAAa,MAAM,IAAI,CAAC,UAAU,IAAI;YACtC,iBAAiB;QACnB;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,QAAQ,OAAO;QACb,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,OAAO;gCACP,WAAW;gCACX,UAAU;4BACZ;wBACF;oBACF;oBACA,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;QACF;IACF;IAEA,cAAc,OAAO;QACnB,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YACzC,OAAO;gBAAE;YAAO;YAChB,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,OAAO;gCACP,WAAW;gCACX,UAAU;4BACZ;wBACF;oBACF;oBACA,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;IACF;IAEA,UAAU,OAAO;QACf,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YAC3C,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,OAAO;gCACP,WAAW;gCACX,UAAU;4BACZ;wBACF;oBACF;oBACA,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;QACF;IACF;IAEA,SAAS;QACP,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YACzC,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,OAAO;gCACP,WAAW;gCACX,UAAU;4BACZ;wBACF;oBACF;oBACA,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;IACF;IAEA,cAAc,OAAO,IAAY;QAC/B,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAG;YACZ,MAAM;gBAAE;gBAAQ,WAAW,IAAI;YAAO;YACtC,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,IAAI;gCACJ,OAAO;gCACP,WAAW;gCACX,UAAU;4BACZ;wBACF;oBACF;oBACA,SAAS;wBAAE,WAAW;oBAAM;gBAC9B;YACF;QACF;IACF;AACF;AAGO,MAAM,mBAAmB;IAC9B,QAAQ,OAAO;QACb,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YACxC;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;IACF;IAEA,gBAAgB,OAAO;QACrB,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YAC1C,OAAO;gBAAE;YAAS;YAClB,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;YACA,SAAS;gBAAE,WAAW;YAAM;QAC9B;IACF;AACF", "debugId": null}}, {"offset": {"line": 952, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport { userDb } from './database';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n\n// Password utilities\nexport const hashPassword = async (password: string): Promise<string> => {\n  return await bcrypt.hash(password, 12);\n};\n\nexport const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {\n  return await bcrypt.compare(password, hashedPassword);\n};\n\n// JWT utilities\nexport const generateToken = (payload: { userId: string; email: string }): string => {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });\n};\n\nexport const verifyToken = (token: string): { userId: string; email: string } | null => {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string; email: string };\n    return decoded;\n  } catch (error) {\n    return null;\n  }\n};\n\n// Generate unique referral ID\nexport const generateReferralId = (): string => {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n  let result = 'HC'; // HashCoreX prefix\n  for (let i = 0; i < 8; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n};\n\n// Authentication middleware\nexport const authenticateRequest = async (request: NextRequest) => {\n  const token = request.headers.get('authorization')?.replace('Bearer ', '') ||\n                request.cookies.get('auth-token')?.value;\n\n  if (!token) {\n    return { authenticated: false, user: null };\n  }\n\n  const decoded = verifyToken(token);\n  if (!decoded) {\n    return { authenticated: false, user: null };\n  }\n\n  const user = await userDb.findByEmail(decoded.email);\n  if (!user) {\n    return { authenticated: false, user: null };\n  }\n\n  return { authenticated: true, user };\n};\n\n// User registration\nexport const registerUser = async (data: {\n  email: string;\n  firstName: string;\n  lastName: string;\n  password: string;\n  referralCode?: string;\n  placementSide?: 'left' | 'right';\n}) => {\n  // Check if user already exists\n  const existingUser = await userDb.findByEmail(data.email);\n  if (existingUser) {\n    throw new Error('User already exists with this email');\n  }\n\n  // Validate referral code if provided\n  let referrerId: string | undefined;\n  if (data.referralCode) {\n    const referrer = await userDb.findByReferralId(data.referralCode);\n    if (!referrer) {\n      throw new Error('Invalid referral code');\n    }\n    referrerId = referrer.id;\n  }\n\n  // Hash password\n  const passwordHash = await hashPassword(data.password);\n\n  // Generate unique referral ID\n  let referralId: string;\n  let isUnique = false;\n  do {\n    referralId = generateReferralId();\n    const existing = await userDb.findByReferralId(referralId);\n    isUnique = !existing;\n  } while (!isUnique);\n\n\n\n  // Create user in PostgreSQL\n  const user = await userDb.create({\n    email: data.email,\n    firstName: data.firstName,\n    lastName: data.lastName,\n    password: passwordHash,\n    referralId,\n  });\n\n  // Create referral relationship if referrer exists\n  if (referrerId) {\n    const { placeUserByReferralType } = await import('./referral');\n\n    // Determine referral type based on placementSide parameter\n    let referralType: 'general' | 'left' | 'right' = 'general';\n    if (data.placementSide === 'left') {\n      referralType = 'left';\n    } else if (data.placementSide === 'right') {\n      referralType = 'right';\n    }\n\n    // Place user using the new unified placement function\n    await placeUserByReferralType(referrerId, user.id, referralType);\n  }\n\n  return {\n    id: user.id,\n    email: user.email,\n    referralId: user.referralId,\n    kycStatus: user.kycStatus,\n  };\n};\n\n// User login\nexport const loginUser = async (data: {\n  email: string;\n  password: string;\n}) => {\n  const user = await userDb.findByEmail(data.email);\n  if (!user) {\n    throw new Error('Invalid email or password');\n  }\n\n\n\n  const isValidPassword = await verifyPassword(data.password, user.password);\n  if (!isValidPassword) {\n    throw new Error('Invalid email or password');\n  }\n\n  const token = generateToken({\n    userId: user.id,\n    email: user.email,\n  });\n\n  return {\n    token,\n    user: {\n      id: user.id,\n      email: user.email,\n      referralId: user.referralId,\n      kycStatus: user.kycStatus,\n    },\n  };\n};\n\n// Password validation\nexport const validatePassword = (password: string): { valid: boolean; errors: string[] } => {\n  const errors: string[] = [];\n\n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n\n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n\n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n\n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n\n  if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n    errors.push('Password must contain at least one special character');\n  }\n\n  return {\n    valid: errors.length === 0,\n    errors,\n  };\n};\n\n// Email validation\nexport const validateEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n// Session management\nexport const createSession = (userId: string, email: string) => {\n  return generateToken({ userId, email });\n};\n\nexport const validateSession = (token: string) => {\n  return verifyToken(token);\n};\n\n// Admin authentication\nexport const isAdmin = async (userId: string): Promise<boolean> => {\n  const user = await userDb.findById(userId);\n  return user?.role === 'ADMIN';\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AAEA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AAG9C,MAAM,eAAe,OAAO;IACjC,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AACrC;AAEO,MAAM,iBAAiB,OAAO,UAAkB;IACrD,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AACxC;AAGO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAe;AACnE;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,MAAM,qBAAqB;IAChC,MAAM,QAAQ;IACd,IAAI,SAAS,MAAM,mBAAmB;IACtC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,MAAM,sBAAsB,OAAO;IACxC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW,OACzD,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IAEjD,IAAI,CAAC,OAAO;QACV,OAAO;YAAE,eAAe;YAAO,MAAM;QAAK;IAC5C;IAEA,MAAM,UAAU,YAAY;IAC5B,IAAI,CAAC,SAAS;QACZ,OAAO;YAAE,eAAe;YAAO,MAAM;QAAK;IAC5C;IAEA,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,KAAK;IACnD,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,eAAe;YAAO,MAAM;QAAK;IAC5C;IAEA,OAAO;QAAE,eAAe;QAAM;IAAK;AACrC;AAGO,MAAM,eAAe,OAAO;IAQjC,+BAA+B;IAC/B,MAAM,eAAe,MAAM,wHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK,KAAK;IACxD,IAAI,cAAc;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,qCAAqC;IACrC,IAAI;IACJ,IAAI,KAAK,YAAY,EAAE;QACrB,MAAM,WAAW,MAAM,wHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,KAAK,YAAY;QAChE,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QACA,aAAa,SAAS,EAAE;IAC1B;IAEA,gBAAgB;IAChB,MAAM,eAAe,MAAM,aAAa,KAAK,QAAQ;IAErD,8BAA8B;IAC9B,IAAI;IACJ,IAAI,WAAW;IACf,GAAG;QACD,aAAa;QACb,MAAM,WAAW,MAAM,wHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC;QAC/C,WAAW,CAAC;IACd,QAAS,CAAC,SAAU;IAIpB,4BAA4B;IAC5B,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,MAAM,CAAC;QAC/B,OAAO,KAAK,KAAK;QACjB,WAAW,KAAK,SAAS;QACzB,UAAU,KAAK,QAAQ;QACvB,UAAU;QACV;IACF;IAEA,kDAAkD;IAClD,IAAI,YAAY;QACd,MAAM,EAAE,uBAAuB,EAAE,GAAG;QAEpC,2DAA2D;QAC3D,IAAI,eAA6C;QACjD,IAAI,KAAK,aAAa,KAAK,QAAQ;YACjC,eAAe;QACjB,OAAO,IAAI,KAAK,aAAa,KAAK,SAAS;YACzC,eAAe;QACjB;QAEA,sDAAsD;QACtD,MAAM,wBAAwB,YAAY,KAAK,EAAE,EAAE;IACrD;IAEA,OAAO;QACL,IAAI,KAAK,EAAE;QACX,OAAO,KAAK,KAAK;QACjB,YAAY,KAAK,UAAU;QAC3B,WAAW,KAAK,SAAS;IAC3B;AACF;AAGO,MAAM,YAAY,OAAO;IAI9B,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK,KAAK;IAChD,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAIA,MAAM,kBAAkB,MAAM,eAAe,KAAK,QAAQ,EAAE,KAAK,QAAQ;IACzE,IAAI,CAAC,iBAAiB;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,QAAQ,cAAc;QAC1B,QAAQ,KAAK,EAAE;QACf,OAAO,KAAK,KAAK;IACnB;IAEA,OAAO;QACL;QACA,MAAM;YACJ,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,YAAY,KAAK,UAAU;YAC3B,WAAW,KAAK,SAAS;QAC3B;IACF;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,yBAAyB,IAAI,CAAC,WAAW;QAC5C,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,OAAO,OAAO,MAAM,KAAK;QACzB;IACF;AACF;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,MAAM,gBAAgB,CAAC,QAAgB;IAC5C,OAAO,cAAc;QAAE;QAAQ;IAAM;AACvC;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,YAAY;AACrB;AAGO,MAAM,UAAU,OAAO;IAC5B,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC;IACnC,OAAO,MAAM,SAAS;AACxB", "debugId": null}}, {"offset": {"line": 1150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/trongrid.ts"], "sourcesContent": ["/**\n * Trongrid API Integration for USDT TRC20 Transaction Verification\n *\n * This module provides utilities to interact with the Trongrid API\n * to verify USDT TRC20 transactions on the Tron blockchain.\n * Supports both Mainnet and Testnet configurations.\n */\n\nimport { adminSettingsDb } from './database';\nimport { fromHex } from 'tron-format-address';\n\nconst TRONGRID_API_KEY = process.env.TRONGRID_API_KEY; // Optional, for higher rate limits\n\n// Network configuration interface\ninterface TronNetworkConfig {\n  apiUrl: string;\n  usdtContract: string;\n  network: 'mainnet' | 'testnet';\n}\n\n// Rate limiting configuration\nconst RATE_LIMIT_DELAY = 1000; // 1 second between requests\nlet lastRequestTime = 0;\n\n/**\n * Get current Tron network configuration from admin settings\n */\nasync function getTronNetworkConfig(): Promise<TronNetworkConfig> {\n  try {\n    const network = await adminSettingsDb.get('tronNetwork') || 'testnet';\n    const mainnetApiUrl = await adminSettingsDb.get('tronMainnetApiUrl') || 'https://api.trongrid.io';\n    const testnetApiUrl = await adminSettingsDb.get('tronTestnetApiUrl') || 'https://api.shasta.trongrid.io';\n    const mainnetContract = await adminSettingsDb.get('usdtMainnetContract') || 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t';\n    const testnetContract = await adminSettingsDb.get('usdtTestnetContract') || 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs';\n\n    const isMainnet = network === 'mainnet';\n\n    return {\n      apiUrl: isMainnet ? mainnetApiUrl : testnetApiUrl,\n      usdtContract: isMainnet ? mainnetContract : testnetContract,\n      network: network as 'mainnet' | 'testnet'\n    };\n  } catch (error) {\n    console.error('Error getting Tron network config, using testnet defaults:', error);\n    // Fallback to testnet configuration\n    return {\n      apiUrl: 'https://api.shasta.trongrid.io',\n      usdtContract: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs',\n      network: 'testnet'\n    };\n  }\n}\n\ninterface TronTransaction {\n  txID: string;\n  blockNumber: number;\n  blockTimeStamp: number;\n  contractResult: string[];\n  receipt: {\n    result: string;\n  };\n  log: Array<{\n    address: string;\n    topics: string[];\n    data: string;\n  }>;\n}\n\ninterface TronTransactionInfo {\n  id: string;\n  fee: number;\n  blockNumber: number;\n  blockTimeStamp: number;\n  contractResult: string[];\n  receipt: {\n    result: string;\n  };\n  log: Array<{\n    address: string;\n    topics: string[];\n    data: string;\n  }>;\n}\n\ninterface TronAccountInfo {\n  address: string;\n  balance: number;\n  create_time: number;\n  latest_opration_time: number;\n}\n\ninterface USDTTransferDetails {\n  isValid: boolean;\n  amount: number;\n  fromAddress: string;\n  toAddress: string;\n  contractAddress: string;\n  blockNumber: number;\n  blockTimestamp: number;\n  confirmations: number;\n  transactionId: string;\n}\n\n/**\n * Rate limiting helper to prevent API abuse\n */\nasync function rateLimitDelay(): Promise<void> {\n  const now = Date.now();\n  const timeSinceLastRequest = now - lastRequestTime;\n  \n  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {\n    const delay = RATE_LIMIT_DELAY - timeSinceLastRequest;\n    await new Promise(resolve => setTimeout(resolve, delay));\n  }\n  \n  lastRequestTime = Date.now();\n}\n\n/**\n * Make HTTP request to Trongrid API with proper headers and error handling\n */\nasync function makeApiRequest(endpoint: string, networkConfig?: TronNetworkConfig): Promise<any> {\n  await rateLimitDelay();\n\n  // Get network config if not provided\n  if (!networkConfig) {\n    networkConfig = await getTronNetworkConfig();\n  }\n\n  const headers: Record<string, string> = {\n    'Content-Type': 'application/json',\n  };\n\n  if (TRONGRID_API_KEY) {\n    headers['TRON-PRO-API-KEY'] = TRONGRID_API_KEY;\n  }\n\n  const response = await fetch(`${networkConfig.apiUrl}${endpoint}`, {\n    method: 'GET',\n    headers,\n  });\n\n  if (!response.ok) {\n    throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);\n  }\n\n  return await response.json();\n}\n\n/**\n * Get transaction details by transaction ID\n */\nexport async function getTransactionById(txId: string, networkConfig?: TronNetworkConfig): Promise<TronTransaction | null> {\n  try {\n    const config = networkConfig || await getTronNetworkConfig();\n    const response = await fetch(`${config.apiUrl}/walletsolidity/gettransactionbyid`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        value: txId\n      })\n    });\n\n    if (!response.ok) {\n      throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data.txID ? data : null;\n  } catch (error) {\n    console.error('Error fetching transaction:', error);\n    return null;\n  }\n}\n\n/**\n * Get transaction info (including receipt) by transaction ID\n */\nexport async function getTransactionInfo(txId: string, networkConfig?: TronNetworkConfig): Promise<TronTransactionInfo | null> {\n  try {\n    const config = networkConfig || await getTronNetworkConfig();\n    const response = await fetch(`${config.apiUrl}/walletsolidity/gettransactioninfobyid`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        value: txId\n      })\n    });\n\n    if (!response.ok) {\n      throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data.id ? data : null;\n  } catch (error) {\n    console.error('Error fetching transaction info:', error);\n    return null;\n  }\n}\n\n/**\n * Get current block number\n */\nexport async function getCurrentBlock(networkConfig?: TronNetworkConfig): Promise<{ blockNumber: number } | null> {\n  try {\n    const config = networkConfig || await getTronNetworkConfig();\n    const response = await fetch(`${config.apiUrl}/walletsolidity/getnowblock`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      }\n    });\n\n    if (!response.ok) {\n      throw new Error(`Trongrid API error: ${response.status} ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data.block_header ? { blockNumber: data.block_header.raw_data.number } : null;\n  } catch (error) {\n    console.error('Error fetching current block:', error);\n    return null;\n  }\n}\n\n/**\n * Convert hex string to decimal number\n */\nfunction hexToDecimal(hex: string): number {\n  return parseInt(hex, 16);\n}\n\n/**\n * Convert Tron address from hex to base58\n */\nfunction hexToTronAddress(hex: string): string {\n  try {\n    // Remove '0x' prefix if present\n    let cleanHex = hex.startsWith('0x') ? hex.slice(2) : hex;\n\n    // Ensure we have a 40-character hex string (20 bytes)\n    if (cleanHex.length < 40) {\n      cleanHex = '0'.repeat(40 - cleanHex.length) + cleanHex;\n    }\n\n    // Add the Tron address prefix (0x41 for mainnet/testnet)\n    const addressHex = '41' + cleanHex;\n\n    // Convert to base58\n    return fromHex(addressHex);\n  } catch (error) {\n    console.error('Error converting hex to Tron address:', error);\n    // Fallback to a recognizable format if conversion fails\n    return `T${hex.slice(-30)}`;\n  }\n}\n\n/**\n * Parse USDT TRC20 transfer from transaction data and logs\n */\nfunction parseUSDTTransfer(\n  transaction: any,\n  logs: Array<{ address: string; topics: string[]; data: string }>,\n  usdtContract: string\n): {\n  amount: number;\n  fromAddress: string;\n  toAddress: string;\n} | null {\n  console.log('Parsing USDT transfer from transaction and logs:', logs.length, 'Contract:', usdtContract);\n\n  // First, try to parse from transaction data (more reliable for recipient address)\n  if (transaction?.raw_data?.contract?.[0]?.parameter?.value) {\n    const contractValue = transaction.raw_data.contract[0].parameter.value;\n\n    // Check if this is a USDT contract call\n    const contractAddressBase58 = hexToTronAddress(contractValue.contract_address?.replace('41', '') || '');\n    const isUSDTContract = contractAddressBase58.toLowerCase() === usdtContract.toLowerCase();\n\n    if (isUSDTContract && contractValue.data) {\n      try {\n        const data = contractValue.data;\n\n        // Check if this is a transfer function call (a9059cbb = transfer)\n        if (data.startsWith('a9059cbb')) {\n          console.log('Found USDT transfer in transaction data');\n\n          // Parse recipient address from data (next 64 chars after function selector)\n          const recipientHex = data.slice(8, 72).slice(24); // Remove padding\n          const toAddress = hexToTronAddress(recipientHex);\n\n          // Parse amount from data (last 64 chars)\n          const amountHex = data.slice(72);\n          const amount = hexToDecimal(amountHex) / 1000000; // USDT has 6 decimals\n\n          // Get sender address from transaction\n          const fromAddress = hexToTronAddress(contractValue.owner_address?.replace('41', '') || '');\n\n          console.log('Parsed transfer from transaction data:', {\n            fromAddress,\n            toAddress,\n            amount,\n            recipientHex,\n            amountHex\n          });\n\n          return {\n            amount,\n            fromAddress,\n            toAddress,\n          };\n        }\n      } catch (error) {\n        console.error('Error parsing transaction data:', error);\n      }\n    }\n  }\n\n  // Fallback to parsing from logs if transaction data parsing fails\n  const usdtLog = logs.find(log => {\n    const logAddressBase58 = hexToTronAddress(log.address);\n    const isUSDTContract = logAddressBase58.toLowerCase() === usdtContract.toLowerCase();\n    const hasCorrectTopics = log.topics.length >= 3;\n    const isTransferEvent = log.topics[0] === 'ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef';\n\n    return isUSDTContract && hasCorrectTopics && isTransferEvent;\n  });\n\n  if (!usdtLog) {\n    console.log('No USDT transfer found in transaction data or logs');\n    return null;\n  }\n\n  try {\n    console.log('Parsing from logs as fallback:', usdtLog);\n\n    const fromAddressHex = usdtLog.topics[1].slice(26);\n    const toAddressHex = usdtLog.topics[2].slice(26);\n    const amountHex = usdtLog.data.startsWith('0x') ? usdtLog.data.slice(2) : usdtLog.data;\n\n    const fromAddress = hexToTronAddress(fromAddressHex);\n    const toAddress = hexToTronAddress(toAddressHex);\n    const amount = hexToDecimal(amountHex) / 1000000;\n\n    return {\n      amount,\n      fromAddress,\n      toAddress,\n    };\n  } catch (error) {\n    console.error('Error parsing USDT transfer from logs:', error);\n    return null;\n  }\n}\n\n/**\n * Verify USDT TRC20 transaction and extract transfer details\n */\nexport async function verifyUSDTTransaction(\n  txId: string,\n  expectedToAddress: string,\n  minConfirmations: number = 1\n): Promise<USDTTransferDetails> {\n  // Get current network configuration\n  const networkConfig = await getTronNetworkConfig();\n\n  console.log('Verifying USDT transaction:', {\n    txId,\n    expectedToAddress,\n    minConfirmations,\n    network: networkConfig.network,\n    apiUrl: networkConfig.apiUrl,\n    usdtContract: networkConfig.usdtContract\n  });\n\n  try {\n    // Get transaction details\n    const transaction = await getTransactionById(txId, networkConfig);\n    console.log('Transaction details:', transaction);\n\n    if (!transaction) {\n      console.log('Transaction not found');\n      return {\n        isValid: false,\n        amount: 0,\n        fromAddress: '',\n        toAddress: '',\n        contractAddress: networkConfig.usdtContract,\n        blockNumber: 0,\n        blockTimestamp: 0,\n        confirmations: 0,\n        transactionId: txId,\n      };\n    }\n\n    // Get transaction info for receipt and confirmations\n    const transactionInfo = await getTransactionInfo(txId, networkConfig);\n    console.log('Transaction info:', transactionInfo);\n\n    if (!transactionInfo) {\n      console.log('Transaction info not found');\n      return {\n        isValid: false,\n        amount: 0,\n        fromAddress: '',\n        toAddress: '',\n        contractAddress: networkConfig.usdtContract,\n        blockNumber: 0,\n        blockTimestamp: 0,\n        confirmations: 0,\n        transactionId: txId,\n      };\n    }\n\n    // Check if transaction was successful\n    console.log('Transaction receipt result:', transactionInfo.receipt?.result);\n    if (transactionInfo.receipt?.result !== 'SUCCESS') {\n      console.log('Transaction failed or not successful');\n      return {\n        isValid: false,\n        amount: 0,\n        fromAddress: '',\n        toAddress: '',\n        contractAddress: networkConfig.usdtContract,\n        blockNumber: transactionInfo.blockNumber,\n        blockTimestamp: transactionInfo.blockTimeStamp,\n        confirmations: 0,\n        transactionId: txId,\n      };\n    }\n\n    // Parse USDT transfer from transaction data and logs\n    const transferDetails = parseUSDTTransfer(transaction, transactionInfo.log || [], networkConfig.usdtContract);\n    if (!transferDetails) {\n      console.log('No USDT transfer details found');\n      return {\n        isValid: false,\n        amount: 0,\n        fromAddress: '',\n        toAddress: '',\n        contractAddress: networkConfig.usdtContract,\n        blockNumber: transactionInfo.blockNumber,\n        blockTimestamp: transactionInfo.blockTimeStamp,\n        confirmations: 0,\n        transactionId: txId,\n      };\n    }\n\n    // Calculate confirmations using block numbers\n    const currentBlock = await getCurrentBlock(networkConfig);\n    let confirmations = 0;\n\n    if (currentBlock && transactionInfo.blockNumber) {\n      confirmations = currentBlock.blockNumber - transactionInfo.blockNumber;\n    }\n\n    console.log('Confirmation calculation:', {\n      currentBlockNumber: currentBlock?.blockNumber,\n      transactionBlockNumber: transactionInfo.blockNumber,\n      confirmations,\n      minConfirmations\n    });\n\n    // Verify the recipient address matches expected address\n    const isValidRecipient = transferDetails.toAddress.toLowerCase() === expectedToAddress.toLowerCase();\n\n    console.log('Address verification:', {\n      transferToAddress: transferDetails.toAddress,\n      expectedToAddress,\n      isValidRecipient,\n      addressesMatch: transferDetails.toAddress.toLowerCase() === expectedToAddress.toLowerCase()\n    });\n\n    const isValid = isValidRecipient && confirmations >= minConfirmations && transferDetails.amount > 0;\n\n    console.log('Final verification result:', {\n      isValid,\n      isValidRecipient,\n      confirmations,\n      minConfirmations,\n      amount: transferDetails.amount\n    });\n\n    return {\n      isValid,\n      amount: transferDetails.amount,\n      fromAddress: transferDetails.fromAddress,\n      toAddress: transferDetails.toAddress,\n      contractAddress: networkConfig.usdtContract,\n      blockNumber: transactionInfo.blockNumber,\n      blockTimestamp: transactionInfo.blockTimeStamp,\n      confirmations: Math.max(0, confirmations),\n      transactionId: txId,\n    };\n\n  } catch (error) {\n    console.error('Error verifying USDT transaction:', error);\n    return {\n      isValid: false,\n      amount: 0,\n      fromAddress: '',\n      toAddress: '',\n      contractAddress: networkConfig.usdtContract,\n      blockNumber: 0,\n      blockTimestamp: 0,\n      confirmations: 0,\n      transactionId: txId,\n    };\n  }\n}\n\n/**\n * Validate Tron transaction ID format\n */\nexport function isValidTronTransactionId(txId: string): boolean {\n  // Tron transaction IDs are 64-character hexadecimal strings\n  const tronTxRegex = /^[a-fA-F0-9]{64}$/;\n  return tronTxRegex.test(txId);\n}\n\n/**\n * Validate Tron address format\n */\nexport function isValidTronAddress(address: string): boolean {\n  // Tron addresses start with 'T' and are 34 characters long\n  const tronAddressRegex = /^T[A-Za-z1-9]{33}$/;\n  return tronAddressRegex.test(address);\n}\n\n/**\n * Get account information by address\n */\nexport async function getAccountInfo(address: string, networkConfig?: TronNetworkConfig): Promise<TronAccountInfo | null> {\n  try {\n    const data = await makeApiRequest(`/v1/accounts/${address}`, networkConfig);\n    return data.data && data.data.length > 0 ? data.data[0] : null;\n  } catch (error) {\n    console.error('Error fetching account info:', error);\n    return null;\n  }\n}\n\n/**\n * Get current network configuration (exported for external use)\n */\nexport async function getCurrentNetworkConfig(): Promise<TronNetworkConfig> {\n  return await getTronNetworkConfig();\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;AAED;AACA;;;AAEA,MAAM,mBAAmB,QAAQ,GAAG,CAAC,gBAAgB,EAAE,mCAAmC;AAS1F,8BAA8B;AAC9B,MAAM,mBAAmB,MAAM,4BAA4B;AAC3D,IAAI,kBAAkB;AAEtB;;CAEC,GACD,eAAe;IACb,IAAI;QACF,MAAM,UAAU,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,kBAAkB;QAC5D,MAAM,gBAAgB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,wBAAwB;QACxE,MAAM,gBAAgB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,wBAAwB;QACxE,MAAM,kBAAkB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,0BAA0B;QAC5E,MAAM,kBAAkB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,0BAA0B;QAE5E,MAAM,YAAY,YAAY;QAE9B,OAAO;YACL,QAAQ,YAAY,gBAAgB;YACpC,cAAc,YAAY,kBAAkB;YAC5C,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8DAA8D;QAC5E,oCAAoC;QACpC,OAAO;YACL,QAAQ;YACR,cAAc;YACd,SAAS;QACX;IACF;AACF;AAoDA;;CAEC,GACD,eAAe;IACb,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,uBAAuB,MAAM;IAEnC,IAAI,uBAAuB,kBAAkB;QAC3C,MAAM,QAAQ,mBAAmB;QACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IACnD;IAEA,kBAAkB,KAAK,GAAG;AAC5B;AAEA;;CAEC,GACD,eAAe,eAAe,QAAgB,EAAE,aAAiC;IAC/E,MAAM;IAEN,qCAAqC;IACrC,IAAI,CAAC,eAAe;QAClB,gBAAgB,MAAM;IACxB;IAEA,MAAM,UAAkC;QACtC,gBAAgB;IAClB;IAEA,IAAI,kBAAkB;QACpB,OAAO,CAAC,mBAAmB,GAAG;IAChC;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,cAAc,MAAM,GAAG,UAAU,EAAE;QACjE,QAAQ;QACR;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;IACjF;IAEA,OAAO,MAAM,SAAS,IAAI;AAC5B;AAKO,eAAe,mBAAmB,IAAY,EAAE,aAAiC;IACtF,IAAI;QACF,MAAM,SAAS,iBAAiB,MAAM;QACtC,MAAM,WAAW,MAAM,MAAM,GAAG,OAAO,MAAM,CAAC,kCAAkC,CAAC,EAAE;YACjF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO;YACT;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACjF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,IAAI,GAAG,OAAO;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAKO,eAAe,mBAAmB,IAAY,EAAE,aAAiC;IACtF,IAAI;QACF,MAAM,SAAS,iBAAiB,MAAM;QACtC,MAAM,WAAW,MAAM,MAAM,GAAG,OAAO,MAAM,CAAC,sCAAsC,CAAC,EAAE;YACrF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO;YACT;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACjF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,EAAE,GAAG,OAAO;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AAKO,eAAe,gBAAgB,aAAiC;IACrE,IAAI;QACF,MAAM,SAAS,iBAAiB,MAAM;QACtC,MAAM,WAAW,MAAM,MAAM,GAAG,OAAO,MAAM,CAAC,2BAA2B,CAAC,EAAE;YAC1E,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACjF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,YAAY,GAAG;YAAE,aAAa,KAAK,YAAY,CAAC,QAAQ,CAAC,MAAM;QAAC,IAAI;IAClF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;IACT;AACF;AAEA;;CAEC,GACD,SAAS,aAAa,GAAW;IAC/B,OAAO,SAAS,KAAK;AACvB;AAEA;;CAEC,GACD,SAAS,iBAAiB,GAAW;IACnC,IAAI;QACF,gCAAgC;QAChC,IAAI,WAAW,IAAI,UAAU,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK;QAErD,sDAAsD;QACtD,IAAI,SAAS,MAAM,GAAG,IAAI;YACxB,WAAW,IAAI,MAAM,CAAC,KAAK,SAAS,MAAM,IAAI;QAChD;QAEA,yDAAyD;QACzD,MAAM,aAAa,OAAO;QAE1B,oBAAoB;QACpB,OAAO,CAAA,GAAA,qKAAA,CAAA,UAAO,AAAD,EAAE;IACjB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,wDAAwD;QACxD,OAAO,CAAC,CAAC,EAAE,IAAI,KAAK,CAAC,CAAC,KAAK;IAC7B;AACF;AAEA;;CAEC,GACD,SAAS,kBACP,WAAgB,EAChB,IAAgE,EAChE,YAAoB;IAMpB,QAAQ,GAAG,CAAC,oDAAoD,KAAK,MAAM,EAAE,aAAa;IAE1F,kFAAkF;IAClF,IAAI,aAAa,UAAU,UAAU,CAAC,EAAE,EAAE,WAAW,OAAO;QAC1D,MAAM,gBAAgB,YAAY,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,CAAC,KAAK;QAEtE,wCAAwC;QACxC,MAAM,wBAAwB,iBAAiB,cAAc,gBAAgB,EAAE,QAAQ,MAAM,OAAO;QACpG,MAAM,iBAAiB,sBAAsB,WAAW,OAAO,aAAa,WAAW;QAEvF,IAAI,kBAAkB,cAAc,IAAI,EAAE;YACxC,IAAI;gBACF,MAAM,OAAO,cAAc,IAAI;gBAE/B,kEAAkE;gBAClE,IAAI,KAAK,UAAU,CAAC,aAAa;oBAC/B,QAAQ,GAAG,CAAC;oBAEZ,4EAA4E;oBAC5E,MAAM,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,iBAAiB;oBACnE,MAAM,YAAY,iBAAiB;oBAEnC,yCAAyC;oBACzC,MAAM,YAAY,KAAK,KAAK,CAAC;oBAC7B,MAAM,SAAS,aAAa,aAAa,SAAS,sBAAsB;oBAExE,sCAAsC;oBACtC,MAAM,cAAc,iBAAiB,cAAc,aAAa,EAAE,QAAQ,MAAM,OAAO;oBAEvF,QAAQ,GAAG,CAAC,0CAA0C;wBACpD;wBACA;wBACA;wBACA;wBACA;oBACF;oBAEA,OAAO;wBACL;wBACA;wBACA;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;YACnD;QACF;IACF;IAEA,kEAAkE;IAClE,MAAM,UAAU,KAAK,IAAI,CAAC,CAAA;QACxB,MAAM,mBAAmB,iBAAiB,IAAI,OAAO;QACrD,MAAM,iBAAiB,iBAAiB,WAAW,OAAO,aAAa,WAAW;QAClF,MAAM,mBAAmB,IAAI,MAAM,CAAC,MAAM,IAAI;QAC9C,MAAM,kBAAkB,IAAI,MAAM,CAAC,EAAE,KAAK;QAE1C,OAAO,kBAAkB,oBAAoB;IAC/C;IAEA,IAAI,CAAC,SAAS;QACZ,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC,kCAAkC;QAE9C,MAAM,iBAAiB,QAAQ,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;QAC/C,MAAM,eAAe,QAAQ,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;QAC7C,MAAM,YAAY,QAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,QAAQ,IAAI;QAEtF,MAAM,cAAc,iBAAiB;QACrC,MAAM,YAAY,iBAAiB;QACnC,MAAM,SAAS,aAAa,aAAa;QAEzC,OAAO;YACL;YACA;YACA;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;IACT;AACF;AAKO,eAAe,sBACpB,IAAY,EACZ,iBAAyB,EACzB,mBAA2B,CAAC;IAE5B,oCAAoC;IACpC,MAAM,gBAAgB,MAAM;IAE5B,QAAQ,GAAG,CAAC,+BAA+B;QACzC;QACA;QACA;QACA,SAAS,cAAc,OAAO;QAC9B,QAAQ,cAAc,MAAM;QAC5B,cAAc,cAAc,YAAY;IAC1C;IAEA,IAAI;QACF,0BAA0B;QAC1B,MAAM,cAAc,MAAM,mBAAmB,MAAM;QACnD,QAAQ,GAAG,CAAC,wBAAwB;QAEpC,IAAI,CAAC,aAAa;YAChB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,WAAW;gBACX,iBAAiB,cAAc,YAAY;gBAC3C,aAAa;gBACb,gBAAgB;gBAChB,eAAe;gBACf,eAAe;YACjB;QACF;QAEA,qDAAqD;QACrD,MAAM,kBAAkB,MAAM,mBAAmB,MAAM;QACvD,QAAQ,GAAG,CAAC,qBAAqB;QAEjC,IAAI,CAAC,iBAAiB;YACpB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,WAAW;gBACX,iBAAiB,cAAc,YAAY;gBAC3C,aAAa;gBACb,gBAAgB;gBAChB,eAAe;gBACf,eAAe;YACjB;QACF;QAEA,sCAAsC;QACtC,QAAQ,GAAG,CAAC,+BAA+B,gBAAgB,OAAO,EAAE;QACpE,IAAI,gBAAgB,OAAO,EAAE,WAAW,WAAW;YACjD,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,WAAW;gBACX,iBAAiB,cAAc,YAAY;gBAC3C,aAAa,gBAAgB,WAAW;gBACxC,gBAAgB,gBAAgB,cAAc;gBAC9C,eAAe;gBACf,eAAe;YACjB;QACF;QAEA,qDAAqD;QACrD,MAAM,kBAAkB,kBAAkB,aAAa,gBAAgB,GAAG,IAAI,EAAE,EAAE,cAAc,YAAY;QAC5G,IAAI,CAAC,iBAAiB;YACpB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,SAAS;gBACT,QAAQ;gBACR,aAAa;gBACb,WAAW;gBACX,iBAAiB,cAAc,YAAY;gBAC3C,aAAa,gBAAgB,WAAW;gBACxC,gBAAgB,gBAAgB,cAAc;gBAC9C,eAAe;gBACf,eAAe;YACjB;QACF;QAEA,8CAA8C;QAC9C,MAAM,eAAe,MAAM,gBAAgB;QAC3C,IAAI,gBAAgB;QAEpB,IAAI,gBAAgB,gBAAgB,WAAW,EAAE;YAC/C,gBAAgB,aAAa,WAAW,GAAG,gBAAgB,WAAW;QACxE;QAEA,QAAQ,GAAG,CAAC,6BAA6B;YACvC,oBAAoB,cAAc;YAClC,wBAAwB,gBAAgB,WAAW;YACnD;YACA;QACF;QAEA,wDAAwD;QACxD,MAAM,mBAAmB,gBAAgB,SAAS,CAAC,WAAW,OAAO,kBAAkB,WAAW;QAElG,QAAQ,GAAG,CAAC,yBAAyB;YACnC,mBAAmB,gBAAgB,SAAS;YAC5C;YACA;YACA,gBAAgB,gBAAgB,SAAS,CAAC,WAAW,OAAO,kBAAkB,WAAW;QAC3F;QAEA,MAAM,UAAU,oBAAoB,iBAAiB,oBAAoB,gBAAgB,MAAM,GAAG;QAElG,QAAQ,GAAG,CAAC,8BAA8B;YACxC;YACA;YACA;YACA;YACA,QAAQ,gBAAgB,MAAM;QAChC;QAEA,OAAO;YACL;YACA,QAAQ,gBAAgB,MAAM;YAC9B,aAAa,gBAAgB,WAAW;YACxC,WAAW,gBAAgB,SAAS;YACpC,iBAAiB,cAAc,YAAY;YAC3C,aAAa,gBAAgB,WAAW;YACxC,gBAAgB,gBAAgB,cAAc;YAC9C,eAAe,KAAK,GAAG,CAAC,GAAG;YAC3B,eAAe;QACjB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YACL,SAAS;YACT,QAAQ;YACR,aAAa;YACb,WAAW;YACX,iBAAiB,cAAc,YAAY;YAC3C,aAAa;YACb,gBAAgB;YAChB,eAAe;YACf,eAAe;QACjB;IACF;AACF;AAKO,SAAS,yBAAyB,IAAY;IACnD,4DAA4D;IAC5D,MAAM,cAAc;IACpB,OAAO,YAAY,IAAI,CAAC;AAC1B;AAKO,SAAS,mBAAmB,OAAe;IAChD,2DAA2D;IAC3D,MAAM,mBAAmB;IACzB,OAAO,iBAAiB,IAAI,CAAC;AAC/B;AAKO,eAAe,eAAe,OAAe,EAAE,aAAiC;IACrF,IAAI;QACF,MAAM,OAAO,MAAM,eAAe,CAAC,aAAa,EAAE,SAAS,EAAE;QAC7D,OAAO,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,IAAI,KAAK,IAAI,CAAC,EAAE,GAAG;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAKO,eAAe;IACpB,OAAO,MAAM;AACf", "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/depositVerificationService.ts"], "sourcesContent": ["import {\n  depositTransactionDb,\n  walletBalanceDb,\n  transactionDb,\n  adminSettingsDb,\n  systemLogDb\n} from '@/lib/database';\nimport { prisma } from '@/lib/prisma';\nimport { verifyUSDTTransaction } from '@/lib/trongrid';\nimport { DepositStatus } from '@/types';\n\n// Map to track active verification processes to prevent duplicates\nconst activeVerifications = new Map<string, boolean>();\n\n// Map to track confirmation checking intervals\nconst confirmationIntervals = new Map<string, NodeJS.Timeout>();\n\n/**\n * Background service for automated USDT deposit verification\n */\nexport class DepositVerificationService {\n  private static instance: DepositVerificationService;\n  private isRunning = false;\n\n  static getInstance(): DepositVerificationService {\n    if (!DepositVerificationService.instance) {\n      DepositVerificationService.instance = new DepositVerificationService();\n    }\n    return DepositVerificationService.instance;\n  }\n\n  /**\n   * Start the background verification service\n   */\n  async start() {\n    if (this.isRunning) {\n      console.log('Deposit verification service is already running');\n      return;\n    }\n\n    this.isRunning = true;\n    console.log('Starting deposit verification service...');\n\n    // Process existing pending verification deposits\n    await this.processPendingVerifications();\n\n    // Process existing waiting for confirmations deposits\n    await this.processWaitingForConfirmations();\n\n    console.log('Deposit verification service started successfully');\n  }\n\n  /**\n   * Stop the background verification service\n   */\n  stop() {\n    this.isRunning = false;\n    \n    // Clear all active intervals\n    confirmationIntervals.forEach((interval) => {\n      clearTimeout(interval);\n    });\n    confirmationIntervals.clear();\n    activeVerifications.clear();\n\n    console.log('Deposit verification service stopped');\n  }\n\n  /**\n   * Process deposits with PENDING_VERIFICATION status\n   */\n  private async processPendingVerifications() {\n    try {\n      const pendingDeposits = await depositTransactionDb.getPendingVerificationDeposits();\n      console.log(`Found ${pendingDeposits.length} deposits pending verification`);\n\n      for (const deposit of pendingDeposits) {\n        if (!activeVerifications.has(deposit.transactionId)) {\n          this.scheduleVerification(deposit.transactionId, deposit.tronAddress);\n        }\n      }\n    } catch (error) {\n      console.error('Error processing pending verifications:', error);\n      await systemLogDb.create({\n        action: 'DEPOSIT_VERIFICATION_ERROR',\n        details: `Error processing pending verifications: ${error instanceof Error ? error.message : 'Unknown error'}`,\n      });\n    }\n  }\n\n  /**\n   * Process deposits with WAITING_FOR_CONFIRMATIONS status\n   */\n  private async processWaitingForConfirmations() {\n    try {\n      const waitingDeposits = await depositTransactionDb.getWaitingForConfirmationsDeposits();\n      console.log(`Found ${waitingDeposits.length} deposits waiting for confirmations`);\n\n      for (const deposit of waitingDeposits) {\n        if (!confirmationIntervals.has(deposit.transactionId)) {\n          this.scheduleConfirmationCheck(deposit.transactionId, deposit.tronAddress);\n        }\n      }\n    } catch (error) {\n      console.error('Error processing waiting for confirmations:', error);\n      await systemLogDb.create({\n        action: 'CONFIRMATION_CHECK_ERROR',\n        details: `Error processing waiting for confirmations: ${error instanceof Error ? error.message : 'Unknown error'}`,\n      });\n    }\n  }\n\n  /**\n   * Schedule verification for a transaction (with 60-second retry)\n   */\n  private scheduleVerification(transactionId: string, tronAddress: string) {\n    if (activeVerifications.has(transactionId)) {\n      return; // Already being processed\n    }\n\n    activeVerifications.set(transactionId, true);\n    console.log(`Scheduling verification for transaction: ${transactionId}`);\n\n    // Immediate verification attempt\n    this.verifyTransaction(transactionId, tronAddress, false);\n\n    // Schedule retry after 60 seconds if not found\n    setTimeout(() => {\n      this.verifyTransaction(transactionId, tronAddress, true);\n    }, 60000);\n  }\n\n  /**\n   * Verify a single transaction\n   */\n  private async verifyTransaction(transactionId: string, tronAddress: string, isRetry: boolean) {\n    try {\n      console.log(`${isRetry ? 'Retrying' : 'Attempting'} verification for transaction: ${transactionId}`);\n\n      // Get minimum confirmations setting\n      const minConfirmations = parseInt(await adminSettingsDb.get('minConfirmations') || '10');\n\n      // Get deposit settings for validation\n      const minDepositAmount = parseFloat(await adminSettingsDb.get('minDepositAmount') || '10');\n      const maxDepositAmount = parseFloat(await adminSettingsDb.get('maxDepositAmount') || '10000');\n\n      // Verify the transaction with timeout\n      const verificationPromise = verifyUSDTTransaction(transactionId, tronAddress, 1);\n      const timeoutPromise = new Promise((_, reject) =>\n        setTimeout(() => reject(new Error('Verification timeout')), 30000)\n      );\n\n      const verificationResult = await Promise.race([verificationPromise, timeoutPromise]) as any;\n\n      if (!verificationResult.isValid && verificationResult.confirmations === 0) {\n        if (isRetry) {\n          // Final attempt failed - mark as failed\n          await depositTransactionDb.updateStatus(transactionId, 'FAILED', {\n            failureReason: 'Transaction not found or invalid after verification attempts',\n            processedAt: new Date(),\n          });\n\n          await systemLogDb.create({\n            action: 'DEPOSIT_VERIFICATION_FAILED',\n            details: `Transaction ${transactionId} failed verification after retry`,\n          });\n\n          activeVerifications.delete(transactionId);\n        }\n        return;\n      }\n\n      // Validate recipient address\n      const hasValidRecipient = verificationResult.toAddress.toLowerCase().includes(tronAddress.toLowerCase().slice(1, 10)) ||\n                                tronAddress.toLowerCase().includes(verificationResult.toAddress.toLowerCase().slice(1, 10));\n\n      if (!hasValidRecipient) {\n        await depositTransactionDb.updateStatus(transactionId, 'FAILED', {\n          failureReason: 'Invalid recipient address',\n          processedAt: new Date(),\n        });\n        activeVerifications.delete(transactionId);\n        return;\n      }\n\n      // Validate deposit amount\n      if (verificationResult.amount < minDepositAmount) {\n        await depositTransactionDb.updateStatus(transactionId, 'FAILED', {\n          failureReason: `Deposit amount ${verificationResult.amount} USDT is below minimum ${minDepositAmount} USDT`,\n          processedAt: new Date(),\n        });\n        activeVerifications.delete(transactionId);\n        return;\n      }\n\n      if (verificationResult.amount > maxDepositAmount) {\n        await depositTransactionDb.updateStatus(transactionId, 'FAILED', {\n          failureReason: `Deposit amount ${verificationResult.amount} USDT exceeds maximum ${maxDepositAmount} USDT`,\n          processedAt: new Date(),\n        });\n        activeVerifications.delete(transactionId);\n        return;\n      }\n\n      // Transaction found and validated - update with verification details\n      // First update the deposit record with transaction details\n      await prisma.depositTransaction.update({\n        where: { transactionId },\n        data: {\n          amount: verificationResult.amount,\n          usdtAmount: verificationResult.amount,\n          senderAddress: verificationResult.fromAddress,\n          blockNumber: verificationResult.blockNumber.toString(),\n          blockTimestamp: new Date(verificationResult.blockTimestamp),\n          confirmations: verificationResult.confirmations,\n        },\n      });\n\n      await depositTransactionDb.updateStatus(transactionId, 'PENDING', {\n        confirmations: verificationResult.confirmations,\n      });\n\n      console.log(`Transaction ${transactionId} verified with ${verificationResult.confirmations} confirmations (required: ${minConfirmations})`);\n\n      // Check if it has enough confirmations\n      if (verificationResult.confirmations >= minConfirmations) {\n        await this.completeDeposit(transactionId, verificationResult.amount);\n      } else {\n        // Not enough confirmations - start confirmation checking\n        await depositTransactionDb.updateStatus(transactionId, 'WAITING_FOR_CONFIRMATIONS');\n        this.scheduleConfirmationCheck(transactionId, tronAddress);\n      }\n\n      activeVerifications.delete(transactionId);\n\n    } catch (error) {\n      console.error(`Error verifying transaction ${transactionId}:`, error);\n\n      const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n      const isNetworkError = errorMessage.includes('timeout') || errorMessage.includes('network') || errorMessage.includes('ECONNRESET');\n\n      if (isRetry || !isNetworkError) {\n        // Final attempt failed due to error or non-network error\n        await depositTransactionDb.updateStatus(transactionId, 'FAILED', {\n          failureReason: `Verification error: ${errorMessage}`,\n          processedAt: new Date(),\n        });\n        activeVerifications.delete(transactionId);\n\n        await systemLogDb.create({\n          action: 'DEPOSIT_VERIFICATION_FAILED',\n          details: `Transaction ${transactionId} failed verification: ${errorMessage}`,\n        });\n      } else {\n        // Network error on first attempt - will retry in 60 seconds\n        await systemLogDb.create({\n          action: 'DEPOSIT_VERIFICATION_NETWORK_ERROR',\n          details: `Network error verifying transaction ${transactionId}: ${errorMessage}. Will retry.`,\n        });\n      }\n    }\n  }\n\n  /**\n   * Schedule confirmation checking for a transaction\n   */\n  private scheduleConfirmationCheck(transactionId: string, tronAddress: string) {\n    if (confirmationIntervals.has(transactionId)) {\n      return; // Already being checked\n    }\n\n    console.log(`Starting confirmation checking for transaction: ${transactionId}`);\n\n    const interval = setInterval(async () => {\n      await this.checkConfirmations(transactionId, tronAddress);\n    }, 60000); // Check every 60 seconds\n\n    confirmationIntervals.set(transactionId, interval);\n\n    // Also check immediately\n    this.checkConfirmations(transactionId, tronAddress);\n  }\n\n  /**\n   * Check confirmations for a transaction\n   */\n  private async checkConfirmations(transactionId: string, tronAddress: string) {\n    try {\n      console.log(`Checking confirmations for transaction: ${transactionId}`);\n\n      // Get minimum confirmations setting\n      const minConfirmations = parseInt(await adminSettingsDb.get('minConfirmations') || '10');\n\n      // Re-verify to get current confirmation count\n      const verificationResult = await verifyUSDTTransaction(transactionId, tronAddress, 1);\n\n      if (!verificationResult.isValid) {\n        console.log(`Transaction ${transactionId} is no longer valid during confirmation check`);\n        return;\n      }\n\n      // Update confirmation count\n      await depositTransactionDb.updateConfirmations(transactionId, verificationResult.confirmations);\n\n      console.log(`Transaction ${transactionId} has ${verificationResult.confirmations} confirmations (required: ${minConfirmations})`);\n\n      // Check if it now has enough confirmations\n      if (verificationResult.confirmations >= minConfirmations) {\n        await this.completeDeposit(transactionId, verificationResult.amount);\n        \n        // Stop checking confirmations for this transaction\n        const interval = confirmationIntervals.get(transactionId);\n        if (interval) {\n          clearInterval(interval);\n          confirmationIntervals.delete(transactionId);\n        }\n      }\n\n    } catch (error) {\n      console.error(`Error checking confirmations for transaction ${transactionId}:`, error);\n      await systemLogDb.create({\n        action: 'CONFIRMATION_CHECK_ERROR',\n        details: `Error checking confirmations for ${transactionId}: ${error instanceof Error ? error.message : 'Unknown error'}`,\n      });\n    }\n  }\n\n  /**\n   * Complete a deposit by crediting the user's wallet\n   */\n  private async completeDeposit(transactionId: string, amount: number) {\n    try {\n      console.log(`Completing deposit for transaction: ${transactionId} with amount: ${amount}`);\n\n      // Get deposit record to get user ID\n      const deposit = await depositTransactionDb.findByTransactionId(transactionId);\n      if (!deposit) {\n        throw new Error('Deposit record not found');\n      }\n\n      // Check if deposit is already completed to prevent double processing\n      if (deposit.status === 'CONFIRMED' || deposit.status === 'COMPLETED') {\n        console.log(`Deposit ${transactionId} already completed, skipping...`);\n        return;\n      }\n\n      // Use database transaction to ensure atomicity\n      await prisma.$transaction(async (tx) => {\n        // Update deposit status to confirmed\n        await tx.depositTransaction.update({\n          where: { transactionId },\n          data: {\n            status: 'CONFIRMED',\n            verifiedAt: new Date(),\n            processedAt: new Date(),\n          },\n        });\n\n        // Get current wallet balance\n        const currentWallet = await tx.walletBalance.findUnique({\n          where: { userId: deposit.userId },\n        });\n\n        if (!currentWallet) {\n          // Create wallet if it doesn't exist\n          await tx.walletBalance.create({\n            data: {\n              userId: deposit.userId,\n              availableBalance: amount,\n              pendingBalance: 0,\n              totalDeposits: amount,\n              totalWithdrawals: 0,\n              totalEarnings: 0,\n            },\n          });\n        } else {\n          // Update existing wallet\n          await tx.walletBalance.update({\n            where: { userId: deposit.userId },\n            data: {\n              availableBalance: currentWallet.availableBalance + amount,\n              totalDeposits: currentWallet.totalDeposits + amount,\n              lastUpdated: new Date(),\n            },\n          });\n        }\n\n        // Create transaction record for balance tracking\n        await tx.transaction.create({\n          data: {\n            userId: deposit.userId,\n            type: 'DEPOSIT',\n            amount: amount,\n            description: `USDT TRC20 Deposit - TX: ${transactionId}`,\n            status: 'COMPLETED',\n          },\n        });\n      });\n\n      await systemLogDb.create({\n        action: 'DEPOSIT_COMPLETED',\n        userId: deposit.userId,\n        details: `Deposit completed: ${amount} USDT from transaction ${transactionId}`,\n      });\n\n      console.log(`Deposit completed successfully for transaction: ${transactionId}`);\n\n    } catch (error) {\n      console.error(`Error completing deposit for transaction ${transactionId}:`, error);\n\n      // Mark as failed if completion fails\n      await depositTransactionDb.updateStatus(transactionId, 'FAILED', {\n        failureReason: `Completion error: ${error instanceof Error ? error.message : 'Unknown error'}`,\n        processedAt: new Date(),\n      });\n\n      await systemLogDb.create({\n        action: 'DEPOSIT_COMPLETION_ERROR',\n        details: `Error completing deposit ${transactionId}: ${error instanceof Error ? error.message : 'Unknown error'}`,\n      });\n    }\n  }\n\n  /**\n   * Add a new transaction for verification\n   */\n  async addTransactionForVerification(transactionId: string, tronAddress: string) {\n    if (!this.isRunning) {\n      console.log('Deposit verification service is not running, starting verification manually');\n    }\n\n    this.scheduleVerification(transactionId, tronAddress);\n  }\n\n  /**\n   * Get service status\n   */\n  getStatus() {\n    return {\n      isRunning: this.isRunning,\n      activeVerifications: activeVerifications.size,\n      confirmationChecks: confirmationIntervals.size,\n    };\n  }\n}\n\n// Export singleton instance\nexport const depositVerificationService = DepositVerificationService.getInstance();\n"], "names": [], "mappings": ";;;;AAAA;AAOA;AACA;;;;AAGA,mEAAmE;AACnE,MAAM,sBAAsB,IAAI;AAEhC,+CAA+C;AAC/C,MAAM,wBAAwB,IAAI;AAK3B,MAAM;IACX,OAAe,SAAqC;IAC5C,YAAY,MAAM;IAE1B,OAAO,cAA0C;QAC/C,IAAI,CAAC,2BAA2B,QAAQ,EAAE;YACxC,2BAA2B,QAAQ,GAAG,IAAI;QAC5C;QACA,OAAO,2BAA2B,QAAQ;IAC5C;IAEA;;GAEC,GACD,MAAM,QAAQ;QACZ,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,GAAG;QACjB,QAAQ,GAAG,CAAC;QAEZ,iDAAiD;QACjD,MAAM,IAAI,CAAC,2BAA2B;QAEtC,sDAAsD;QACtD,MAAM,IAAI,CAAC,8BAA8B;QAEzC,QAAQ,GAAG,CAAC;IACd;IAEA;;GAEC,GACD,OAAO;QACL,IAAI,CAAC,SAAS,GAAG;QAEjB,6BAA6B;QAC7B,sBAAsB,OAAO,CAAC,CAAC;YAC7B,aAAa;QACf;QACA,sBAAsB,KAAK;QAC3B,oBAAoB,KAAK;QAEzB,QAAQ,GAAG,CAAC;IACd;IAEA;;GAEC,GACD,MAAc,8BAA8B;QAC1C,IAAI;YACF,MAAM,kBAAkB,MAAM,wHAAA,CAAA,uBAAoB,CAAC,8BAA8B;YACjF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,gBAAgB,MAAM,CAAC,8BAA8B,CAAC;YAE3E,KAAK,MAAM,WAAW,gBAAiB;gBACrC,IAAI,CAAC,oBAAoB,GAAG,CAAC,QAAQ,aAAa,GAAG;oBACnD,IAAI,CAAC,oBAAoB,CAAC,QAAQ,aAAa,EAAE,QAAQ,WAAW;gBACtE;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBACvB,QAAQ;gBACR,SAAS,CAAC,wCAAwC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YAChH;QACF;IACF;IAEA;;GAEC,GACD,MAAc,iCAAiC;QAC7C,IAAI;YACF,MAAM,kBAAkB,MAAM,wHAAA,CAAA,uBAAoB,CAAC,kCAAkC;YACrF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,gBAAgB,MAAM,CAAC,mCAAmC,CAAC;YAEhF,KAAK,MAAM,WAAW,gBAAiB;gBACrC,IAAI,CAAC,sBAAsB,GAAG,CAAC,QAAQ,aAAa,GAAG;oBACrD,IAAI,CAAC,yBAAyB,CAAC,QAAQ,aAAa,EAAE,QAAQ,WAAW;gBAC3E;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBACvB,QAAQ;gBACR,SAAS,CAAC,4CAA4C,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YACpH;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,qBAAqB,aAAqB,EAAE,WAAmB,EAAE;QACvE,IAAI,oBAAoB,GAAG,CAAC,gBAAgB;YAC1C,QAAQ,0BAA0B;QACpC;QAEA,oBAAoB,GAAG,CAAC,eAAe;QACvC,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,eAAe;QAEvE,iCAAiC;QACjC,IAAI,CAAC,iBAAiB,CAAC,eAAe,aAAa;QAEnD,+CAA+C;QAC/C,WAAW;YACT,IAAI,CAAC,iBAAiB,CAAC,eAAe,aAAa;QACrD,GAAG;IACL;IAEA;;GAEC,GACD,MAAc,kBAAkB,aAAqB,EAAE,WAAmB,EAAE,OAAgB,EAAE;QAC5F,IAAI;YACF,QAAQ,GAAG,CAAC,GAAG,UAAU,aAAa,aAAa,+BAA+B,EAAE,eAAe;YAEnG,oCAAoC;YACpC,MAAM,mBAAmB,SAAS,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,uBAAuB;YAEnF,sCAAsC;YACtC,MAAM,mBAAmB,WAAW,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,uBAAuB;YACrF,MAAM,mBAAmB,WAAW,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,uBAAuB;YAErF,sCAAsC;YACtC,MAAM,sBAAsB,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD,EAAE,eAAe,aAAa;YAC9E,MAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,SACrC,WAAW,IAAM,OAAO,IAAI,MAAM,0BAA0B;YAG9D,MAAM,qBAAqB,MAAM,QAAQ,IAAI,CAAC;gBAAC;gBAAqB;aAAe;YAEnF,IAAI,CAAC,mBAAmB,OAAO,IAAI,mBAAmB,aAAa,KAAK,GAAG;gBACzE,IAAI,SAAS;oBACX,wCAAwC;oBACxC,MAAM,wHAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,eAAe,UAAU;wBAC/D,eAAe;wBACf,aAAa,IAAI;oBACnB;oBAEA,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;wBACvB,QAAQ;wBACR,SAAS,CAAC,YAAY,EAAE,cAAc,gCAAgC,CAAC;oBACzE;oBAEA,oBAAoB,MAAM,CAAC;gBAC7B;gBACA;YACF;YAEA,6BAA6B;YAC7B,MAAM,oBAAoB,mBAAmB,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,GAAG,KAAK,CAAC,GAAG,QACvF,YAAY,WAAW,GAAG,QAAQ,CAAC,mBAAmB,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,GAAG;YAEjH,IAAI,CAAC,mBAAmB;gBACtB,MAAM,wHAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,eAAe,UAAU;oBAC/D,eAAe;oBACf,aAAa,IAAI;gBACnB;gBACA,oBAAoB,MAAM,CAAC;gBAC3B;YACF;YAEA,0BAA0B;YAC1B,IAAI,mBAAmB,MAAM,GAAG,kBAAkB;gBAChD,MAAM,wHAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,eAAe,UAAU;oBAC/D,eAAe,CAAC,eAAe,EAAE,mBAAmB,MAAM,CAAC,uBAAuB,EAAE,iBAAiB,KAAK,CAAC;oBAC3G,aAAa,IAAI;gBACnB;gBACA,oBAAoB,MAAM,CAAC;gBAC3B;YACF;YAEA,IAAI,mBAAmB,MAAM,GAAG,kBAAkB;gBAChD,MAAM,wHAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,eAAe,UAAU;oBAC/D,eAAe,CAAC,eAAe,EAAE,mBAAmB,MAAM,CAAC,sBAAsB,EAAE,iBAAiB,KAAK,CAAC;oBAC1G,aAAa,IAAI;gBACnB;gBACA,oBAAoB,MAAM,CAAC;gBAC3B;YACF;YAEA,qEAAqE;YACrE,2DAA2D;YAC3D,MAAM,sHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;gBACrC,OAAO;oBAAE;gBAAc;gBACvB,MAAM;oBACJ,QAAQ,mBAAmB,MAAM;oBACjC,YAAY,mBAAmB,MAAM;oBACrC,eAAe,mBAAmB,WAAW;oBAC7C,aAAa,mBAAmB,WAAW,CAAC,QAAQ;oBACpD,gBAAgB,IAAI,KAAK,mBAAmB,cAAc;oBAC1D,eAAe,mBAAmB,aAAa;gBACjD;YACF;YAEA,MAAM,wHAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,eAAe,WAAW;gBAChE,eAAe,mBAAmB,aAAa;YACjD;YAEA,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,cAAc,eAAe,EAAE,mBAAmB,aAAa,CAAC,0BAA0B,EAAE,iBAAiB,CAAC,CAAC;YAE1I,uCAAuC;YACvC,IAAI,mBAAmB,aAAa,IAAI,kBAAkB;gBACxD,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,mBAAmB,MAAM;YACrE,OAAO;gBACL,yDAAyD;gBACzD,MAAM,wHAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,eAAe;gBACvD,IAAI,CAAC,yBAAyB,CAAC,eAAe;YAChD;YAEA,oBAAoB,MAAM,CAAC;QAE7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,cAAc,CAAC,CAAC,EAAE;YAE/D,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,MAAM,iBAAiB,aAAa,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC;YAErH,IAAI,WAAW,CAAC,gBAAgB;gBAC9B,yDAAyD;gBACzD,MAAM,wHAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,eAAe,UAAU;oBAC/D,eAAe,CAAC,oBAAoB,EAAE,cAAc;oBACpD,aAAa,IAAI;gBACnB;gBACA,oBAAoB,MAAM,CAAC;gBAE3B,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oBACvB,QAAQ;oBACR,SAAS,CAAC,YAAY,EAAE,cAAc,sBAAsB,EAAE,cAAc;gBAC9E;YACF,OAAO;gBACL,4DAA4D;gBAC5D,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oBACvB,QAAQ;oBACR,SAAS,CAAC,oCAAoC,EAAE,cAAc,EAAE,EAAE,aAAa,aAAa,CAAC;gBAC/F;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,0BAA0B,aAAqB,EAAE,WAAmB,EAAE;QAC5E,IAAI,sBAAsB,GAAG,CAAC,gBAAgB;YAC5C,QAAQ,wBAAwB;QAClC;QAEA,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,eAAe;QAE9E,MAAM,WAAW,YAAY;YAC3B,MAAM,IAAI,CAAC,kBAAkB,CAAC,eAAe;QAC/C,GAAG,QAAQ,yBAAyB;QAEpC,sBAAsB,GAAG,CAAC,eAAe;QAEzC,yBAAyB;QACzB,IAAI,CAAC,kBAAkB,CAAC,eAAe;IACzC;IAEA;;GAEC,GACD,MAAc,mBAAmB,aAAqB,EAAE,WAAmB,EAAE;QAC3E,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,eAAe;YAEtE,oCAAoC;YACpC,MAAM,mBAAmB,SAAS,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,uBAAuB;YAEnF,8CAA8C;YAC9C,MAAM,qBAAqB,MAAM,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD,EAAE,eAAe,aAAa;YAEnF,IAAI,CAAC,mBAAmB,OAAO,EAAE;gBAC/B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,cAAc,6CAA6C,CAAC;gBACvF;YACF;YAEA,4BAA4B;YAC5B,MAAM,wHAAA,CAAA,uBAAoB,CAAC,mBAAmB,CAAC,eAAe,mBAAmB,aAAa;YAE9F,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,cAAc,KAAK,EAAE,mBAAmB,aAAa,CAAC,0BAA0B,EAAE,iBAAiB,CAAC,CAAC;YAEhI,2CAA2C;YAC3C,IAAI,mBAAmB,aAAa,IAAI,kBAAkB;gBACxD,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,mBAAmB,MAAM;gBAEnE,mDAAmD;gBACnD,MAAM,WAAW,sBAAsB,GAAG,CAAC;gBAC3C,IAAI,UAAU;oBACZ,cAAc;oBACd,sBAAsB,MAAM,CAAC;gBAC/B;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,6CAA6C,EAAE,cAAc,CAAC,CAAC,EAAE;YAChF,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBACvB,QAAQ;gBACR,SAAS,CAAC,iCAAiC,EAAE,cAAc,EAAE,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YAC3H;QACF;IACF;IAEA;;GAEC,GACD,MAAc,gBAAgB,aAAqB,EAAE,MAAc,EAAE;QACnE,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,cAAc,cAAc,EAAE,QAAQ;YAEzF,oCAAoC;YACpC,MAAM,UAAU,MAAM,wHAAA,CAAA,uBAAoB,CAAC,mBAAmB,CAAC;YAC/D,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,qEAAqE;YACrE,IAAI,QAAQ,MAAM,KAAK,eAAe,QAAQ,MAAM,KAAK,aAAa;gBACpE,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,cAAc,+BAA+B,CAAC;gBACrE;YACF;YAEA,+CAA+C;YAC/C,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;gBAC/B,qCAAqC;gBACrC,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC;oBACjC,OAAO;wBAAE;oBAAc;oBACvB,MAAM;wBACJ,QAAQ;wBACR,YAAY,IAAI;wBAChB,aAAa,IAAI;oBACnB;gBACF;gBAEA,6BAA6B;gBAC7B,MAAM,gBAAgB,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC;oBACtD,OAAO;wBAAE,QAAQ,QAAQ,MAAM;oBAAC;gBAClC;gBAEA,IAAI,CAAC,eAAe;oBAClB,oCAAoC;oBACpC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;wBAC5B,MAAM;4BACJ,QAAQ,QAAQ,MAAM;4BACtB,kBAAkB;4BAClB,gBAAgB;4BAChB,eAAe;4BACf,kBAAkB;4BAClB,eAAe;wBACjB;oBACF;gBACF,OAAO;oBACL,yBAAyB;oBACzB,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;wBAC5B,OAAO;4BAAE,QAAQ,QAAQ,MAAM;wBAAC;wBAChC,MAAM;4BACJ,kBAAkB,cAAc,gBAAgB,GAAG;4BACnD,eAAe,cAAc,aAAa,GAAG;4BAC7C,aAAa,IAAI;wBACnB;oBACF;gBACF;gBAEA,iDAAiD;gBACjD,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;oBAC1B,MAAM;wBACJ,QAAQ,QAAQ,MAAM;wBACtB,MAAM;wBACN,QAAQ;wBACR,aAAa,CAAC,yBAAyB,EAAE,eAAe;wBACxD,QAAQ;oBACV;gBACF;YACF;YAEA,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBACvB,QAAQ;gBACR,QAAQ,QAAQ,MAAM;gBACtB,SAAS,CAAC,mBAAmB,EAAE,OAAO,uBAAuB,EAAE,eAAe;YAChF;YAEA,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,eAAe;QAEhF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yCAAyC,EAAE,cAAc,CAAC,CAAC,EAAE;YAE5E,qCAAqC;YACrC,MAAM,wHAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,eAAe,UAAU;gBAC/D,eAAe,CAAC,kBAAkB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;gBAC9F,aAAa,IAAI;YACnB;YAEA,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBACvB,QAAQ;gBACR,SAAS,CAAC,yBAAyB,EAAE,cAAc,EAAE,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YACnH;QACF;IACF;IAEA;;GAEC,GACD,MAAM,8BAA8B,aAAqB,EAAE,WAAmB,EAAE;QAC9E,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,QAAQ,GAAG,CAAC;QACd;QAEA,IAAI,CAAC,oBAAoB,CAAC,eAAe;IAC3C;IAEA;;GAEC,GACD,YAAY;QACV,OAAO;YACL,WAAW,IAAI,CAAC,SAAS;YACzB,qBAAqB,oBAAoB,IAAI;YAC7C,oBAAoB,sBAAsB,IAAI;QAChD;IACF;AACF;AAGO,MAAM,6BAA6B,2BAA2B,WAAW", "debugId": null}}, {"offset": {"line": 1931, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/app/api/wallet/deposit/verify/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { authenticateRequest } from '@/lib/auth';\nimport {\n  depositTransactionDb,\n  walletBalanceDb,\n  transactionDb,\n  adminSettingsDb,\n  systemLogDb\n} from '@/lib/database';\nimport { verifyUSDTTransaction, isValidTronTransactionId } from '@/lib/trongrid';\nimport { depositVerificationService } from '@/lib/depositVerificationService';\n\n// Rate limiting map to prevent abuse\nconst rateLimitMap = new Map<string, { count: number; resetTime: number }>();\nconst RATE_LIMIT_WINDOW = 60000; // 1 minute\nconst RATE_LIMIT_MAX_REQUESTS = 5; // 5 requests per minute per user\n\nfunction checkRateLimit(userId: string): boolean {\n  const now = Date.now();\n  const userLimit = rateLimitMap.get(userId);\n\n  if (!userLimit || now > userLimit.resetTime) {\n    rateLimitMap.set(userId, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });\n    return true;\n  }\n\n  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {\n    return false;\n  }\n\n  userLimit.count++;\n  return true;\n}\n\n// POST - Verify deposit transaction\nexport async function POST(request: NextRequest) {\n  try {\n    const { authenticated, user } = await authenticateRequest(request);\n\n    if (!authenticated || !user) {\n      return NextResponse.json(\n        { success: false, error: 'Not authenticated' },\n        { status: 401 }\n      );\n    }\n\n    // Check rate limiting\n    if (!checkRateLimit(user.id)) {\n      return NextResponse.json(\n        { success: false, error: 'Too many verification requests. Please wait before trying again.' },\n        { status: 429 }\n      );\n    }\n\n    const body = await request.json();\n    const { transactionId } = body;\n\n    // Validation\n    if (!transactionId || typeof transactionId !== 'string') {\n      return NextResponse.json(\n        { success: false, error: 'Transaction ID is required' },\n        { status: 400 }\n      );\n    }\n\n    if (!isValidTronTransactionId(transactionId)) {\n      return NextResponse.json(\n        { success: false, error: 'Invalid Tron transaction ID format' },\n        { status: 400 }\n      );\n    }\n\n    // Check if transaction already exists\n    const existingDeposit = await depositTransactionDb.findByTransactionId(transactionId);\n    if (existingDeposit) {\n      await systemLogDb.create({\n        action: 'DEPOSIT_DUPLICATE_ATTEMPT',\n        userId: user.id,\n        details: {\n          transactionId,\n          existingDepositId: existingDeposit.id,\n          existingStatus: existingDeposit.status\n        },\n        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',\n        userAgent: request.headers.get('user-agent') || 'unknown',\n      });\n\n      return NextResponse.json(\n        {\n          success: false,\n          error: 'This transaction ID has already been submitted. Please check your deposit history.',\n          data: {\n            existingStatus: existingDeposit.status,\n            submittedAt: existingDeposit.createdAt,\n          }\n        },\n        { status: 400 }\n      );\n    }\n\n    // Get deposit settings - try both camelCase and UPPER_CASE keys for compatibility\n    let depositAddress = await adminSettingsDb.get('usdtDepositAddress');\n    if (!depositAddress) {\n      depositAddress = await adminSettingsDb.get('USDT_DEPOSIT_ADDRESS');\n    }\n\n    // Clean deposit address - remove quotes and extra characters\n    if (depositAddress) {\n      depositAddress = depositAddress.replace(/['\"]/g, '').trim();\n    }\n\n    let minDepositAmount = await adminSettingsDb.get('minDepositAmount');\n    if (!minDepositAmount) {\n      minDepositAmount = await adminSettingsDb.get('MIN_DEPOSIT_AMOUNT');\n    }\n    minDepositAmount = parseFloat(minDepositAmount || '10');\n\n    let maxDepositAmount = await adminSettingsDb.get('maxDepositAmount');\n    if (!maxDepositAmount) {\n      maxDepositAmount = await adminSettingsDb.get('MAX_DEPOSIT_AMOUNT');\n    }\n    maxDepositAmount = parseFloat(maxDepositAmount || '10000');\n\n    let depositEnabled = await adminSettingsDb.get('depositEnabled');\n    if (!depositEnabled) {\n      depositEnabled = await adminSettingsDb.get('DEPOSIT_ENABLED');\n    }\n    depositEnabled = depositEnabled === 'true' || depositEnabled === true;\n\n    let minConfirmations = await adminSettingsDb.get('minConfirmations');\n    if (!minConfirmations) {\n      minConfirmations = await adminSettingsDb.get('MIN_CONFIRMATIONS');\n    }\n    minConfirmations = parseInt(minConfirmations || '1');\n\n    if (!depositEnabled) {\n      return NextResponse.json(\n        { success: false, error: 'Deposits are currently disabled' },\n        { status: 503 }\n      );\n    }\n\n    if (!depositAddress) {\n      return NextResponse.json(\n        { success: false, error: 'Deposit address not configured. Please contact support.' },\n        { status: 503 }\n      );\n    }\n\n    // Log verification attempt\n    await systemLogDb.create({\n      action: 'DEPOSIT_VERIFICATION_ATTEMPT',\n      userId: user.id,\n      details: { transactionId },\n      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',\n      userAgent: request.headers.get('user-agent') || 'unknown',\n    });\n\n    // Immediately create deposit record with PENDING_VERIFICATION status\n    // This happens regardless of transaction validity\n    const depositRecord = await depositTransactionDb.create({\n      userId: user.id,\n      transactionId,\n      amount: 0, // Will be updated during verification\n      usdtAmount: 0, // Will be updated during verification\n      tronAddress: depositAddress,\n      senderAddress: '', // Will be updated during verification\n      blockNumber: '',\n      blockTimestamp: new Date(),\n      confirmations: 0,\n    });\n\n    // Update status to PENDING_VERIFICATION\n    await depositTransactionDb.updateStatus(transactionId, 'PENDING_VERIFICATION');\n\n    // Start background verification process\n    await depositVerificationService.addTransactionForVerification(transactionId, depositAddress);\n\n    // Log deposit submission\n    await systemLogDb.create({\n      action: 'DEPOSIT_SUBMITTED',\n      userId: user.id,\n      details: {\n        transactionId,\n        depositAddress,\n        status: 'PENDING_VERIFICATION',\n      },\n      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',\n      userAgent: request.headers.get('user-agent') || 'unknown',\n    });\n\n    // Return immediate success response\n    return NextResponse.json({\n      success: true,\n      message: 'Deposit transaction submitted successfully. We are now verifying your transaction. This may take up to 2 minutes.',\n      data: {\n        transactionId,\n        status: 'PENDING_VERIFICATION',\n        estimatedVerificationTime: 'Within 2 minutes',\n        nextSteps: [\n          'Transaction verification in progress',\n          'Confirmation checking will begin once transaction is found',\n          `Wallet will be credited automatically after ${minConfirmations} confirmations`,\n        ],\n      },\n    });\n\n  } catch (error) {\n    console.error('Deposit verification error:', error);\n    \n    // Log error for debugging\n    if (request.headers.get('authorization')) {\n      try {\n        const { user } = await authenticateRequest(request);\n        if (user) {\n          await systemLogDb.create({\n            action: 'DEPOSIT_VERIFICATION_ERROR',\n            userId: user.id,\n            details: { error: error instanceof Error ? error.message : 'Unknown error' },\n            ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',\n            userAgent: request.headers.get('user-agent') || 'unknown',\n          });\n        }\n      } catch (logError) {\n        console.error('Failed to log error:', logError);\n      }\n    }\n\n    return NextResponse.json(\n      { success: false, error: 'Failed to verify deposit. Please try again later.' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAOA;AACA;;;;;;AAEA,qCAAqC;AACrC,MAAM,eAAe,IAAI;AACzB,MAAM,oBAAoB,OAAO,WAAW;AAC5C,MAAM,0BAA0B,GAAG,iCAAiC;AAEpE,SAAS,eAAe,MAAc;IACpC,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,YAAY,aAAa,GAAG,CAAC;IAEnC,IAAI,CAAC,aAAa,MAAM,UAAU,SAAS,EAAE;QAC3C,aAAa,GAAG,CAAC,QAAQ;YAAE,OAAO;YAAG,WAAW,MAAM;QAAkB;QACxE,OAAO;IACT;IAEA,IAAI,UAAU,KAAK,IAAI,yBAAyB;QAC9C,OAAO;IACT;IAEA,UAAU,KAAK;IACf,OAAO;AACT;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAE1D,IAAI,CAAC,iBAAiB,CAAC,MAAM;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,IAAI,CAAC,eAAe,KAAK,EAAE,GAAG;YAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAmE,GAC5F;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,aAAa,EAAE,GAAG;QAE1B,aAAa;QACb,IAAI,CAAC,iBAAiB,OAAO,kBAAkB,UAAU;YACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA6B,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,CAAA,GAAA,wHAAA,CAAA,2BAAwB,AAAD,EAAE,gBAAgB;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAqC,GAC9D;gBAAE,QAAQ;YAAI;QAElB;QAEA,sCAAsC;QACtC,MAAM,kBAAkB,MAAM,wHAAA,CAAA,uBAAoB,CAAC,mBAAmB,CAAC;QACvE,IAAI,iBAAiB;YACnB,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBACvB,QAAQ;gBACR,QAAQ,KAAK,EAAE;gBACf,SAAS;oBACP;oBACA,mBAAmB,gBAAgB,EAAE;oBACrC,gBAAgB,gBAAgB,MAAM;gBACxC;gBACA,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBAAgB;gBACzF,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;YAClD;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;gBACP,MAAM;oBACJ,gBAAgB,gBAAgB,MAAM;oBACtC,aAAa,gBAAgB,SAAS;gBACxC;YACF,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,kFAAkF;QAClF,IAAI,iBAAiB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QAC/C,IAAI,CAAC,gBAAgB;YACnB,iBAAiB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QAC7C;QAEA,6DAA6D;QAC7D,IAAI,gBAAgB;YAClB,iBAAiB,eAAe,OAAO,CAAC,SAAS,IAAI,IAAI;QAC3D;QAEA,IAAI,mBAAmB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QACjD,IAAI,CAAC,kBAAkB;YACrB,mBAAmB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QAC/C;QACA,mBAAmB,WAAW,oBAAoB;QAElD,IAAI,mBAAmB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QACjD,IAAI,CAAC,kBAAkB;YACrB,mBAAmB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QAC/C;QACA,mBAAmB,WAAW,oBAAoB;QAElD,IAAI,iBAAiB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QAC/C,IAAI,CAAC,gBAAgB;YACnB,iBAAiB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QAC7C;QACA,iBAAiB,mBAAmB,UAAU,mBAAmB;QAEjE,IAAI,mBAAmB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QACjD,IAAI,CAAC,kBAAkB;YACrB,mBAAmB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QAC/C;QACA,mBAAmB,SAAS,oBAAoB;QAEhD,IAAI,CAAC,gBAAgB;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkC,GAC3D;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,gBAAgB;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA0D,GACnF;gBAAE,QAAQ;YAAI;QAElB;QAEA,2BAA2B;QAC3B,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACvB,QAAQ;YACR,QAAQ,KAAK,EAAE;YACf,SAAS;gBAAE;YAAc;YACzB,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBAAgB;YACzF,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;QAClD;QAEA,qEAAqE;QACrE,kDAAkD;QAClD,MAAM,gBAAgB,MAAM,wHAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC;YACtD,QAAQ,KAAK,EAAE;YACf;YACA,QAAQ;YACR,YAAY;YACZ,aAAa;YACb,eAAe;YACf,aAAa;YACb,gBAAgB,IAAI;YACpB,eAAe;QACjB;QAEA,wCAAwC;QACxC,MAAM,wHAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,eAAe;QAEvD,wCAAwC;QACxC,MAAM,0IAAA,CAAA,6BAA0B,CAAC,6BAA6B,CAAC,eAAe;QAE9E,yBAAyB;QACzB,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACvB,QAAQ;YACR,QAAQ,KAAK,EAAE;YACf,SAAS;gBACP;gBACA;gBACA,QAAQ;YACV;YACA,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBAAgB;YACzF,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;QAClD;QAEA,oCAAoC;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;gBACJ;gBACA,QAAQ;gBACR,2BAA2B;gBAC3B,WAAW;oBACT;oBACA;oBACA,CAAC,4CAA4C,EAAE,iBAAiB,cAAc,CAAC;iBAChF;YACH;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAE7C,0BAA0B;QAC1B,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB;YACxC,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;gBAC3C,IAAI,MAAM;oBACR,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;wBACvB,QAAQ;wBACR,QAAQ,KAAK,EAAE;wBACf,SAAS;4BAAE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAAgB;wBAC3E,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB,QAAQ,OAAO,CAAC,GAAG,CAAC,gBAAgB;wBACzF,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;oBAClD;gBACF;YACF,EAAE,OAAO,UAAU;gBACjB,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAoD,GAC7E;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}