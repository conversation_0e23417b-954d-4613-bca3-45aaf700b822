import { useState, useCallback, useRef } from 'react';

export interface TreeViewSettings {
  zoom: number;
  maxDepth: number;
  showInactive: boolean;
  expandedNodes: Set<string>;
  focusedNode: string | null;
  panOffset: { x: number; y: number };
}

export interface TreeInteractionHandlers {
  handleZoom: (direction: 'in' | 'out') => void;
  handleResetZoom: () => void;
  handleResetView: () => void;
  handleDepthChange: (depth: number) => void;
  handleToggleInactive: () => void;
  handleToggleExpansion: (nodeId: string) => void;
  handleFocusNode: (nodeId: string) => void;
  handlePan: (deltaX: number, deltaY: number) => void;
  handleExportTree: () => void;
}

export const useTreeInteractions = (
  initialSettings?: Partial<TreeViewSettings>
): [TreeViewSettings, TreeInteractionHandlers] => {
  const [viewSettings, setViewSettings] = useState<TreeViewSettings>({
    zoom: 1,
    maxDepth: 5,
    showInactive: true,
    expandedNodes: new Set(),
    focusedNode: null,
    panOffset: { x: 0, y: 0 },
    ...initialSettings,
  });

  const isDragging = useRef(false);
  const lastPanPoint = useRef({ x: 0, y: 0 });

  const handleZoom = useCallback((direction: 'in' | 'out') => {
    setViewSettings(prev => ({
      ...prev,
      zoom: direction === 'in' 
        ? Math.min(prev.zoom * 1.2, 3) 
        : Math.max(prev.zoom / 1.2, 0.5)
    }));
  }, []);

  const handleResetZoom = useCallback(() => {
    setViewSettings(prev => ({ ...prev, zoom: 1 }));
  }, []);

  const handleResetView = useCallback(() => {
    setViewSettings(prev => ({
      ...prev,
      zoom: 1,
      panOffset: { x: 0, y: 0 },
      focusedNode: null,
      expandedNodes: new Set(),
    }));
  }, []);

  const handleDepthChange = useCallback((depth: number) => {
    setViewSettings(prev => ({ ...prev, maxDepth: depth }));
  }, []);

  const handleToggleInactive = useCallback(() => {
    setViewSettings(prev => ({ ...prev, showInactive: !prev.showInactive }));
  }, []);

  const handleToggleExpansion = useCallback((nodeId: string) => {
    setViewSettings(prev => {
      const newExpanded = new Set(prev.expandedNodes);
      if (newExpanded.has(nodeId)) {
        newExpanded.delete(nodeId);
      } else {
        newExpanded.add(nodeId);
      }
      return { ...prev, expandedNodes: newExpanded };
    });
  }, []);

  const handleFocusNode = useCallback((nodeId: string) => {
    setViewSettings(prev => ({ ...prev, focusedNode: nodeId }));
  }, []);

  const handlePan = useCallback((deltaX: number, deltaY: number) => {
    setViewSettings(prev => ({
      ...prev,
      panOffset: {
        x: prev.panOffset.x + deltaX,
        y: prev.panOffset.y + deltaY,
      },
    }));
  }, []);

  const handleExportTree = useCallback(() => {
    // Create a simple text representation of the tree
    const exportData = {
      timestamp: new Date().toISOString(),
      settings: viewSettings,
      note: 'Binary tree export from HashCoreX',
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `binary-tree-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [viewSettings]);

  const handlers: TreeInteractionHandlers = {
    handleZoom,
    handleResetZoom,
    handleResetView,
    handleDepthChange,
    handleToggleInactive,
    handleToggleExpansion,
    handleFocusNode,
    handlePan,
    handleExportTree,
  };

  return [viewSettings, handlers];
};

// Hook for managing tree search functionality
export const useTreeSearch = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const performSearch = useCallback(async (term: string) => {
    if (!term.trim() || term.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch(`/api/referrals/search?term=${encodeURIComponent(term)}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSearchResults(data.data);
        }
      }
    } catch (error) {
      console.error('Search failed:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  const clearSearch = useCallback(() => {
    setSearchTerm('');
    setSearchResults([]);
  }, []);

  return {
    searchTerm,
    setSearchTerm,
    searchResults,
    isSearching,
    performSearch,
    clearSearch,
  };
};

// Hook for managing tree data fetching
export const useTreeData = () => {
  const [treeData, setTreeData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTreeData = useCallback(async (depth = 5, enhanced = true) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(
        `/api/referrals/tree?depth=${depth}&enhanced=${enhanced}`, 
        { credentials: 'include' }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setTreeData(data.data);
        } else {
          setError(data.error || 'Failed to fetch tree data');
        }
      } else {
        setError('Failed to fetch tree data');
      }
    } catch (err) {
      console.error('Failed to fetch tree data:', err);
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshTreeData = useCallback(() => {
    if (treeData) {
      fetchTreeData();
    }
  }, [fetchTreeData, treeData]);

  return {
    treeData,
    loading,
    error,
    fetchTreeData,
    refreshTreeData,
  };
};
