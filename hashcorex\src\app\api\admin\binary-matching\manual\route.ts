import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { processBinaryMatching } from '@/lib/referral';
import { systemLogDb } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    console.log(`Manual binary matching initiated by admin: ${user.email}`);

    // Process binary matching
    const result = await processBinaryMatching();

    // Log the manual trigger
    await systemLogDb.create({
      action: 'MANUAL_BINARY_MATCHING_TRIGGERED',
      details: {
        triggeredBy: user.email,
        adminId: user.id,
        result,
        timestamp: new Date().toISOString(),
      },
    });

    console.log(`Manual binary matching completed. Processed ${result.usersProcessed} users with total payouts: $${result.totalPayouts.toFixed(2)}`);

    return NextResponse.json({
      success: true,
      message: 'Binary matching process completed successfully',
      data: {
        usersProcessed: result.usersProcessed,
        totalPayouts: result.totalPayouts,
        matchingResults: result.matchingResults,
      },
    });

  } catch (error: any) {
    console.error('Manual binary matching error:', error);

    // Log the error
    try {
      const { user: errorUser } = await authenticateRequest(request);
      await systemLogDb.create({
        action: 'MANUAL_BINARY_MATCHING_ERROR',
        details: {
          triggeredBy: errorUser?.email || 'unknown',
          error: error.message,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (logError) {
      console.error('Failed to log manual binary matching error:', logError);
    }
    
    return NextResponse.json(
      { success: false, error: 'Failed to process binary matching' },
      { status: 500 }
    );
  }
}
