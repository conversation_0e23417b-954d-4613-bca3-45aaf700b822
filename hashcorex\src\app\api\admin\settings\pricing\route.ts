import { NextRequest, NextResponse } from 'next/server';
import { adminSettingsDb } from '@/lib/database';

// GET - Fetch dynamic pricing settings (public endpoint for frontend)
export async function GET(request: NextRequest) {
  try {
    // Get all settings from the new admin settings system
    const settings = await adminSettingsDb.getAll();

    // Convert to object format
    const settingsObject: any = {};
    settings.forEach(setting => {
      try {
        // Try to parse as JSO<PERSON> first, fallback to string
        settingsObject[setting.key] = JSON.parse(setting.value);
      } catch {
        settingsObject[setting.key] = setting.value;
      }
    });

    // Set default values if not exists
    const defaultSettings = {
      thsPriceUSD: 50.0,
      minPurchaseAmount: 100.0,
      maxPurchaseAmount: 10000.0,
      earningsRanges: [
        { minTHS: 0, maxTHS: 10, dailyReturnMin: 0.3, dailyReturnMax: 0.5, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
        { minTHS: 10, maxTHS: 50, dailyReturnMin: 0.4, dailyReturnMax: 0.6, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
        { minTHS: 50, maxTHS: 999999, dailyReturnMin: 0.5, dailyReturnMax: 0.7, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },
      ],
    };

    const finalSettings = { ...defaultSettings, ...settingsObject };

    const pricingData = {
      thsPrice: finalSettings.thsPriceUSD,
      minPurchaseAmount: finalSettings.minPurchaseAmount,
      maxPurchaseAmount: finalSettings.maxPurchaseAmount,
      earningsRanges: finalSettings.earningsRanges,
    };

    return NextResponse.json({
      success: true,
      data: pricingData,
    });

  } catch (error: any) {
    console.error('Pricing fetch error:', error);

    return NextResponse.json(
      { success: false, error: 'Failed to fetch pricing settings' },
      { status: 500 }
    );
  }
}
