'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, Button, Input } from '@/components/ui';
import { 
  DollarSign, 
  Copy, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  ExternalLink,
  RefreshCw,
  Info,
  Wallet
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';
import { DepositTransaction } from '@/types';

interface DepositInfo {
  depositAddress: string | null;
  minDepositAmount: number;
  maxDepositAmount: number;
  depositEnabled: boolean;
  minConfirmations: number;
  depositFeePercentage: number;
  network: string;
  currency: string;
  tronNetwork?: 'mainnet' | 'testnet';
  tronApiUrl?: string;
  usdtContract?: string;
}

interface UserStats {
  totalDeposited: number;
  depositCount: number;
  pendingDeposits: number;
}

export const DepositPage: React.FC = () => {
  const [depositInfo, setDepositInfo] = useState<DepositInfo | null>(null);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [deposits, setDeposits] = useState<DepositTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [transactionId, setTransactionId] = useState('');
  const [copied, setCopied] = useState(false);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  const fetchDepositInfo = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/wallet/deposit/info', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch deposit information');
      }

      const data = await response.json();
      if (data.success) {
        setDepositInfo(data.data.depositInfo);
        setUserStats(data.data.userStats);
        setDeposits(data.data.deposits);
      } else {
        throw new Error(data.error || 'Failed to fetch deposit information');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleCopyAddress = async () => {
    if (!depositInfo?.depositAddress) return;
    
    try {
      await navigator.clipboard.writeText(depositInfo.depositAddress);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy address:', err);
    }
  };

  const handleSubmitTransaction = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!transactionId.trim()) return;

    try {
      setSubmitting(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/wallet/deposit/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          transactionId: transactionId.trim(),
        }),
      });

      const data = await response.json();

      if (data.success) {
        if (data.data.status === 'PENDING_VERIFICATION') {
          setSuccess('Transaction submitted successfully! We are now verifying your deposit. This may take up to 2 minutes.');
        } else {
          setSuccess(`Deposit verified successfully! ${formatCurrency(data.data.amount)} USDT has been credited to your wallet.`);
        }
        setTransactionId('');
        // Refresh deposit info and history
        await fetchDepositInfo();
      } else {
        setError(data.error || 'Failed to verify transaction');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
      case 'CONFIRMED':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'PENDING_VERIFICATION':
        return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />;
      case 'WAITING_FOR_CONFIRMATIONS':
        return <Clock className="w-4 h-4 text-orange-600" />;
      case 'PENDING':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'FAILED':
      case 'REJECTED':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
      case 'CONFIRMED':
        return 'text-green-700 bg-green-100';
      case 'PENDING_VERIFICATION':
        return 'text-blue-700 bg-blue-100';
      case 'WAITING_FOR_CONFIRMATIONS':
        return 'text-orange-700 bg-orange-100';
      case 'PENDING':
        return 'text-yellow-700 bg-yellow-100';
      case 'FAILED':
      case 'REJECTED':
        return 'text-red-700 bg-red-100';
      default:
        return 'text-gray-700 bg-gray-100';
    }
  };

  const getStatusMessage = (status: string, confirmations?: number, minConfirmations?: number) => {
    switch (status) {
      case 'COMPLETED':
      case 'CONFIRMED':
        return 'Deposit completed successfully';
      case 'PENDING_VERIFICATION':
        return 'Verifying transaction on blockchain...';
      case 'WAITING_FOR_CONFIRMATIONS':
        return `Waiting for confirmations (${confirmations || 0}/${minConfirmations || 10})`;
      case 'PENDING':
        return 'Transaction verified, processing deposit...';
      case 'FAILED':
        return 'Transaction verification failed';
      case 'REJECTED':
        return 'Deposit rejected by admin';
      default:
        return 'Processing...';
    }
  };

  const getEstimatedTime = (status: string, confirmations?: number, minConfirmations?: number) => {
    switch (status) {
      case 'COMPLETED':
      case 'CONFIRMED':
        return null;
      case 'PENDING_VERIFICATION':
        return 'Within 2 minutes';
      case 'WAITING_FOR_CONFIRMATIONS':
        const remaining = (minConfirmations || 10) - (confirmations || 0);
        return remaining > 0 ? `~${remaining * 3} minutes` : 'Processing...';
      case 'PENDING':
        return 'Within 1 minute';
      default:
        return null;
    }
  };

  useEffect(() => {
    fetchDepositInfo();

    // Set up regular refresh every 30 seconds to keep data fresh
    const regularRefresh = setInterval(() => {
      fetchDepositInfo();
    }, 30000);

    // Start polling for status updates if there are pending deposits
    const startPolling = () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }

      const interval = setInterval(() => {
        fetchDepositInfo();
      }, 10000); // Poll every 10 seconds

      setPollingInterval(interval);
    };

    // Check if there are any pending deposits that need polling
    const hasPendingDeposits = deposits.some(deposit =>
      ['PENDING_VERIFICATION', 'WAITING_FOR_CONFIRMATIONS', 'PENDING'].includes(deposit.status)
    );

    if (hasPendingDeposits) {
      startPolling();
    }

    return () => {
      clearInterval(regularRefresh);
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [deposits.length]);

  // Stop polling when all deposits are completed or failed
  useEffect(() => {
    const hasPendingDeposits = deposits.some(deposit =>
      ['PENDING_VERIFICATION', 'WAITING_FOR_CONFIRMATIONS', 'PENDING'].includes(deposit.status)
    );

    if (!hasPendingDeposits && pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
  }, [deposits, pollingInterval]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (!depositInfo) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-red-600 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Deposit Service Unavailable</h3>
        <p className="text-gray-600">Unable to load deposit information. Please try again later.</p>
      </div>
    );
  }

  if (!depositInfo.depositEnabled) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Deposits Temporarily Disabled</h3>
        <p className="text-gray-600">Deposit service is currently unavailable. Please check back later.</p>
      </div>
    );
  }

  if (!depositInfo.depositAddress) {
    return (
      <div className="text-center py-12">
        <Wallet className="h-12 w-12 text-gray-600 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Deposit Address Not Configured</h3>
        <p className="text-gray-600">Please contact support to enable deposits.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Deposit USDT</h1>
          <p className="text-gray-600 mt-1">Add funds to your wallet using USDT TRC20</p>
        </div>
        <button
          onClick={fetchDepositInfo}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Refresh</span>
        </button>
      </div>

      {/* User Stats */}
      {userStats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Total Deposited</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(userStats.totalDeposited)} USDT</p>
                </div>
                <DollarSign className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white border-gray-200 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Successful Deposits</p>
                  <p className="text-2xl font-bold text-gray-900">{userStats.depositCount}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white border-gray-200 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Pending Deposits</p>
                  <p className="text-2xl font-bold text-gray-900">{userStats.pendingDeposits}</p>
                </div>
                <Clock className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Deposit Instructions */}
      <Card className="bg-white border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="text-gray-900 flex items-center space-x-2">
            <Info className="w-5 h-5 text-blue-600" />
            <span>How to Deposit</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                <div>
                  <p className="text-gray-900 font-medium">Send USDT TRC20 to the address below</p>
                  <p className="text-gray-600 text-sm">Only send USDT on the TRC20 network. Other tokens or networks will result in loss of funds.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                <div>
                  <p className="text-gray-900 font-medium">Copy your transaction ID</p>
                  <p className="text-gray-600 text-sm">After sending, copy the transaction ID from your wallet or block explorer.</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                <div>
                  <p className="text-gray-900 font-medium">Submit transaction ID below</p>
                  <p className="text-gray-600 text-sm">Paste your transaction ID in the form below to verify and credit your deposit.</p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Network:</span>
              <span className="text-gray-900 ml-2 font-medium">{depositInfo.network}</span>
            </div>
            <div>
              <span className="text-gray-600">Currency:</span>
              <span className="text-gray-900 ml-2 font-medium">{depositInfo.currency}</span>
            </div>
            <div>
              <span className="text-gray-600">Min Amount:</span>
              <span className="text-gray-900 ml-2 font-medium">{formatCurrency(depositInfo.minDepositAmount)} USDT</span>
            </div>
            <div>
              <span className="text-gray-600">Max Amount:</span>
              <span className="text-gray-900 ml-2 font-medium">{formatCurrency(depositInfo.maxDepositAmount)} USDT</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Deposit Address */}
      <Card className="bg-white border-gray-200 shadow-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-gray-900">Deposit Address</CardTitle>
            {depositInfo.tronNetwork && (
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  depositInfo.tronNetwork === 'mainnet' ? 'bg-green-500' : 'bg-orange-500'
                } animate-pulse`}></div>
                <span className="text-sm text-gray-600">
                  {depositInfo.tronNetwork === 'mainnet' ? 'Mainnet Active' : 'Testnet Active'}
                </span>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex-1 mr-4">
                <div className="flex items-center gap-2 mb-1">
                  <p className="text-gray-600 text-sm">USDT Address</p>
                  {depositInfo.tronNetwork && (
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      depositInfo.tronNetwork === 'mainnet'
                        ? 'bg-green-100 text-green-700'
                        : 'bg-orange-100 text-orange-700'
                    }`}>
                      {depositInfo.tronNetwork === 'mainnet' ? 'Mainnet' : 'Testnet'}
                    </span>
                  )}
                </div>
                <p className="text-gray-900 font-mono text-sm break-all">{depositInfo.depositAddress}</p>
              </div>
              <button
                onClick={handleCopyAddress}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                {copied ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                <span>{copied ? 'Copied!' : 'Copy'}</span>
              </button>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <p className="text-yellow-700 font-medium">Important Notice</p>
                <p className="text-gray-700 text-sm mt-1">
                  Only send USDT on the TRC20 network to this address. Sending other cryptocurrencies or using different networks will result in permanent loss of funds.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Transaction Verification Form */}
      <Card className="bg-white border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="text-gray-900">Verify Your Deposit</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmitTransaction} className="space-y-4">
            <div>
              <label className="block text-gray-700 text-sm mb-2">
                Transaction ID
              </label>
              <Input
                type="text"
                value={transactionId}
                onChange={(e) => setTransactionId(e.target.value)}
                placeholder="Enter your TRON transaction ID (64 characters)"
                className="bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500"
                disabled={submitting}
              />
              <p className="text-gray-600 text-xs mt-1">
                Transaction ID should be 64 characters long and contain only letters and numbers
              </p>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-700">{error}</p>
              </div>
            )}

            {success && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-green-700">{success}</p>
              </div>
            )}

            <Button
              type="submit"
              disabled={!transactionId.trim() || submitting}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"
            >
              {submitting ? (
                <div className="flex items-center space-x-2">
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Verifying Transaction...</span>
                </div>
              ) : (
                'Verify Deposit'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Recent Deposits */}
      {deposits.length > 0 && (
        <Card className="bg-white border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-gray-900">Recent Deposits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {deposits.map((deposit) => (
                <div key={deposit.id} className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(deposit.status)}
                      <div>
                        <p className="text-gray-900 font-medium">
                          {deposit.amount > 0 ? formatCurrency(deposit.amount) : '---'} USDT
                        </p>
                        <p className="text-gray-600 text-sm">
                          {formatDate(deposit.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(deposit.status)}`}>
                        <span>{deposit.status.replace('_', ' ')}</span>
                      </div>
                      <p className="text-gray-600 text-xs mt-1">
                        TX: {deposit.transactionId.slice(0, 8)}...{deposit.transactionId.slice(-8)}
                      </p>
                    </div>
                  </div>

                  {/* Status message and estimated time */}
                  <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                    <div className="flex items-center justify-between">
                      <p className="text-blue-700 text-sm">
                        {getStatusMessage(deposit.status, deposit.confirmations, depositInfo?.minConfirmations)}
                      </p>
                      {getEstimatedTime(deposit.status, deposit.confirmations, depositInfo?.minConfirmations) && (
                        <p className="text-blue-600 text-xs">
                          ETA: {getEstimatedTime(deposit.status, deposit.confirmations, depositInfo?.minConfirmations)}
                        </p>
                      )}
                    </div>
                    {deposit.confirmations > 0 && depositInfo?.minConfirmations && (
                      <div className="mt-2">
                        <div className="flex justify-between text-xs text-blue-600 mb-1">
                          <span>Confirmations</span>
                          <span>{deposit.confirmations}/{depositInfo.minConfirmations}</span>
                        </div>
                        <div className="w-full bg-blue-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${Math.min((deposit.confirmations / depositInfo.minConfirmations) * 100, 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>

                  {deposit.failureReason && (
                    <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                      <p className="text-red-700 text-sm">{deposit.failureReason}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
