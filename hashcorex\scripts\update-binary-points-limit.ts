import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function updateBinaryPointsLimit() {
  try {
    console.log('🔄 Updating binary points limit setting...');

    // Update the MAX_BINARY_POINTS_PER_SIDE setting from 2000 to 10
    const result = await prisma.adminSettings.upsert({
      where: { key: 'MAX_BINARY_POINTS_PER_SIDE' },
      update: { value: '10' },
      create: {
        key: 'MAX_BINARY_POINTS_PER_SIDE',
        value: '10',
      },
    });

    console.log(`✅ Updated MAX_BINARY_POINTS_PER_SIDE to: ${result.value}`);

    // Verify the update
    const currentSetting = await prisma.adminSettings.findUnique({
      where: { key: 'MAX_BINARY_POINTS_PER_SIDE' },
    });

    console.log(`📋 Current setting: ${currentSetting?.key} = ${currentSetting?.value}`);

    // Also show current binary points data
    const binaryPointsCount = await prisma.binaryPoints.count();
    const totalLeftPoints = await prisma.binaryPoints.aggregate({
      _sum: { leftPoints: true },
    });
    const totalRightPoints = await prisma.binaryPoints.aggregate({
      _sum: { rightPoints: true },
    });

    console.log('\n📊 Current Binary Points Summary:');
    console.log(`  Users with binary points: ${binaryPointsCount}`);
    console.log(`  Total left points: ${totalLeftPoints._sum.leftPoints || 0}`);
    console.log(`  Total right points: ${totalRightPoints._sum.rightPoints || 0}`);

    // Show users with points above the new limit
    const usersAboveLimit = await prisma.binaryPoints.findMany({
      where: {
        OR: [
          { leftPoints: { gt: 10 } },
          { rightPoints: { gt: 10 } },
        ],
      },
      include: {
        user: {
          select: {
            email: true,
          },
        },
      },
    });

    if (usersAboveLimit.length > 0) {
      console.log('\n⚠️  Users with points above new limit (10):');
      usersAboveLimit.forEach(bp => {
        console.log(`  ${bp.user.email}: Left=${bp.leftPoints}, Right=${bp.rightPoints}`);
      });
      console.log('\n💡 Consider using the "Reset All Points" feature in admin panel to test the new limit.');
    } else {
      console.log('\n✅ All users are within the new limit of 10 points per side.');
    }

    console.log('\n🎉 Binary points limit update completed successfully!');

  } catch (error) {
    console.error('❌ Error updating binary points limit:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
updateBinaryPointsLimit();
