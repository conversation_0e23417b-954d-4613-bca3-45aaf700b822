const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugTeamCount() {
  try {
    // Find <EMAIL> user
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        directReferralCount: true,
        totalLeftDownline: true,
        totalRightDownline: true,
        lastTreeUpdate: true,
        isActive: true
      }
    });

    if (!user) {
      console.log('User <EMAIL> not found');
      return;
    }

    console.log('User Info:', user);

    // Get all direct referrals (sponsored users)
    const directReferrals = await prisma.user.findMany({
      where: { referrerId: user.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        isActive: true,
        createdAt: true
      }
    });

    console.log('\nDirect Referrals (Sponsored Users):', directReferrals.length);
    directReferrals.forEach((ref, index) => {
      console.log(`${index + 1}. ${ref.email} - ${ref.firstName} ${ref.lastName} - Active: ${ref.isActive}`);
    });

    // Get all binary tree referrals (placed users)
    const binaryReferrals = await prisma.referral.findMany({
      where: { referrerId: user.id },
      include: {
        referred: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            isActive: true
          }
        }
      }
    });

    console.log('\nBinary Tree Referrals (Placed Users):', binaryReferrals.length);
    binaryReferrals.forEach((ref, index) => {
      console.log(`${index + 1}. ${ref.referred.email} - ${ref.referred.firstName} ${ref.referred.lastName} - Side: ${ref.placementSide} - Active: ${ref.referred.isActive}`);
    });

    // Get all downline users recursively
    async function getAllDownlineUsers(userId, visited = new Set()) {
      if (visited.has(userId)) return [];
      visited.add(userId);

      const directPlacements = await prisma.referral.findMany({
        where: { referrerId: userId },
        include: {
          referred: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
              isActive: true
            }
          }
        }
      });

      let allDownline = [];
      for (const placement of directPlacements) {
        allDownline.push(placement.referred);
        const subDownline = await getAllDownlineUsers(placement.referredId, visited);
        allDownline = allDownline.concat(subDownline);
      }

      return allDownline;
    }

    const allDownlineUsers = await getAllDownlineUsers(user.id);
    console.log('\nAll Downline Users (Binary Tree):', allDownlineUsers.length);
    allDownlineUsers.forEach((downline, index) => {
      console.log(`${index + 1}. ${downline.email} - ${downline.firstName} ${downline.lastName} - Active: ${downline.isActive}`);
    });

    const activeDownlineCount = allDownlineUsers.filter(u => u.isActive).length;
    console.log('\nActive Downline Count:', activeDownlineCount);

    // Check left and right side counts
    async function getDownlineForSide(userId, side) {
      const downlineUsers = [];
      const queue = [userId];
      
      while (queue.length > 0) {
        const currentUserId = queue.shift();
        
        const referrals = await prisma.referral.findMany({
          where: {
            referrerId: currentUserId,
            placementSide: side,
          },
          select: {
            referredId: true,
          },
        });
        
        for (const referral of referrals) {
          downlineUsers.push({ id: referral.referredId });
          queue.push(referral.referredId);
        }
      }
      
      return downlineUsers;
    }

    const leftDownline = await getDownlineForSide(user.id, 'LEFT');
    const rightDownline = await getDownlineForSide(user.id, 'RIGHT');

    console.log('\nLeft Side Downline:', leftDownline.length);
    console.log('Right Side Downline:', rightDownline.length);
    console.log('Total Binary Tree Count:', leftDownline.length + rightDownline.length);

    // Test the updated functions
    console.log('\n=== Testing Updated Functions ===');

    // Force cache update by clearing lastTreeUpdate
    await prisma.user.update({
      where: { id: user.id },
      data: { lastTreeUpdate: null }
    });

    // Import and test the updated functions
    const { getCachedDownlineCounts, getDetailedTeamStats } = require('./src/lib/referral');

    const updatedTeamCounts = await getCachedDownlineCounts(user.id);
    console.log('Updated Team Counts:', updatedTeamCounts);

    const detailedStats = await getDetailedTeamStats(user.id);
    console.log('Detailed Team Stats:', detailedStats);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugTeamCount();
