import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function initTronNetworkSettings() {
  console.log('🔧 Initializing Tron network settings...');

  try {
    // Initialize Tron network settings with default values
    const tronNetworkSettings = [
      { key: 'tronNetwork', value: 'testnet' }, // Default to testnet for safety
      { key: 'tronMainnetApiUrl', value: 'https://api.trongrid.io' },
      { key: 'tronTestnetApiUrl', value: 'https://api.shasta.trongrid.io' },
      { key: 'usdtMainnetContract', value: 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t' },
      { key: 'usdtTestnetContract', value: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs' },
      { key: 'minConfirmations', value: '10' }, // Update to 10 confirmations for better security
    ];

    console.log('📝 Creating/updating Tron network settings...');
    for (const setting of tronNetworkSettings) {
      await prisma.adminSettings.upsert({
        where: { key: setting.key },
        update: { value: setting.value },
        create: {
          key: setting.key,
          value: setting.value,
        },
      });
      console.log(`✅ Set ${setting.key} = ${setting.value}`);
    }

    console.log('✨ Tron network settings initialized successfully!');
    console.log('\n📋 Current Tron network settings:');
    
    const allSettings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: tronNetworkSettings.map(s => s.key)
        }
      }
    });

    allSettings.forEach(setting => {
      console.log(`  ${setting.key}: ${setting.value}`);
    });

    console.log('\n🔍 Network Configuration Summary:');
    const currentNetwork = allSettings.find(s => s.key === 'tronNetwork')?.value || 'testnet';
    const apiUrl = currentNetwork === 'mainnet' 
      ? allSettings.find(s => s.key === 'tronMainnetApiUrl')?.value 
      : allSettings.find(s => s.key === 'tronTestnetApiUrl')?.value;
    const usdtContract = currentNetwork === 'mainnet'
      ? allSettings.find(s => s.key === 'usdtMainnetContract')?.value
      : allSettings.find(s => s.key === 'usdtTestnetContract')?.value;

    console.log(`  Active Network: ${currentNetwork.toUpperCase()}`);
    console.log(`  API URL: ${apiUrl}`);
    console.log(`  USDT Contract: ${usdtContract}`);
    console.log(`  Min Confirmations: ${allSettings.find(s => s.key === 'minConfirmations')?.value}`);

    console.log('\n⚠️  Important Notes:');
    console.log('  - Default network is set to TESTNET for safety');
    console.log('  - Minimum confirmations set to 10 for better security');
    console.log('  - Switch to mainnet only when ready for production');
    console.log('  - Update deposit address to match the selected network');

  } catch (error) {
    console.error('❌ Error initializing Tron network settings:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the initialization
initTronNetworkSettings();
