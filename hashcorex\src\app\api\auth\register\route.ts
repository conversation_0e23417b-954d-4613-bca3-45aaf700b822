import { NextRequest, NextResponse } from 'next/server';
import { registerUser, validateEmail, validatePassword } from '@/lib/auth';
import { systemLogDb } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, firstName, lastName, password, confirmPassword, referralCode } = body;

    // Validation
    if (!email || !firstName || !lastName || !password || !confirmPassword) {
      return NextResponse.json(
        { success: false, error: 'All fields are required' },
        { status: 400 }
      );
    }

    if (!validateEmail(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    if (password !== confirmPassword) {
      return NextResponse.json(
        { success: false, error: 'Passwords do not match' },
        { status: 400 }
      );
    }

    const passwordValidation = validatePassword(password);
    if (!passwordValidation.valid) {
      return NextResponse.json(
        { success: false, error: passwordValidation.errors.join(', ') },
        { status: 400 }
      );
    }

    // Extract side parameter from URL if present
    const url = new URL(request.url);
    const side = url.searchParams.get('side') as 'left' | 'right' | null;

    // Register user
    const user = await registerUser({
      email,
      firstName,
      lastName,
      password,
      referralCode,
      placementSide: side || undefined,
    });

    // Log registration
    await systemLogDb.create({
      action: 'USER_REGISTERED',
      userId: user.id,
      details: {
        email: user.email,
        referralCode: referralCode || null,
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: 'Registration successful',
      data: user,
    });

  } catch (error: any) {
    console.error('Registration error:', error);
    
    return NextResponse.json(
      { success: false, error: error.message || 'Registration failed' },
      { status: 400 }
    );
  }
}
