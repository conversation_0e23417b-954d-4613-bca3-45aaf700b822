import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);
    
    if (!authenticated || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Check if user already has a deposit address
    let depositAddress = await prisma.depositAddress.findUnique({
      where: { userId: user.id },
    });

    // If no address exists, generate one
    if (!depositAddress) {
      // Generate a unique USDT TRC20 address
      // In a real implementation, you would integrate with a wallet service
      // For now, we'll generate a mock address
      const address = generateMockTRC20Address();
      
      depositAddress = await prisma.depositAddress.create({
        data: {
          userId: user.id,
          address,
          network: 'TRC20',
          currency: 'USDT',
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        address: depositAddress.address,
        network: depositAddress.network,
        currency: depositAddress.currency,
      },
    });

  } catch (error) {
    console.error('Deposit address error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get deposit address' },
      { status: 500 }
    );
  }
}

// Mock function to generate TRC20 address
// In production, integrate with actual wallet service
function generateMockTRC20Address(): string {
  const chars = '**********************************************************';
  let result = 'T'; // TRC20 addresses start with 'T'
  
  for (let i = 0; i < 33; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}
