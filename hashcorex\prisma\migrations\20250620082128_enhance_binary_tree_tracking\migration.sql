-- AlterTable
ALTER TABLE "binary_points" ADD COLUMN     "lastMatchDate" TIMESTAMP(3),
ADD COLUMN     "totalMatched" DOUBLE PRECISION NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "referrals" ADD COLUMN     "isDirectSponsor" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "directReferralCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "lastTreeUpdate" TIMESTAMP(3),
ADD COLUMN     "totalLeftDownline" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "totalRightDownline" INTEGER NOT NULL DEFAULT 0;

-- CreateIndex
CREATE INDEX "binary_points_leftPoints_idx" ON "binary_points"("leftPoints");

-- CreateIndex
CREATE INDEX "binary_points_rightPoints_idx" ON "binary_points"("rightPoints");

-- CreateIndex
CREATE INDEX "binary_points_flushDate_idx" ON "binary_points"("flushDate");

-- CreateIndex
CREATE INDEX "binary_points_lastMatchDate_idx" ON "binary_points"("lastMatchDate");

-- CreateIndex
CREATE INDEX "referrals_referrerId_placementSide_idx" ON "referrals"("referrerId", "placementSide");

-- CreateIndex
CREATE INDEX "referrals_referredId_idx" ON "referrals"("referredId");

-- CreateIndex
CREATE INDEX "referrals_createdAt_idx" ON "referrals"("createdAt");

-- CreateIndex
CREATE INDEX "referrals_isDirectSponsor_idx" ON "referrals"("isDirectSponsor");

-- CreateIndex
CREATE INDEX "users_referrerId_idx" ON "users"("referrerId");

-- CreateIndex
CREATE INDEX "users_referralId_idx" ON "users"("referralId");

-- CreateIndex
CREATE INDEX "users_leftReferralId_idx" ON "users"("leftReferralId");

-- CreateIndex
CREATE INDEX "users_rightReferralId_idx" ON "users"("rightReferralId");

-- CreateIndex
CREATE INDEX "users_createdAt_idx" ON "users"("createdAt");

-- CreateIndex
CREATE INDEX "users_isActive_idx" ON "users"("isActive");

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_referrerId_fkey" FOREIGN KEY ("referrerId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
