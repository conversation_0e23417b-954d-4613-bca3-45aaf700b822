'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui';
import {
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  Filter,
  Download,
  RefreshCw,
  Info
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/lib/utils';
import { DepositTransaction, DepositStatus } from '@/types';

interface DepositStats {
  totalDeposits: number;
  totalAmount: number;
  pendingDeposits: number;
}

interface DepositManagementProps {}

export const DepositManagement: React.FC<DepositManagementProps> = () => {
  const [deposits, setDeposits] = useState<DepositTransaction[]>([]);
  const [stats, setStats] = useState<DepositStats>({
    totalDeposits: 0,
    totalAmount: 0,
    pendingDeposits: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<DepositStatus | 'ALL'>('ALL');
  const [selectedDeposit, setSelectedDeposit] = useState<DepositTransaction | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  const fetchDeposits = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedStatus !== 'ALL') {
        params.append('status', selectedStatus);
      }
      params.append('limit', '50');

      const response = await fetch(`/api/admin/deposits?${params}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch deposits');
      }

      const data = await response.json();
      if (data.success) {
        setDeposits(data.data.deposits);
        setStats(data.data.stats);
      } else {
        throw new Error(data.error || 'Failed to fetch deposits');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleDepositAction = async (transactionId: string, action: string, reason?: string) => {
    try {
      setActionLoading(transactionId);
      
      const response = await fetch('/api/admin/deposits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          action,
          transactionId,
          reason,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to process deposit action');
      }

      const data = await response.json();
      if (data.success) {
        // Refresh deposits list
        await fetchDeposits();
        setSelectedDeposit(null);
      } else {
        throw new Error(data.error || 'Failed to process deposit action');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setActionLoading(null);
    }
  };

  useEffect(() => {
    fetchDeposits();
  }, [selectedStatus]);

  const getStatusIcon = (status: DepositStatus) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'PENDING':
        return <Clock className="w-4 h-4 text-yellow-400" />;
      case 'FAILED':
      case 'REJECTED':
        return <XCircle className="w-4 h-4 text-red-400" />;
      case 'VERIFYING':
        return <AlertTriangle className="w-4 h-4 text-blue-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: DepositStatus) => {
    switch (status) {
      case 'COMPLETED':
        return 'text-green-400 bg-green-400/10';
      case 'PENDING':
        return 'text-yellow-400 bg-yellow-400/10';
      case 'FAILED':
      case 'REJECTED':
        return 'text-red-400 bg-red-400/10';
      case 'VERIFYING':
        return 'text-blue-400 bg-blue-400/10';
      default:
        return 'text-gray-400 bg-gray-400/10';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-400" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-white">Deposit Management</h1>
        <div className="flex items-center space-x-4">
          <button
            onClick={fetchDeposits}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Deposits</p>
                <p className="text-2xl font-bold text-white">{stats.totalDeposits}</p>
              </div>
              <DollarSign className="w-8 h-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Total Amount</p>
                <p className="text-2xl font-bold text-white">{formatCurrency(stats.totalAmount)}</p>
              </div>
              <DollarSign className="w-8 h-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Pending Deposits</p>
                <p className="text-2xl font-bold text-white">{stats.pendingDeposits}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-gray-800 border-gray-700">
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <Filter className="w-5 h-5 text-gray-400" />
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as DepositStatus | 'ALL')}
              className="bg-gray-700 border border-gray-600 text-white rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="ALL">All Status</option>
              <option value="PENDING">Pending</option>
              <option value="VERIFYING">Verifying</option>
              <option value="CONFIRMED">Confirmed</option>
              <option value="COMPLETED">Completed</option>
              <option value="FAILED">Failed</option>
              <option value="REJECTED">Rejected</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Deposits Table */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">Recent Deposits</CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="mb-4 p-4 bg-red-900/20 border border-red-500 rounded-lg">
              <p className="text-red-400">{error}</p>
            </div>
          )}

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-3 px-4 text-gray-400 font-medium">User</th>
                  <th className="text-left py-3 px-4 text-gray-400 font-medium">Amount</th>
                  <th className="text-left py-3 px-4 text-gray-400 font-medium">Status</th>
                  <th className="text-left py-3 px-4 text-gray-400 font-medium">Transaction ID</th>
                  <th className="text-left py-3 px-4 text-gray-400 font-medium">Date</th>
                  <th className="text-left py-3 px-4 text-gray-400 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {deposits.map((deposit) => (
                  <tr key={deposit.id} className="border-b border-gray-700/50 hover:bg-gray-700/30">
                    <td className="py-3 px-4">
                      <div className="text-white">
                        {deposit.user ? `${deposit.user.firstName} ${deposit.user.lastName}` : 'Unknown'}
                      </div>
                      <div className="text-sm text-gray-400">
                        {deposit.user?.email || 'No email'}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-white font-medium">
                        {formatCurrency(deposit.usdtAmount)} USDT
                      </div>
                      <div className="text-sm text-gray-400">
                        {deposit.confirmations} confirmations
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className={`inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(deposit.status)}`}>
                        {getStatusIcon(deposit.status)}
                        <span>{deposit.status}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-white font-mono text-sm">
                        {deposit.transactionId.slice(0, 8)}...{deposit.transactionId.slice(-8)}
                      </div>
                    </td>
                    <td className="py-3 px-4 text-gray-300">
                      {formatDateTime(deposit.createdAt)}
                    </td>
                    <td className="py-3 px-4">
                      <button
                        onClick={() => setSelectedDeposit(deposit)}
                        className="flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors"
                      >
                        <Eye className="w-4 h-4" />
                        <span>View</span>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {deposits.length === 0 && (
              <div className="text-center py-8 text-gray-400">
                No deposits found
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Deposit Detail Modal */}
      {selectedDeposit && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">Deposit Details</h2>
              <button
                onClick={() => setSelectedDeposit(null)}
                className="text-gray-400 hover:text-white"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-gray-400 text-sm">User</label>
                  <p className="text-white">
                    {selectedDeposit.user ? `${selectedDeposit.user.firstName} ${selectedDeposit.user.lastName}` : 'Unknown'}
                  </p>
                  <p className="text-gray-400 text-sm">{selectedDeposit.user?.email}</p>
                </div>
                <div>
                  <label className="text-gray-400 text-sm">Amount</label>
                  <p className="text-white font-medium">{formatCurrency(selectedDeposit.usdtAmount)} USDT</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-gray-400 text-sm">Status</label>
                  <div className={`inline-flex items-center space-x-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedDeposit.status)}`}>
                    {getStatusIcon(selectedDeposit.status)}
                    <span>{selectedDeposit.status}</span>
                  </div>
                </div>
                <div>
                  <label className="text-gray-400 text-sm">Confirmations</label>
                  <p className="text-white">{selectedDeposit.confirmations}</p>
                </div>
              </div>

              <div>
                <label className="text-gray-400 text-sm">Transaction ID</label>
                <p className="text-white font-mono text-sm break-all">{selectedDeposit.transactionId}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-gray-400 text-sm">Sender Address</label>
                  <p className="text-white font-mono text-sm break-all">
                    {selectedDeposit.senderAddress || 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="text-gray-400 text-sm">Deposit Address</label>
                  <p className="text-white font-mono text-sm break-all">{selectedDeposit.tronAddress}</p>
                </div>
              </div>

              {selectedDeposit.failureReason && (
                <div>
                  <label className="text-gray-400 text-sm">Failure Reason</label>
                  <p className="text-red-400">{selectedDeposit.failureReason}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-gray-400 text-sm">Created At</label>
                  <p className="text-white">{formatDateTime(selectedDeposit.createdAt)}</p>
                </div>
                <div>
                  <label className="text-gray-400 text-sm">Processed At</label>
                  <p className="text-white">
                    {selectedDeposit.processedAt ? formatDateTime(selectedDeposit.processedAt) : 'Not processed'}
                  </p>
                </div>
              </div>
            </div>

            {/* Automated Processing Notice */}
            <div className="mt-6 pt-6 border-t border-gray-700">
              <div className="bg-blue-900/50 border border-blue-700 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <Info className="w-5 h-5 text-blue-400" />
                  <div>
                    <h4 className="text-blue-300 font-medium">Automated Processing</h4>
                    <p className="text-blue-200 text-sm mt-1">
                      Deposits are now processed automatically. The system verifies transactions and credits wallets once confirmations are met.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
