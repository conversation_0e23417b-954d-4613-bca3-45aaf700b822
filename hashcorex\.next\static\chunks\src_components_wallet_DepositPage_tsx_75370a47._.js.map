{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/wallet/DepositPage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardHeader, CardTitle, CardContent, Button, Input } from '@/components/ui';\nimport { \n  DollarSign, \n  Copy, \n  CheckCircle, \n  Clock, \n  AlertTriangle,\n  ExternalLink,\n  RefreshCw,\n  Info,\n  Wallet\n} from 'lucide-react';\nimport { formatCurrency, formatDate } from '@/lib/utils';\nimport { DepositTransaction } from '@/types';\n\ninterface DepositInfo {\n  depositAddress: string | null;\n  minDepositAmount: number;\n  maxDepositAmount: number;\n  depositEnabled: boolean;\n  minConfirmations: number;\n  depositFeePercentage: number;\n  network: string;\n  currency: string;\n  tronNetwork?: 'mainnet' | 'testnet';\n  tronApiUrl?: string;\n  usdtContract?: string;\n}\n\ninterface UserStats {\n  totalDeposited: number;\n  depositCount: number;\n  pendingDeposits: number;\n}\n\nexport const DepositPage: React.FC = () => {\n  const [depositInfo, setDepositInfo] = useState<DepositInfo | null>(null);\n  const [userStats, setUserStats] = useState<UserStats | null>(null);\n  const [deposits, setDeposits] = useState<DepositTransaction[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [transactionId, setTransactionId] = useState('');\n  const [copied, setCopied] = useState(false);\n  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);\n\n  const fetchDepositInfo = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/wallet/deposit/info', {\n        credentials: 'include',\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch deposit information');\n      }\n\n      const data = await response.json();\n      if (data.success) {\n        setDepositInfo(data.data.depositInfo);\n        setUserStats(data.data.userStats);\n        setDeposits(data.data.deposits);\n      } else {\n        throw new Error(data.error || 'Failed to fetch deposit information');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCopyAddress = async () => {\n    if (!depositInfo?.depositAddress) return;\n    \n    try {\n      await navigator.clipboard.writeText(depositInfo.depositAddress);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy address:', err);\n    }\n  };\n\n  const handleSubmitTransaction = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!transactionId.trim()) return;\n\n    try {\n      setSubmitting(true);\n      setError(null);\n      setSuccess(null);\n\n      const response = await fetch('/api/wallet/deposit/verify', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          transactionId: transactionId.trim(),\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        if (data.data.status === 'PENDING_VERIFICATION') {\n          setSuccess('Transaction submitted successfully! We are now verifying your deposit. This may take up to 2 minutes.');\n        } else {\n          setSuccess(`Deposit verified successfully! ${formatCurrency(data.data.amount)} USDT has been credited to your wallet.`);\n        }\n        setTransactionId('');\n        // Refresh deposit info and history\n        await fetchDepositInfo();\n      } else {\n        setError(data.error || 'Failed to verify transaction');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'COMPLETED':\n      case 'CONFIRMED':\n        return <CheckCircle className=\"w-4 h-4 text-green-600\" />;\n      case 'PENDING_VERIFICATION':\n        return <RefreshCw className=\"w-4 h-4 text-blue-600 animate-spin\" />;\n      case 'WAITING_FOR_CONFIRMATIONS':\n        return <Clock className=\"w-4 h-4 text-orange-600\" />;\n      case 'PENDING':\n        return <Clock className=\"w-4 h-4 text-yellow-600\" />;\n      case 'FAILED':\n      case 'REJECTED':\n        return <AlertTriangle className=\"w-4 h-4 text-red-600\" />;\n      default:\n        return <Clock className=\"w-4 h-4 text-gray-600\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'COMPLETED':\n      case 'CONFIRMED':\n        return 'text-green-700 bg-green-100';\n      case 'PENDING_VERIFICATION':\n        return 'text-blue-700 bg-blue-100';\n      case 'WAITING_FOR_CONFIRMATIONS':\n        return 'text-orange-700 bg-orange-100';\n      case 'PENDING':\n        return 'text-yellow-700 bg-yellow-100';\n      case 'FAILED':\n      case 'REJECTED':\n        return 'text-red-700 bg-red-100';\n      default:\n        return 'text-gray-700 bg-gray-100';\n    }\n  };\n\n  const getStatusMessage = (status: string, confirmations?: number, minConfirmations?: number) => {\n    switch (status) {\n      case 'COMPLETED':\n      case 'CONFIRMED':\n        return 'Deposit completed successfully';\n      case 'PENDING_VERIFICATION':\n        return 'Verifying transaction on blockchain...';\n      case 'WAITING_FOR_CONFIRMATIONS':\n        return `Waiting for confirmations (${confirmations || 0}/${minConfirmations || 10})`;\n      case 'PENDING':\n        return 'Transaction verified, processing deposit...';\n      case 'FAILED':\n        return 'Transaction verification failed';\n      case 'REJECTED':\n        return 'Deposit rejected by admin';\n      default:\n        return 'Processing...';\n    }\n  };\n\n  const getEstimatedTime = (status: string, confirmations?: number, minConfirmations?: number) => {\n    switch (status) {\n      case 'COMPLETED':\n      case 'CONFIRMED':\n        return null;\n      case 'PENDING_VERIFICATION':\n        return 'Within 2 minutes';\n      case 'WAITING_FOR_CONFIRMATIONS':\n        const remaining = (minConfirmations || 10) - (confirmations || 0);\n        return remaining > 0 ? `~${remaining * 3} minutes` : 'Processing...';\n      case 'PENDING':\n        return 'Within 1 minute';\n      default:\n        return null;\n    }\n  };\n\n  useEffect(() => {\n    fetchDepositInfo();\n\n    // Set up regular refresh every 30 seconds to keep data fresh\n    const regularRefresh = setInterval(() => {\n      fetchDepositInfo();\n    }, 30000);\n\n    // Start polling for status updates if there are pending deposits\n    const startPolling = () => {\n      if (pollingInterval) {\n        clearInterval(pollingInterval);\n      }\n\n      const interval = setInterval(() => {\n        fetchDepositInfo();\n      }, 10000); // Poll every 10 seconds\n\n      setPollingInterval(interval);\n    };\n\n    // Check if there are any pending deposits that need polling\n    const hasPendingDeposits = deposits.some(deposit =>\n      ['PENDING_VERIFICATION', 'WAITING_FOR_CONFIRMATIONS', 'PENDING'].includes(deposit.status)\n    );\n\n    if (hasPendingDeposits) {\n      startPolling();\n    }\n\n    return () => {\n      clearInterval(regularRefresh);\n      if (pollingInterval) {\n        clearInterval(pollingInterval);\n      }\n    };\n  }, [deposits.length]);\n\n  // Stop polling when all deposits are completed or failed\n  useEffect(() => {\n    const hasPendingDeposits = deposits.some(deposit =>\n      ['PENDING_VERIFICATION', 'WAITING_FOR_CONFIRMATIONS', 'PENDING'].includes(deposit.status)\n    );\n\n    if (!hasPendingDeposits && pollingInterval) {\n      clearInterval(pollingInterval);\n      setPollingInterval(null);\n    }\n  }, [deposits, pollingInterval]);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <RefreshCw className=\"w-8 h-8 animate-spin text-blue-600\" />\n      </div>\n    );\n  }\n\n  if (!depositInfo) {\n    return (\n      <div className=\"text-center py-12\">\n        <AlertTriangle className=\"h-12 w-12 text-red-600 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Deposit Service Unavailable</h3>\n        <p className=\"text-gray-600\">Unable to load deposit information. Please try again later.</p>\n      </div>\n    );\n  }\n\n  if (!depositInfo.depositEnabled) {\n    return (\n      <div className=\"text-center py-12\">\n        <AlertTriangle className=\"h-12 w-12 text-yellow-600 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Deposits Temporarily Disabled</h3>\n        <p className=\"text-gray-600\">Deposit service is currently unavailable. Please check back later.</p>\n      </div>\n    );\n  }\n\n  if (!depositInfo.depositAddress) {\n    return (\n      <div className=\"text-center py-12\">\n        <Wallet className=\"h-12 w-12 text-gray-600 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Deposit Address Not Configured</h3>\n        <p className=\"text-gray-600\">Please contact support to enable deposits.</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">Deposit USDT</h1>\n          <p className=\"text-gray-600 mt-1\">Add funds to your wallet using USDT TRC20</p>\n        </div>\n        <button\n          onClick={fetchDepositInfo}\n          className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\"\n        >\n          <RefreshCw className=\"w-4 h-4\" />\n          <span>Refresh</span>\n        </button>\n      </div>\n\n      {/* User Stats */}\n      {userStats && (\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <Card className=\"bg-white border-gray-200 shadow-sm\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-gray-600 text-sm\">Total Deposited</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{formatCurrency(userStats.totalDeposited)} USDT</p>\n                </div>\n                <DollarSign className=\"w-8 h-8 text-green-600\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-white border-gray-200 shadow-sm\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-gray-600 text-sm\">Successful Deposits</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{userStats.depositCount}</p>\n                </div>\n                <CheckCircle className=\"w-8 h-8 text-blue-600\" />\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-white border-gray-200 shadow-sm\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-gray-600 text-sm\">Pending Deposits</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{userStats.pendingDeposits}</p>\n                </div>\n                <Clock className=\"w-8 h-8 text-yellow-600\" />\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Deposit Instructions */}\n      <Card className=\"bg-white border-gray-200 shadow-sm\">\n        <CardHeader>\n          <CardTitle className=\"text-gray-900 flex items-center space-x-2\">\n            <Info className=\"w-5 h-5 text-blue-600\" />\n            <span>How to Deposit</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n            <div className=\"space-y-3\">\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold\">1</div>\n                <div>\n                  <p className=\"text-gray-900 font-medium\">Send USDT TRC20 to the address below</p>\n                  <p className=\"text-gray-600 text-sm\">Only send USDT on the TRC20 network. Other tokens or networks will result in loss of funds.</p>\n                </div>\n              </div>\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold\">2</div>\n                <div>\n                  <p className=\"text-gray-900 font-medium\">Copy your transaction ID</p>\n                  <p className=\"text-gray-600 text-sm\">After sending, copy the transaction ID from your wallet or block explorer.</p>\n                </div>\n              </div>\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold\">3</div>\n                <div>\n                  <p className=\"text-gray-900 font-medium\">Submit transaction ID below</p>\n                  <p className=\"text-gray-600 text-sm\">Paste your transaction ID in the form below to verify and credit your deposit.</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\n            <div>\n              <span className=\"text-gray-600\">Network:</span>\n              <span className=\"text-gray-900 ml-2 font-medium\">{depositInfo.network}</span>\n            </div>\n            <div>\n              <span className=\"text-gray-600\">Currency:</span>\n              <span className=\"text-gray-900 ml-2 font-medium\">{depositInfo.currency}</span>\n            </div>\n            <div>\n              <span className=\"text-gray-600\">Min Amount:</span>\n              <span className=\"text-gray-900 ml-2 font-medium\">{formatCurrency(depositInfo.minDepositAmount)} USDT</span>\n            </div>\n            <div>\n              <span className=\"text-gray-600\">Max Amount:</span>\n              <span className=\"text-gray-900 ml-2 font-medium\">{formatCurrency(depositInfo.maxDepositAmount)} USDT</span>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Deposit Address */}\n      <Card className=\"bg-white border-gray-200 shadow-sm\">\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <CardTitle className=\"text-gray-900\">Deposit Address</CardTitle>\n            {depositInfo.tronNetwork && (\n              <div className=\"flex items-center gap-2\">\n                <div className={`w-2 h-2 rounded-full ${\n                  depositInfo.tronNetwork === 'mainnet' ? 'bg-green-500' : 'bg-orange-500'\n                } animate-pulse`}></div>\n                <span className=\"text-sm text-gray-600\">\n                  {depositInfo.tronNetwork === 'mainnet' ? 'Mainnet Active' : 'Testnet Active'}\n                </span>\n              </div>\n            )}\n          </div>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex-1 mr-4\">\n                <div className=\"flex items-center gap-2 mb-1\">\n                  <p className=\"text-gray-600 text-sm\">USDT Address</p>\n                  {depositInfo.tronNetwork && (\n                    <span className={`px-2 py-1 text-xs rounded-full ${\n                      depositInfo.tronNetwork === 'mainnet'\n                        ? 'bg-green-100 text-green-700'\n                        : 'bg-orange-100 text-orange-700'\n                    }`}>\n                      {depositInfo.tronNetwork === 'mainnet' ? 'Mainnet' : 'Testnet'}\n                    </span>\n                  )}\n                </div>\n                <p className=\"text-gray-900 font-mono text-sm break-all\">{depositInfo.depositAddress}</p>\n              </div>\n              <button\n                onClick={handleCopyAddress}\n                className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\"\n              >\n                {copied ? <CheckCircle className=\"w-4 h-4\" /> : <Copy className=\"w-4 h-4\" />}\n                <span>{copied ? 'Copied!' : 'Copy'}</span>\n              </button>\n            </div>\n          </div>\n\n          <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n            <div className=\"flex items-start space-x-3\">\n              <AlertTriangle className=\"w-5 h-5 text-yellow-600 mt-0.5\" />\n              <div>\n                <p className=\"text-yellow-700 font-medium\">Important Notice</p>\n                <p className=\"text-gray-700 text-sm mt-1\">\n                  Only send USDT on the TRC20 network to this address. Sending other cryptocurrencies or using different networks will result in permanent loss of funds.\n                </p>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Transaction Verification Form */}\n      <Card className=\"bg-white border-gray-200 shadow-sm\">\n        <CardHeader>\n          <CardTitle className=\"text-gray-900\">Verify Your Deposit</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmitTransaction} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-gray-700 text-sm mb-2\">\n                Transaction ID\n              </label>\n              <Input\n                type=\"text\"\n                value={transactionId}\n                onChange={(e) => setTransactionId(e.target.value)}\n                placeholder=\"Enter your TRON transaction ID (64 characters)\"\n                className=\"bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500\"\n                disabled={submitting}\n              />\n              <p className=\"text-gray-600 text-xs mt-1\">\n                Transaction ID should be 64 characters long and contain only letters and numbers\n              </p>\n            </div>\n\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n                <p className=\"text-red-700\">{error}</p>\n              </div>\n            )}\n\n            {success && (\n              <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                <p className=\"text-green-700\">{success}</p>\n              </div>\n            )}\n\n            <Button\n              type=\"submit\"\n              disabled={!transactionId.trim() || submitting}\n              className=\"w-full bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50\"\n            >\n              {submitting ? (\n                <div className=\"flex items-center space-x-2\">\n                  <RefreshCw className=\"w-4 h-4 animate-spin\" />\n                  <span>Verifying Transaction...</span>\n                </div>\n              ) : (\n                'Verify Deposit'\n              )}\n            </Button>\n          </form>\n        </CardContent>\n      </Card>\n\n      {/* Recent Deposits */}\n      {deposits.length > 0 && (\n        <Card className=\"bg-white border-gray-200 shadow-sm\">\n          <CardHeader>\n            <CardTitle className=\"text-gray-900\">Recent Deposits</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {deposits.map((deposit) => (\n                <div key={deposit.id} className=\"bg-gray-50 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      {getStatusIcon(deposit.status)}\n                      <div>\n                        <p className=\"text-gray-900 font-medium\">\n                          {deposit.amount > 0 ? formatCurrency(deposit.amount) : '---'} USDT\n                        </p>\n                        <p className=\"text-gray-600 text-sm\">\n                          {formatDate(deposit.createdAt)}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(deposit.status)}`}>\n                        <span>{deposit.status.replace('_', ' ')}</span>\n                      </div>\n                      <p className=\"text-gray-600 text-xs mt-1\">\n                        TX: {deposit.transactionId.slice(0, 8)}...{deposit.transactionId.slice(-8)}\n                      </p>\n                    </div>\n                  </div>\n\n                  {/* Status message and estimated time */}\n                  <div className=\"mt-3 p-3 bg-blue-50 border border-blue-200 rounded\">\n                    <div className=\"flex items-center justify-between\">\n                      <p className=\"text-blue-700 text-sm\">\n                        {getStatusMessage(deposit.status, deposit.confirmations, depositInfo?.minConfirmations)}\n                      </p>\n                      {getEstimatedTime(deposit.status, deposit.confirmations, depositInfo?.minConfirmations) && (\n                        <p className=\"text-blue-600 text-xs\">\n                          ETA: {getEstimatedTime(deposit.status, deposit.confirmations, depositInfo?.minConfirmations)}\n                        </p>\n                      )}\n                    </div>\n                    {deposit.confirmations > 0 && depositInfo?.minConfirmations && (\n                      <div className=\"mt-2\">\n                        <div className=\"flex justify-between text-xs text-blue-600 mb-1\">\n                          <span>Confirmations</span>\n                          <span>{deposit.confirmations}/{depositInfo.minConfirmations}</span>\n                        </div>\n                        <div className=\"w-full bg-blue-200 rounded-full h-2\">\n                          <div\n                            className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                            style={{ width: `${Math.min((deposit.confirmations / depositInfo.minConfirmations) * 100, 100)}%` }}\n                          ></div>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n\n                  {deposit.failureReason && (\n                    <div className=\"mt-3 p-3 bg-red-50 border border-red-200 rounded\">\n                      <p className=\"text-red-700 text-sm\">{deposit.failureReason}</p>\n                    </div>\n                  )}\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAfA;;;;;AAsCO,MAAM,cAAwB;;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAE9E,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,aAAa;YACf;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,eAAe,KAAK,IAAI,CAAC,WAAW;gBACpC,aAAa,KAAK,IAAI,CAAC,SAAS;gBAChC,YAAY,KAAK,IAAI,CAAC,QAAQ;YAChC,OAAO;gBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,aAAa,gBAAgB;QAElC,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,YAAY,cAAc;YAC9D,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,0BAA0B,OAAO;QACrC,EAAE,cAAc;QAChB,IAAI,CAAC,cAAc,IAAI,IAAI;QAE3B,IAAI;YACF,cAAc;YACd,SAAS;YACT,WAAW;YAEX,MAAM,WAAW,MAAM,MAAM,8BAA8B;gBACzD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB,eAAe,cAAc,IAAI;gBACnC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,IAAI,KAAK,IAAI,CAAC,MAAM,KAAK,wBAAwB;oBAC/C,WAAW;gBACb,OAAO;oBACL,WAAW,CAAC,+BAA+B,EAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI,CAAC,MAAM,EAAE,uCAAuC,CAAC;gBACxH;gBACA,iBAAiB;gBACjB,mCAAmC;gBACnC,MAAM;YACR,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,mNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAgB,eAAwB;QAChE,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,2BAA2B,EAAE,iBAAiB,EAAE,CAAC,EAAE,oBAAoB,GAAG,CAAC,CAAC;YACtF,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC,QAAgB,eAAwB;QAChE,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,MAAM,YAAY,CAAC,oBAAoB,EAAE,IAAI,CAAC,iBAAiB,CAAC;gBAChE,OAAO,YAAY,IAAI,CAAC,CAAC,EAAE,YAAY,EAAE,QAAQ,CAAC,GAAG;YACvD,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;YAEA,6DAA6D;YAC7D,MAAM,iBAAiB;wDAAY;oBACjC;gBACF;uDAAG;YAEH,iEAAiE;YACjE,MAAM;sDAAe;oBACnB,IAAI,iBAAiB;wBACnB,cAAc;oBAChB;oBAEA,MAAM,WAAW;uEAAY;4BAC3B;wBACF;sEAAG,QAAQ,wBAAwB;oBAEnC,mBAAmB;gBACrB;;YAEA,4DAA4D;YAC5D,MAAM,qBAAqB,SAAS,IAAI;4DAAC,CAAA,UACvC;wBAAC;wBAAwB;wBAA6B;qBAAU,CAAC,QAAQ,CAAC,QAAQ,MAAM;;YAG1F,IAAI,oBAAoB;gBACtB;YACF;YAEA;yCAAO;oBACL,cAAc;oBACd,IAAI,iBAAiB;wBACnB,cAAc;oBAChB;gBACF;;QACF;gCAAG;QAAC,SAAS,MAAM;KAAC;IAEpB,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,qBAAqB,SAAS,IAAI;4DAAC,CAAA,UACvC;wBAAC;wBAAwB;wBAA6B;qBAAU,CAAC,QAAQ,CAAC,QAAQ,MAAM;;YAG1F,IAAI,CAAC,sBAAsB,iBAAiB;gBAC1C,cAAc;gBACd,mBAAmB;YACrB;QACF;gCAAG;QAAC;QAAU;KAAgB;IAE9B,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;IAG3B;IAEA,IAAI,CAAC,aAAa;QAChB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,IAAI,CAAC,YAAY,cAAc,EAAE;QAC/B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,IAAI,CAAC,YAAY,cAAc,EAAE;QAC/B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;8BAClB,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAEpC,6LAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;YAKT,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;;oDAAoC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,cAAc;oDAAE;;;;;;;;;;;;;kDAE5F,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK5B,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAoC,UAAU,YAAY;;;;;;;;;;;;kDAEzE,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK7B,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,6LAAC;gDAAE,WAAU;0DAAoC,UAAU,eAAe;;;;;;;;;;;;kDAE5E,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3B,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAiG;;;;;;8DAChH,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA4B;;;;;;sEACzC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAiG;;;;;;8DAChH,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA4B;;;;;;sEACzC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAiG;;;;;;8DAChH,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA4B;;;;;;sEACzC,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAkC,YAAY,OAAO;;;;;;;;;;;;kDAEvE,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAkC,YAAY,QAAQ;;;;;;;;;;;;kDAExE,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAkC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,gBAAgB;oDAAE;;;;;;;;;;;;;kDAEjG,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;;oDAAkC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,gBAAgB;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvG,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAgB;;;;;;gCACpC,YAAY,WAAW,kBACtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,qBAAqB,EACpC,YAAY,WAAW,KAAK,YAAY,iBAAiB,gBAC1D,cAAc,CAAC;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDACb,YAAY,WAAW,KAAK,YAAY,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;kCAMtE,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;wDACpC,YAAY,WAAW,kBACtB,6LAAC;4DAAK,WAAW,CAAC,+BAA+B,EAC/C,YAAY,WAAW,KAAK,YACxB,gCACA,iCACJ;sEACC,YAAY,WAAW,KAAK,YAAY,YAAY;;;;;;;;;;;;8DAI3D,6LAAC;oDAAE,WAAU;8DAA6C,YAAY,cAAc;;;;;;;;;;;;sDAEtF,6LAAC;4CACC,SAAS;4CACT,WAAU;;gDAET,uBAAS,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAAe,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChE,6LAAC;8DAAM,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;0CAKlC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA8B;;;;;;8DAC3C,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUpD,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAgB;;;;;;;;;;;kCAEvC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAK,UAAU;4BAAyB,WAAU;;8CACjD,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAmC;;;;;;sDAGpD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,aAAY;4CACZ,WAAU;4CACV,UAAU;;;;;;sDAEZ,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;gCAK3C,uBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;gCAIhC,yBACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAkB;;;;;;;;;;;8CAInC,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU,CAAC,cAAc,IAAI,MAAM;oCACnC,WAAU;8CAET,2BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAK;;;;;;;;;;;+CAGR;;;;;;;;;;;;;;;;;;;;;;;YAQT,SAAS,MAAM,GAAG,mBACjB,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAgB;;;;;;;;;;;kCAEvC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;oCAAqB,WAAU;;sDAC9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDACZ,cAAc,QAAQ,MAAM;sEAC7B,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;;wEACV,QAAQ,MAAM,GAAG,IAAI,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM,IAAI;wEAAM;;;;;;;8EAE/D,6LAAC;oEAAE,WAAU;8EACV,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,SAAS;;;;;;;;;;;;;;;;;;8DAInC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAC,8EAA8E,EAAE,eAAe,QAAQ,MAAM,GAAG;sEAC/H,cAAA,6LAAC;0EAAM,QAAQ,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;sEAErC,6LAAC;4DAAE,WAAU;;gEAA6B;gEACnC,QAAQ,aAAa,CAAC,KAAK,CAAC,GAAG;gEAAG;gEAAI,QAAQ,aAAa,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;;;;;;;sDAM9E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEACV,iBAAiB,QAAQ,MAAM,EAAE,QAAQ,aAAa,EAAE,aAAa;;;;;;wDAEvE,iBAAiB,QAAQ,MAAM,EAAE,QAAQ,aAAa,EAAE,aAAa,mCACpE,6LAAC;4DAAE,WAAU;;gEAAwB;gEAC7B,iBAAiB,QAAQ,MAAM,EAAE,QAAQ,aAAa,EAAE,aAAa;;;;;;;;;;;;;gDAIhF,QAAQ,aAAa,GAAG,KAAK,aAAa,kCACzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK;;;;;;8EACN,6LAAC;;wEAAM,QAAQ,aAAa;wEAAC;wEAAE,YAAY,gBAAgB;;;;;;;;;;;;;sEAE7D,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,KAAK,GAAG,CAAC,AAAC,QAAQ,aAAa,GAAG,YAAY,gBAAgB,GAAI,KAAK,KAAK,CAAC,CAAC;gEAAC;;;;;;;;;;;;;;;;;;;;;;;wCAO3G,QAAQ,aAAa,kBACpB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAwB,QAAQ,aAAa;;;;;;;;;;;;mCArDtD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEpC;GA1iBa;KAAA", "debugId": null}}]}