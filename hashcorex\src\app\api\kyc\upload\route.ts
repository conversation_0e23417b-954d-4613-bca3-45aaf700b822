import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { systemLogDb } from '@/lib/database';
import { prisma } from '@/lib/prisma';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

// POST - Upload KYC document
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const documentType = formData.get('documentType') as string;
    const idType = formData.get('idType') as string;
    const documentSide = formData.get('documentSide') as string;

    // Validation
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!documentType || !['ID_DOCUMENT', 'SELFIE'].includes(documentType)) {
      return NextResponse.json(
        { success: false, error: 'Invalid document type' },
        { status: 400 }
      );
    }

    // Validate idType for ID documents
    if (documentType === 'ID_DOCUMENT') {
      if (!idType || !['NATIONAL_ID', 'PASSPORT', 'DRIVING_LICENSE'].includes(idType)) {
        return NextResponse.json(
          { success: false, error: 'Invalid ID type' },
          { status: 400 }
        );
      }

      if (!documentSide || !['FRONT', 'BACK'].includes(documentSide)) {
        return NextResponse.json(
          { success: false, error: 'Invalid document side' },
          { status: 400 }
        );
      }
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { success: false, error: 'Only image files are allowed' },
        { status: 400 }
      );
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { success: false, error: 'File size must be less than 5MB' },
        { status: 400 }
      );
    }

    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'kyc');
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop();
    const fileNameParts = [user.id, documentType];
    if (idType) fileNameParts.push(idType);
    if (documentSide) fileNameParts.push(documentSide);
    fileNameParts.push(uuidv4());
    const fileName = `${fileNameParts.join('_')}.${fileExtension}`;
    const filePath = join(uploadDir, fileName);
    const publicPath = `/uploads/kyc/${fileName}`;

    // Save file
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Check if document already exists and update or create
    const whereClause: any = {
      userId: user.id,
      documentType: documentType as 'ID_DOCUMENT' | 'SELFIE',
    };

    // For ID documents, also match idType and documentSide
    if (documentType === 'ID_DOCUMENT') {
      whereClause.idType = idType as 'NATIONAL_ID' | 'PASSPORT' | 'DRIVING_LICENSE';
      whereClause.documentSide = documentSide as 'FRONT' | 'BACK';
    }

    const existingDocument = await prisma.kYCDocument.findFirst({
      where: whereClause,
    });

    let kycDocument;

    if (existingDocument) {
      // Update existing document
      kycDocument = await prisma.kYCDocument.update({
        where: { id: existingDocument.id },
        data: {
          filePath: publicPath,
          status: 'PENDING',
          reviewedAt: null,
          reviewedBy: null,
          rejectionReason: null,
        },
      });
    } else {
      // Create new document
      const createData: any = {
        userId: user.id,
        documentType: documentType as 'ID_DOCUMENT' | 'SELFIE',
        filePath: publicPath,
        status: 'PENDING',
      };

      // Add idType and documentSide for ID documents
      if (documentType === 'ID_DOCUMENT') {
        createData.idType = idType as 'NATIONAL_ID' | 'PASSPORT' | 'DRIVING_LICENSE';
        createData.documentSide = documentSide as 'FRONT' | 'BACK';
      }

      kycDocument = await prisma.kYCDocument.create({
        data: createData,
      });
    }

    // Check if user has uploaded all required documents and update KYC status
    const userDocuments = await prisma.kYCDocument.findMany({
      where: { userId: user.id },
    });

    const hasSelfieDocument = userDocuments.some(doc => doc.documentType === 'SELFIE');

    // Check if user has complete ID documents based on their ID type
    let hasCompleteIdDocuments = false;
    const userIdType = userDocuments.find(doc => doc.documentType === 'ID_DOCUMENT')?.idType;

    if (userIdType) {
      const idDocuments = userDocuments.filter(doc =>
        doc.documentType === 'ID_DOCUMENT' && doc.idType === userIdType
      );

      if (userIdType === 'PASSPORT') {
        // Passport only needs front side
        hasCompleteIdDocuments = idDocuments.some(doc => doc.documentSide === 'FRONT');
      } else {
        // National ID and Driving License need both front and back
        const hasFront = idDocuments.some(doc => doc.documentSide === 'FRONT');
        const hasBack = idDocuments.some(doc => doc.documentSide === 'BACK');
        hasCompleteIdDocuments = hasFront && hasBack;
      }
    }

    if (hasCompleteIdDocuments && hasSelfieDocument) {
      // Update user KYC status to PENDING if all required documents are uploaded
      await prisma.user.update({
        where: { id: user.id },
        data: { kycStatus: 'PENDING' },
      });
    }

    // Log the upload
    await systemLogDb.create({
      action: 'KYC_DOCUMENT_UPLOADED',
      userId: user.id,
      details: {
        documentType,
        idType: idType || null,
        documentSide: documentSide || null,
        fileName,
        fileSize: file.size,
        documentId: kycDocument.id,
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: 'Document uploaded successfully',
      data: {
        documentId: kycDocument.id,
        documentType: kycDocument.documentType,
        status: kycDocument.status,
      },
    });

  } catch (error: any) {
    console.error('KYC document upload error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to upload document' },
      { status: 500 }
    );
  }
}
