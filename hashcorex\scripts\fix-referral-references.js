/**
 * <PERSON><PERSON><PERSON> to fix missing reference fields in existing referral commission transactions
 * This script will update existing DIRECT_REFERRAL transactions that don't have a reference field
 * by matching them with mining unit purchases based on timing and amount
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function fixReferralReferences() {
  console.log('Starting referral reference fix...');

  try {
    // Find all DIRECT_REFERRAL transactions without reference
    const referralTransactions = await prisma.transaction.findMany({
      where: {
        type: 'DIRECT_REFERRAL',
        OR: [
          { reference: null },
          { reference: '' },
          { reference: 'direct_referral' }
        ]
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`Found ${referralTransactions.length} referral transactions to fix`);

    let fixedCount = 0;

    for (const transaction of referralTransactions) {
      console.log(`Processing transaction ${transaction.id} for user ${transaction.user.email}`);

      // Find users who have this transaction recipient as their referrer
      const referredUsers = await prisma.user.findMany({
        where: {
          referrerId: transaction.userId,
          miningUnits: {
            some: {
              createdAt: {
                gte: new Date(new Date(transaction.createdAt).getTime() - 15 * 60 * 1000), // Within 15 minutes
                lte: new Date(new Date(transaction.createdAt).getTime() + 15 * 60 * 1000),
              },
            },
          },
        },
        include: {
          miningUnits: {
            where: {
              createdAt: {
                gte: new Date(new Date(transaction.createdAt).getTime() - 15 * 60 * 1000),
                lte: new Date(new Date(transaction.createdAt).getTime() + 15 * 60 * 1000),
              },
            },
            orderBy: { createdAt: 'desc' },
          },
        },
      });

      // Find the user whose purchase amount matches the commission (10% rule)
      let matchedUser = null;
      for (const user of referredUsers) {
        for (const miningUnit of user.miningUnits) {
          const expectedCommission = miningUnit.investmentAmount * 0.1;
          if (Math.abs(expectedCommission - transaction.amount) < 0.01) {
            matchedUser = user;
            break;
          }
        }
        if (matchedUser) break;
      }

      if (matchedUser) {
        // Update the transaction with the correct reference
        await prisma.transaction.update({
          where: { id: transaction.id },
          data: {
            reference: `from_user:${matchedUser.id}`
          }
        });

        console.log(`✅ Fixed transaction ${transaction.id} - matched with user ${matchedUser.email}`);
        fixedCount++;
      } else {
        console.log(`❌ Could not find matching user for transaction ${transaction.id}`);
      }
    }

    console.log(`\n✅ Fixed ${fixedCount} out of ${referralTransactions.length} transactions`);

  } catch (error) {
    console.error('Error fixing referral references:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  fixReferralReferences()
    .then(() => {
      console.log('Script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { fixReferralReferences };
