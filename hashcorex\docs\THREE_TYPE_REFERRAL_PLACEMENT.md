# Three-Type Referral Placement System

## Overview

The HashCoreX binary tree system now supports three distinct types of referral links, each with different placement behaviors:

1. **General Referral Link** (<PERSON><PERSON>ult) - Uses "weaker leg" placement algorithm
2. **Left Side Referral Link** - Forces placement exclusively on the LEFT side
3. **Right Side Referral Link** - Forces placement exclusively on the RIGHT side

## Referral Link Types

### 1. General Referral Link (<PERSON><PERSON>ult)
```
https://hashcorex.com/register?ref=REFERRAL_ID
```

**Behavior:**
- Uses the existing "weaker leg" algorithm
- Calculates total downline count for both LEFT and RIGHT sides
- Places new user in the side with fewer total members
- Maintains balanced tree growth

**Use Case:** Standard referral sharing for balanced team building

### 2. Left Side Referral Link
```
https://hashcorex.com/register?ref=REFERRAL_ID&side=left
```

**Behavior:**
- **STRICT LEFT PLACEMENT ONLY**
- Finds the deepest available position on the LEFT side of the sponsor's downline tree
- Never places on the right side, regardless of tree balance
- Traverses down the left side until finding the first available left position

**Use Case:** Strategic team building when you want to strengthen your left leg specifically

### 3. Right Side Referral Link
```
https://hashcorex.com/register?ref=REFERRAL_ID&side=right
```

**Behavior:**
- **STRICT RIGHT PLACEMENT ONLY**
- Finds the deepest available position on the RIGHT side of the sponsor's downline tree
- Never places on the left side, regardless of tree balance
- Traverses down the right side until finding the first available right position

**Use Case:** Strategic team building when you want to strengthen your right leg specifically

## Visual Examples

### Example Tree Structure
```
        Sponsor A
       /         \
   User B         User C
   /    \         /    \
User D  User E  User F  [Empty]
```

### Placement Scenarios

#### Scenario 1: General Link Placement
- **Current State:** Left side has 3 users (B, D, E), Right side has 1 user (C)
- **New User via General Link:** Places under User C (RIGHT side - weaker leg)
- **Result:** Balances the tree

#### Scenario 2: Left Side Link Placement
- **Current State:** Same as above
- **New User via Left Link:** Places under User D or User E (deepest LEFT position)
- **Result:** Continues building left side regardless of balance

#### Scenario 3: Right Side Link Placement
- **Current State:** Same as above
- **New User via Right Link:** Places under User C (RIGHT side)
- **Result:** Builds right side as requested

## Implementation Details

### Core Functions

#### `placeUserByReferralType(referrerId, newUserId, referralType)`
Main routing function that determines which placement algorithm to use:
- `referralType: 'general'` → Uses `placeUserInBinaryTree()`
- `referralType: 'left'` → Uses `placeUserInLeftSideOnly()`
- `referralType: 'right'` → Uses `placeUserInRightSideOnly()`

#### `placeUserInLeftSideOnly(referrerId, newUserId)`
- Calls `findDeepestLeftPosition()` to locate optimal placement
- Creates referral relationship with LEFT placement side
- Updates parent's `leftReferralId`
- Creates sponsor relationship (separate from binary placement)

#### `placeUserInRightSideOnly(referrerId, newUserId)`
- Calls `findDeepestRightPosition()` to locate optimal placement
- Creates referral relationship with RIGHT placement side
- Updates parent's `rightReferralId`
- Creates sponsor relationship (separate from binary placement)

### Placement Algorithms

#### Left Side Algorithm (`findDeepestLeftPosition`)
1. Start from the referrer
2. Check if LEFT spot is available
3. If available, place there
4. If occupied, move to LEFT child and repeat
5. Continue until finding available LEFT position
6. Maximum depth limit prevents infinite loops

#### Right Side Algorithm (`findDeepestRightPosition`)
1. Start from the referrer
2. Check if RIGHT spot is available
3. If available, place there
4. If occupied, move to RIGHT child and repeat
5. Continue until finding available RIGHT position
6. Maximum depth limit prevents infinite loops

## Registration Flow Integration

### URL Parameter Processing
The registration system automatically detects the `side` parameter:
```javascript
// Extract side parameter from URL
const side = url.searchParams.get('side') as 'left' | 'right' | null;

// Determine referral type
let referralType: 'general' | 'left' | 'right' = 'general';
if (side === 'left') referralType = 'left';
else if (side === 'right') referralType = 'right';

// Place user using unified function
await placeUserByReferralType(referrerId, user.id, referralType);
```

### Backward Compatibility
- Existing general referral links continue to work unchanged
- Legacy `placeUserInSpecificSide()` function maintained for compatibility
- No breaking changes to existing functionality

## Benefits

### For Users
1. **Strategic Control:** Choose exactly where to place new referrals
2. **Team Building:** Build specific legs according to strategy
3. **Flexibility:** Use different link types for different purposes

### For System
1. **Clear Separation:** Each placement type has distinct, predictable behavior
2. **Maintainability:** Clean code structure with separate functions
3. **Testability:** Each placement type can be tested independently

## Usage Guidelines

### When to Use Each Type

#### General Links
- Default sharing with friends and family
- When you want balanced team growth
- For users who don't have specific placement strategies

#### Left Side Links
- When your left leg needs strengthening
- For strategic team building campaigns
- When you want to concentrate growth on one side

#### Right Side Links
- When your right leg needs strengthening
- For balancing an existing heavy left side
- For specific promotional campaigns

### Best Practices

1. **Monitor Tree Balance:** Use analytics to understand which side needs growth
2. **Strategic Sharing:** Use different link types for different audiences
3. **Documentation:** Keep track of which links you share where
4. **Team Education:** Teach your team about the different link types

## Technical Notes

### Database Schema
- No changes required to existing database schema
- Uses existing `placementSide` field in referrals table
- Maintains all existing relationships and constraints

### Performance
- Minimal performance impact
- Efficient tree traversal algorithms
- Proper depth limits prevent infinite loops
- Cached downline counts for optimization

### Error Handling
- Robust fallback mechanisms
- User existence validation
- Foreign key constraint handling
- Graceful degradation on errors

## Testing

The system includes comprehensive tests covering:
- All three placement types
- Edge cases and error conditions
- Deep tree structures
- Placement verification
- Backward compatibility

Key test scenarios verify that:
- General links use weaker leg logic
- Left links never place on right side
- Right links never place on left side
- Deep placement works correctly
- Error conditions are handled gracefully
