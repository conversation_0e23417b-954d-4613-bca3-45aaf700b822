import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET - Fetch user's KYC documents
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get user's KYC documents
    const documents = await prisma.kYCDocument.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        documentType: true,
        filePath: true,
        status: true,
        reviewedAt: true,
        rejectionReason: true,
        createdAt: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: documents,
    });

  } catch (error: any) {
    console.error('KYC documents fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch KYC documents' },
      { status: 500 }
    );
  }
}
