# HashCoreX Testing Guide

This guide covers testing procedures for the HashCoreX platform.

## Testing Environment Setup

### 1. Install Testing Dependencies

```bash
npm install --save-dev jest @testing-library/react @testing-library/jest-dom
npm install --save-dev cypress @cypress/react
npm install --save-dev supertest
```

### 2. Configure Test Environment

Create `.env.test`:
```env
NODE_ENV=test
DATABASE_URL="postgresql://test_user:test_pass@localhost:5432/hashcorex_test"
JWT_SECRET="test-jwt-secret"
ENCRYPTION_KEY="test-encryption-key-32-characters"
```

### 3. Setup Test Database

```bash
# Create test database
createdb hashcorex_test

# Run migrations
DATABASE_URL="postgresql://test_user:test_pass@localhost:5432/hashcorex_test" npx prisma db push
```

## Testing Checklist

### Core Functionality Tests

#### 1. User Authentication System
- [ ] User registration with email validation
- [ ] User login with correct credentials
- [ ] User login with incorrect credentials
- [ ] Password reset functionality
- [ ] JWT token generation and validation
- [ ] Session management
- [ ] Logout functionality

#### 2. KYC Verification System
- [ ] Document upload (ID and Selfie)
- [ ] File validation (type, size, format)
- [ ] KYC status updates
- [ ] Admin KYC review process
- [ ] Document approval/rejection
- [ ] User notification on status change

#### 3. Mining Unit System
- [ ] Mining unit purchase with valid amount
- [ ] Mining unit purchase with insufficient funds
- [ ] TH/s calculation accuracy
- [ ] Investment amount validation
- [ ] Mining unit expiry (12 months)
- [ ] Mining unit expiry (5x investment)
- [ ] Daily ROI calculation
- [ ] Mining unit status updates

#### 4. ROI & Earnings Engine
- [ ] Daily ROI calculation accuracy
- [ ] ROI range validation (0.6% - 1.1%)
- [ ] Weekly earnings distribution
- [ ] Earnings accumulation
- [ ] Payout timing (Saturday 15:00 UTC)
- [ ] Earnings transaction creation
- [ ] Total earnings calculation

#### 5. Internal Wallet System
- [ ] Wallet balance calculation
- [ ] Transaction history tracking
- [ ] Withdrawal request creation
- [ ] Withdrawal validation (minimum amount)
- [ ] USDT address validation
- [ ] Withdrawal status updates
- [ ] Balance updates after transactions

#### 6. Binary Referral System
- [ ] Referral link generation
- [ ] User placement in binary tree
- [ ] Left/right side assignment
- [ ] Direct referral bonus (10%)
- [ ] Binary points accumulation
- [ ] Binary matching calculation
- [ ] Binary pool distribution
- [ ] Maximum points per side (2000)
- [ ] Daily binary matching (12:00 AM UTC)

#### 7. Admin Panel
- [ ] Admin authentication
- [ ] User management (view, edit, disable)
- [ ] KYC review and approval
- [ ] Withdrawal processing
- [ ] System settings management
- [ ] Platform statistics
- [ ] System logs viewing

### API Endpoint Tests

#### Authentication Endpoints
```bash
# Test user registration
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPass123!","confirmPassword":"TestPass123!"}'

# Test user login
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPass123!"}'
```

#### Mining Unit Endpoints
```bash
# Test mining unit purchase
curl -X POST http://localhost:3000/api/mining-units \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"thsAmount":1,"investmentAmount":50}'

# Test mining units fetch
curl -X GET http://localhost:3000/api/mining-units \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### Wallet Endpoints
```bash
# Test wallet balance
curl -X GET http://localhost:3000/api/wallet/balance \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test withdrawal request
curl -X POST http://localhost:3000/api/wallet/withdraw \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"amount":100,"usdtAddress":"TTestAddress123456789"}'
```

### Security Tests

#### 1. Authentication Security
- [ ] SQL injection prevention
- [ ] XSS attack prevention
- [ ] CSRF protection
- [ ] Rate limiting on login attempts
- [ ] Password strength validation
- [ ] JWT token expiration
- [ ] Secure cookie settings

#### 2. Authorization Tests
- [ ] User can only access own data
- [ ] Admin endpoints require admin role
- [ ] Protected routes redirect to login
- [ ] API endpoints validate authentication
- [ ] File upload security
- [ ] Input validation and sanitization

#### 3. Data Protection
- [ ] Sensitive data encryption
- [ ] Password hashing (bcrypt)
- [ ] Database connection security
- [ ] Environment variable protection
- [ ] File upload restrictions
- [ ] CORS configuration

### Performance Tests

#### 1. Load Testing
```bash
# Install artillery for load testing
npm install -g artillery

# Run load test
artillery run load-test.yml
```

#### 2. Database Performance
- [ ] Query optimization
- [ ] Index effectiveness
- [ ] Connection pooling
- [ ] Transaction performance
- [ ] Large dataset handling

#### 3. API Response Times
- [ ] Authentication endpoints < 200ms
- [ ] Data retrieval endpoints < 500ms
- [ ] File upload endpoints < 2s
- [ ] Complex calculations < 1s

### Integration Tests

#### 1. Cron Job Testing
```bash
# Test daily ROI calculation
curl -X POST http://localhost:3000/api/cron/daily-roi \
  -H "Authorization: Bearer YOUR_CRON_SECRET"

# Test weekly payout
curl -X POST http://localhost:3000/api/cron/weekly-payout \
  -H "Authorization: Bearer YOUR_CRON_SECRET"

# Test binary matching
curl -X POST http://localhost:3000/api/cron/binary-matching \
  -H "Authorization: Bearer YOUR_CRON_SECRET"
```

#### 2. Email Integration
- [ ] Registration confirmation emails
- [ ] Password reset emails
- [ ] KYC status notification emails
- [ ] Withdrawal confirmation emails

#### 3. File Upload Integration
- [ ] KYC document upload
- [ ] File storage and retrieval
- [ ] Image processing and validation
- [ ] File cleanup and management

### User Interface Tests

#### 1. Responsive Design
- [ ] Mobile device compatibility
- [ ] Tablet device compatibility
- [ ] Desktop compatibility
- [ ] Cross-browser testing (Chrome, Firefox, Safari)

#### 2. User Experience
- [ ] Navigation functionality
- [ ] Form validation and feedback
- [ ] Loading states and indicators
- [ ] Error handling and messages
- [ ] Success notifications

#### 3. Accessibility
- [ ] Keyboard navigation
- [ ] Screen reader compatibility
- [ ] Color contrast compliance
- [ ] Alt text for images

### Automated Testing

#### 1. Unit Tests
```bash
# Run unit tests
npm test

# Run with coverage
npm run test:coverage
```

#### 2. Integration Tests
```bash
# Run integration tests
npm run test:integration
```

#### 3. End-to-End Tests
```bash
# Run Cypress tests
npm run cypress:run

# Open Cypress GUI
npm run cypress:open
```

### Manual Testing Scenarios

#### 1. Complete User Journey
1. User registration and email verification
2. KYC document upload and approval
3. Mining unit purchase
4. Earnings accumulation over time
5. Referral system usage
6. Withdrawal request and processing

#### 2. Admin Workflow
1. Admin login and dashboard access
2. User management operations
3. KYC review and approval process
4. Withdrawal processing
5. System settings configuration
6. Platform monitoring and logs

#### 3. Error Scenarios
1. Invalid input handling
2. Network failure recovery
3. Database connection issues
4. File upload failures
5. Payment processing errors

### Test Data Management

#### 1. Test User Accounts
```sql
-- Create test users
INSERT INTO users (email, password, referral_id, kyc_status) VALUES
('<EMAIL>', 'hashed_password', 'TEST001', 'APPROVED'),
('<EMAIL>', 'hashed_password', 'TEST002', 'PENDING'),
('<EMAIL>', 'hashed_password', 'ADMIN01', 'APPROVED');
```

#### 2. Test Mining Units
```sql
-- Create test mining units
INSERT INTO mining_units (user_id, ths_amount, investment_amount, daily_roi, status) VALUES
('user_id_1', 1.0, 50.0, 0.8, 'ACTIVE'),
('user_id_2', 2.0, 100.0, 0.9, 'ACTIVE');
```

### Continuous Integration

#### 1. GitHub Actions
```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: npm test
      - run: npm run test:integration
```

### Test Reporting

#### 1. Coverage Reports
- Aim for >80% code coverage
- Focus on critical business logic
- Monitor coverage trends

#### 2. Performance Metrics
- Track API response times
- Monitor database query performance
- Measure user interaction times

#### 3. Security Audit
- Regular dependency vulnerability scans
- Penetration testing
- Security code reviews

## Test Environment Maintenance

### 1. Database Cleanup
```bash
# Reset test database
npm run db:reset:test
```

### 2. Test Data Refresh
```bash
# Seed fresh test data
npm run seed:test
```

### 3. Log Management
```bash
# Clear test logs
npm run logs:clear:test
```

Remember to run the complete test suite before any production deployment!
