{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(amount);\n}\n\nexport function formatNumber(num: number, decimals = 2): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(num);\n}\n\nexport function formatDate(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d);\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function copyToClipboard(text: string): Promise<void> {\n  if (navigator.clipboard) {\n    return navigator.clipboard.writeText(text);\n  }\n  \n  // Fallback for older browsers\n  const textArea = document.createElement('textarea');\n  textArea.value = text;\n  document.body.appendChild(textArea);\n  textArea.focus();\n  textArea.select();\n  \n  try {\n    document.execCommand('copy');\n    return Promise.resolve();\n  } catch (err) {\n    return Promise.reject(err);\n  } finally {\n    document.body.removeChild(textArea);\n  }\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n    errors.push('Password must contain at least one special character');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n}\n\nexport function calculateROI(\n  investment: number,\n  dailyRate: number,\n  days: number\n): number {\n  return investment * (dailyRate / 100) * days;\n}\n\nexport function calculateTHSPrice(ths: number, pricePerTHS: number): number {\n  return ths * pricePerTHS;\n}\n\nexport function formatTHS(ths: number): string {\n  if (ths >= 1000) {\n    return `${(ths / 1000).toFixed(1)}K TH/s`;\n  }\n  return `${ths.toFixed(2)} TH/s`;\n}\n\nexport function getTimeUntilNextPayout(): {\n  days: number;\n  hours: number;\n  minutes: number;\n  seconds: number;\n} {\n  const now = new Date();\n  const nextSaturday = new Date();\n  \n  // Set to next Saturday at 15:00 UTC\n  nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));\n  nextSaturday.setUTCHours(15, 0, 0, 0);\n  \n  // If it's already past Saturday 15:00, move to next week\n  if (now > nextSaturday) {\n    nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);\n  }\n  \n  const diff = nextSaturday.getTime() - now.getTime();\n  \n  const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n  \n  return { days, hours, minutes, seconds };\n}\n\nexport function getTimeUntilBinaryPayout(): {\n  days: number;\n  hours: number;\n  minutes: number;\n  seconds: number;\n} {\n  const now = new Date();\n  const nextSaturday = new Date();\n\n  // Set to next Saturday at 15:00 UTC\n  nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));\n  nextSaturday.setUTCHours(15, 0, 0, 0);\n\n  // If it's already past Saturday 15:00, move to next week\n  if (now > nextSaturday) {\n    nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);\n  }\n\n  const diff = nextSaturday.getTime() - now.getTime();\n\n  const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n\n  return { days, hours, minutes, seconds };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW,EAAE,WAAW,CAAC;IACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,gBAAgB,IAAY;IAC1C,IAAI,UAAU,SAAS,EAAE;QACvB,OAAO,UAAU,SAAS,CAAC,SAAS,CAAC;IACvC;IAEA,8BAA8B;IAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;IACxC,SAAS,KAAK,GAAG;IACjB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,SAAS,KAAK;IACd,SAAS,MAAM;IAEf,IAAI;QACF,SAAS,WAAW,CAAC;QACrB,OAAO,QAAQ,OAAO;IACxB,EAAE,OAAO,KAAK;QACZ,OAAO,QAAQ,MAAM,CAAC;IACxB,SAAU;QACR,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,yBAAyB,IAAI,CAAC,WAAW;QAC5C,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,aACd,UAAkB,EAClB,SAAiB,EACjB,IAAY;IAEZ,OAAO,aAAa,CAAC,YAAY,GAAG,IAAI;AAC1C;AAEO,SAAS,kBAAkB,GAAW,EAAE,WAAmB;IAChE,OAAO,MAAM;AACf;AAEO,SAAS,UAAU,GAAW;IACnC,IAAI,OAAO,MAAM;QACf,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC;IAC3C;IACA,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,KAAK,CAAC;AACjC;AAEO,SAAS;IAMd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI;IAEzB,oCAAoC;IACpC,aAAa,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,IAAI,IAAI,SAAS,EAAE;IAC/D,aAAa,WAAW,CAAC,IAAI,GAAG,GAAG;IAEnC,yDAAyD;IACzD,IAAI,MAAM,cAAc;QACtB,aAAa,UAAU,CAAC,aAAa,UAAU,KAAK;IACtD;IAEA,MAAM,OAAO,aAAa,OAAO,KAAK,IAAI,OAAO;IAEjD,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;IACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,IAAK,CAAC,OAAO,KAAK,EAAE;IACzE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,OAAO;QAAE;QAAM;QAAO;QAAS;IAAQ;AACzC;AAEO,SAAS;IAMd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI;IAEzB,oCAAoC;IACpC,aAAa,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,IAAI,IAAI,SAAS,EAAE;IAC/D,aAAa,WAAW,CAAC,IAAI,GAAG,GAAG;IAEnC,yDAAyD;IACzD,IAAI,MAAM,cAAc;QACtB,aAAa,UAAU,CAAC,aAAa,UAAU,KAAK;IACtD;IAEA,MAAM,OAAO,aAAa,OAAO,KAAK,IAAI,OAAO;IAEjD,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;IACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,IAAK,CAAC,OAAO,KAAK,EAAE;IACzE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,OAAO;QAAE;QAAM;QAAO;QAAS;IAAQ;AACzC", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-xl font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-lg',\n  {\n    variants: {\n      variant: {\n        primary: 'bg-yellow-500 text-white focus:ring-yellow-500',\n        secondary: 'bg-gray-200 text-gray-800 focus:ring-gray-500',\n        success: 'bg-emerald-500 text-white focus:ring-emerald-500',\n        danger: 'bg-red-500 text-white focus:ring-red-500',\n        warning: 'bg-yellow-500 text-white focus:ring-yellow-500',\n        destructive: 'bg-red-600 text-white focus:ring-red-500',\n        outline: 'border-2 border-yellow-500 bg-transparent text-yellow-600 focus:ring-yellow-500',\n        ghost: 'text-gray-600 focus:ring-yellow-500 rounded-lg',\n        link: 'text-yellow-600 underline-offset-4 focus:ring-yellow-500',\n        premium: 'bg-slate-800 text-white focus:ring-slate-500',\n        glass: 'glass-morphism text-slate-900 backdrop-blur-xl border border-white/20',\n      },\n      size: {\n        sm: 'h-10 px-4 text-sm rounded-lg',\n        md: 'h-12 px-6 text-base rounded-xl',\n        lg: 'h-14 px-8 text-lg rounded-xl',\n        xl: 'h-16 px-10 text-xl rounded-2xl font-bold',\n        icon: 'h-12 w-12 rounded-xl',\n      },\n    },\n    defaultVariants: {\n      variant: 'primary',\n      size: 'md',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, leftIcon, rightIcon, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <div className=\"mr-2\">\n            <div className=\"spinner\" />\n          </div>\n        )}\n        {leftIcon && !loading && <span className=\"mr-2\">{leftIcon}</span>}\n        {children}\n        {rightIcon && !loading && <span className=\"ml-2\">{rightIcon}</span>}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,mLACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,QAAQ;YACR,SAAS;YACT,aAAa;YACb,SAAS;YACT,OAAO;YACP,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAWF,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;YAGlB,YAAY,CAAC,yBAAW,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChD;YACA,aAAa,CAAC,yBAAW,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGxD;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          'rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl overflow-hidden',\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCard.displayName = 'Card';\n\nexport interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('flex flex-col space-y-1.5 p-6 pb-4', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardHeader.displayName = 'CardHeader';\n\nexport interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {\n  children: React.ReactNode;\n}\n\nconst CardTitle = React.forwardRef<HTMLParagraphElement, CardTitleProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <h3\n        ref={ref}\n        className={cn('text-xl font-semibold leading-none tracking-tight text-dark-900', className)}\n        {...props}\n      >\n        {children}\n      </h3>\n    );\n  }\n);\n\nCardTitle.displayName = 'CardTitle';\n\nexport interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {\n  children: React.ReactNode;\n}\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <p\n        ref={ref}\n        className={cn('text-sm text-gray-500', className)}\n        {...props}\n      >\n        {children}\n      </p>\n    );\n  }\n);\n\nCardDescription.displayName = 'CardDescription';\n\nexport interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('p-6 pt-0', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardContent.displayName = 'CardContent';\n\nexport interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('flex items-center p-6 pt-0', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AASA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,KAAK,WAAW,GAAG;AAMnB,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,WAAW,WAAW,GAAG;AAMzB,MAAM,0BAAY,qMAAA,CAAA,UAAK,CAAC,UAAU,CAChC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,UAAU,WAAW,GAAG;AAMxB,MAAM,gCAAkB,qMAAA,CAAA,UAAK,CAAC,UAAU,CACtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,gBAAgB,WAAW,GAAG;AAM9B,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,YAAY,WAAW,GAAG;AAM1B,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Input.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, leftIcon, rightIcon, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-400\">{leftIcon}</span>\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              'flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300',\n              leftIcon && 'pl-12',\n              rightIcon && 'pr-12',\n              error && 'border-red-500 focus:ring-red-500 focus:border-red-500',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n              <span className=\"text-gray-400\">{rightIcon}</span>\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,sBAAQ,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACjE,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;oBACZ,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;kCAGrC,8OAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8UACA,YAAY,SACZ,aAAa,SACb,SAAS,0DACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;;;;;;;YAItC,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Modal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport { X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from './Button';\n\nexport interface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  showCloseButton?: boolean;\n  darkMode?: boolean;\n}\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md',\n  showCloseButton = true,\n  darkMode = false,\n}) => {\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl',\n  };\n\n  const modalContent = (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n      {/* Backdrop */}\n      <div\n        className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\"\n        onClick={onClose}\n      />\n      \n      {/* Modal */}\n      <div\n        className={cn(\n          'relative w-full rounded-xl shadow-xl transform transition-all',\n          darkMode ? 'bg-slate-800' : 'bg-white',\n          sizeClasses[size]\n        )}\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Header */}\n        <div className={cn(\n          'flex items-center justify-between p-6 border-b',\n          darkMode ? 'border-slate-700' : 'border-gray-200'\n        )}>\n          <h2 className={cn(\n            'text-xl font-semibold',\n            darkMode ? 'text-white' : 'text-dark-900'\n          )}>{title}</h2>\n          {showCloseButton && (\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onClose}\n              className=\"h-8 w-8 rounded-full\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n        \n        {/* Content */}\n        <div className=\"p-6\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n\n  return createPortal(modalContent, document.body);\n};\n\nexport { Modal };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAkBA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACtB,WAAW,KAAK,EACjB;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,UAAU;gBAC1B;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,WAAW;QACvC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,6BACJ,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA,WAAW,iBAAiB,YAC5B,WAAW,CAAC,KAAK;gBAEnB,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,kDACA,WAAW,qBAAqB;;0CAEhC,8OAAC;gCAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACd,yBACA,WAAW,eAAe;0CACxB;;;;;;4BACH,iCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAMnB,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;IAMT,qBAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,cAAc,SAAS,IAAI;AACjD", "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Loading.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  text?: string;\n}\n\nconst Loading: React.FC<LoadingProps> = ({ size = 'md', className, text }) => {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n  };\n\n  return (\n    <div className={cn('flex flex-col items-center justify-center', className)}>\n      <div\n        className={cn(\n          'animate-spin rounded-full border-2 border-gray-300 border-t-solar-500',\n          sizeClasses[size]\n        )}\n      />\n      {text && (\n        <p className=\"mt-2 text-sm text-gray-600\">{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport interface LoadingOverlayProps {\n  isLoading: boolean;\n  text?: string;\n  children: React.ReactNode;\n}\n\nconst LoadingOverlay: React.FC<LoadingOverlayProps> = ({\n  isLoading,\n  text = 'Loading...',\n  children,\n}) => {\n  return (\n    <div className=\"relative\">\n      {children}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10\">\n          <Loading text={text} />\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport { Loading, LoadingOverlay };\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAWA,MAAM,UAAkC,CAAC,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE;IACvE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;0BAC9D,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yEACA,WAAW,CAAC,KAAK;;;;;;YAGpB,sBACC,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;AAQA,MAAM,iBAAgD,CAAC,EACrD,SAAS,EACT,OAAO,YAAY,EACnB,QAAQ,EACT;IACC,qBACE,8OAAC;QAAI,WAAU;;YACZ;YACA,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAQ,MAAM;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/ConfirmDialog.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { createPortal } from 'react-dom';\nimport { AlertTriangle, CheckCircle, Info, X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from './Button';\n\nexport interface ConfirmDialogProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onConfirm: () => void;\n  title: string;\n  message: string;\n  confirmText?: string;\n  cancelText?: string;\n  variant?: 'default' | 'danger' | 'warning' | 'success';\n  darkMode?: boolean;\n  loading?: boolean;\n}\n\nexport const ConfirmDialog: React.FC<ConfirmDialogProps> = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  message,\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  variant = 'default',\n  darkMode = false,\n  loading = false,\n}) => {\n  if (!isOpen) return null;\n\n  const getIcon = () => {\n    switch (variant) {\n      case 'danger':\n        return <AlertTriangle className=\"h-6 w-6 text-red-500\" />;\n      case 'warning':\n        return <AlertTriangle className=\"h-6 w-6 text-yellow-500\" />;\n      case 'success':\n        return <CheckCircle className=\"h-6 w-6 text-green-500\" />;\n      default:\n        return <Info className=\"h-6 w-6 text-blue-500\" />;\n    }\n  };\n\n  const getConfirmButtonVariant = () => {\n    switch (variant) {\n      case 'danger':\n        return 'destructive';\n      case 'warning':\n        return 'warning';\n      case 'success':\n        return 'default';\n      default:\n        return 'default';\n    }\n  };\n\n  const modalContent = (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n      {/* Backdrop */}\n      <div \n        className=\"absolute inset-0 bg-black bg-opacity-50 transition-opacity\"\n        onClick={onClose}\n      />\n      \n      {/* Dialog */}\n      <div\n        className={cn(\n          'relative w-full max-w-md rounded-xl shadow-xl transform transition-all',\n          darkMode ? 'bg-slate-800 border border-slate-700' : 'bg-white'\n        )}\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Header */}\n        <div className={cn(\n          'flex items-center justify-between p-6 border-b',\n          darkMode ? 'border-slate-700' : 'border-gray-200'\n        )}>\n          <div className=\"flex items-center space-x-3\">\n            {getIcon()}\n            <h2 className={cn(\n              'text-lg font-semibold',\n              darkMode ? 'text-white' : 'text-gray-900'\n            )}>\n              {title}\n            </h2>\n          </div>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={onClose}\n            disabled={loading}\n            className=\"h-8 w-8 rounded-full\"\n          >\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </div>\n        \n        {/* Content */}\n        <div className=\"p-6\">\n          <p className={cn(\n            'text-sm leading-relaxed',\n            darkMode ? 'text-slate-300' : 'text-gray-600'\n          )}>\n            {message}\n          </p>\n        </div>\n\n        {/* Actions */}\n        <div className={cn(\n          'flex items-center justify-end space-x-3 p-6 border-t',\n          darkMode ? 'border-slate-700' : 'border-gray-200'\n        )}>\n          <Button\n            variant=\"outline\"\n            onClick={onClose}\n            disabled={loading}\n            className={cn(\n              darkMode ? 'border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white' : ''\n            )}\n          >\n            {cancelText}\n          </Button>\n          <Button\n            variant={getConfirmButtonVariant()}\n            onClick={onConfirm}\n            disabled={loading}\n            className={cn(\n              loading && 'opacity-50 cursor-not-allowed'\n            )}\n          >\n            {loading ? 'Processing...' : confirmText}\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n\n  return createPortal(modalContent, document.body);\n};\n\n// Hook for easier usage\nexport const useConfirmDialog = () => {\n  const [dialogState, setDialogState] = React.useState<{\n    isOpen: boolean;\n    title: string;\n    message: string;\n    onConfirm: () => void;\n    variant?: 'default' | 'danger' | 'warning' | 'success';\n    confirmText?: string;\n    cancelText?: string;\n    darkMode?: boolean;\n  }>({\n    isOpen: false,\n    title: '',\n    message: '',\n    onConfirm: () => {},\n  });\n\n  const [loading, setLoading] = React.useState(false);\n\n  const showConfirm = (options: {\n    title: string;\n    message: string;\n    onConfirm: () => void | Promise<void>;\n    variant?: 'default' | 'danger' | 'warning' | 'success';\n    confirmText?: string;\n    cancelText?: string;\n    darkMode?: boolean;\n  }) => {\n    setDialogState({\n      isOpen: true,\n      ...options,\n      onConfirm: async () => {\n        setLoading(true);\n        try {\n          await options.onConfirm();\n          setDialogState(prev => ({ ...prev, isOpen: false }));\n        } catch (error) {\n          console.error('Confirm action failed:', error);\n        } finally {\n          setLoading(false);\n        }\n      },\n    });\n  };\n\n  const hideConfirm = () => {\n    if (!loading) {\n      setDialogState(prev => ({ ...prev, isOpen: false }));\n    }\n  };\n\n  const ConfirmDialogComponent = () => (\n    <ConfirmDialog\n      isOpen={dialogState.isOpen}\n      onClose={hideConfirm}\n      onConfirm={dialogState.onConfirm}\n      title={dialogState.title}\n      message={dialogState.message}\n      variant={dialogState.variant}\n      confirmText={dialogState.confirmText}\n      cancelText={dialogState.cancelText}\n      darkMode={dialogState.darkMode}\n      loading={loading}\n    />\n  );\n\n  return {\n    showConfirm,\n    hideConfirm,\n    ConfirmDialog: ConfirmDialogComponent,\n    loading,\n  };\n};\n\nexport { ConfirmDialog as default };\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAqBO,MAAM,gBAA8C,CAAC,EAC1D,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,OAAO,EACP,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,UAAU,SAAS,EACnB,WAAW,KAAK,EAChB,UAAU,KAAK,EAChB;IACC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,0BAA0B;QAC9B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,6BACJ,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA,WAAW,yCAAyC;gBAEtD,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,kDACA,WAAW,qBAAqB;;0CAEhC,8OAAC;gCAAI,WAAU;;oCACZ;kDACD,8OAAC;wCAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACd,yBACA,WAAW,eAAe;kDAEzB;;;;;;;;;;;;0CAGL,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,2BACA,WAAW,mBAAmB;sCAE7B;;;;;;;;;;;kCAKL,8OAAC;wBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,wDACA,WAAW,qBAAqB;;0CAEhC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WAAW,wEAAwE;0CAGpF;;;;;;0CAEH,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAS;gCACT,UAAU;gCACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WAAW;0CAGZ,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;IAOvC,qBAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,cAAc,SAAS,IAAI;AACjD;AAGO,MAAM,mBAAmB;IAC9B,MAAM,CAAC,aAAa,eAAe,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CASjD;QACD,QAAQ;QACR,OAAO;QACP,SAAS;QACT,WAAW,KAAO;IACpB;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE7C,MAAM,cAAc,CAAC;QASnB,eAAe;YACb,QAAQ;YACR,GAAG,OAAO;YACV,WAAW;gBACT,WAAW;gBACX,IAAI;oBACF,MAAM,QAAQ,SAAS;oBACvB,eAAe,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;wBAAM,CAAC;gBACpD,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0BAA0B;gBAC1C,SAAU;oBACR,WAAW;gBACb;YACF;QACF;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;YACZ,eAAe,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAM,CAAC;QACpD;IACF;IAEA,MAAM,yBAAyB,kBAC7B,8OAAC;YACC,QAAQ,YAAY,MAAM;YAC1B,SAAS;YACT,WAAW,YAAY,SAAS;YAChC,OAAO,YAAY,KAAK;YACxB,SAAS,YAAY,OAAO;YAC5B,SAAS,YAAY,OAAO;YAC5B,aAAa,YAAY,WAAW;YACpC,YAAY,YAAY,UAAU;YAClC,UAAU,YAAY,QAAQ;YAC9B,SAAS;;;;;;IAIb,OAAO;QACL;QACA;QACA,eAAe;QACf;IACF;AACF", "debugId": null}}, {"offset": {"line": 976, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/index.ts"], "sourcesContent": ["export { Button, buttonVariants, type ButtonProps } from './Button';\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from './Card';\nexport { Input, type InputProps } from './Input';\nexport { Modal, type ModalProps } from './Modal';\nexport { Loading, LoadingOverlay, type LoadingProps } from './Loading';\nexport { ConfirmDialog, useConfirmDialog, type ConfirmDialogProps } from './ConfirmDialog';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/SolarPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const SolarPanel: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"2\" y1=\"8\" x2=\"22\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"2\" y1=\"16\" x2=\"22\" y2=\"16\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"6\" y1=\"4\" x2=\"6\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"10\" y1=\"4\" x2=\"10\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"4\" x2=\"14\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"18\" y1=\"4\" x2=\"18\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <circle cx=\"20\" cy=\"2\" r=\"1\" fill=\"currentColor\"/>\n      <path d=\"M19 1l1 1-1 1-1-1z\" fill=\"currentColor\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM,aAAkC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACtE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACrE,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACrE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;gBAAI,MAAK;;;;;;0BAClC,8OAAC;gBAAK,GAAE;gBAAqB,MAAK;;;;;;;;;;;;AAGxC", "debugId": null}}, {"offset": {"line": 1153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/MiningRig.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const MiningRig: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <rect x=\"2\" y=\"6\" width=\"20\" height=\"12\" rx=\"2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <rect x=\"4\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"8\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"12\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"16\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"4\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"8\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"12\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"16\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <circle cx=\"20\" cy=\"4\" r=\"1\" fill=\"currentColor\"/>\n      <line x1=\"20\" y1=\"4\" x2=\"20\" y2=\"6\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"18\" y1=\"2\" x2=\"22\" y2=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"19\" y1=\"1\" x2=\"21\" y2=\"3\" stroke=\"currentColor\" strokeWidth=\"1\"/>\n      <line x1=\"21\" y1=\"1\" x2=\"19\" y2=\"3\" stroke=\"currentColor\" strokeWidth=\"1\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM,YAAiC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACrE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACrD,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACrD,8OAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,8OAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,8OAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACvD,8OAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACvD,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;gBAAI,MAAK;;;;;;0BAClC,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG5E", "debugId": null}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/Cryptocurrency.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const Cryptocurrency: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 12h8M12 8v8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <path d=\"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"12\" y1=\"6\" x2=\"12\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n\nexport const Bitcoin: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 12h4c1.1 0 2-.9 2-2s-.9-2-2-2H8v8h4c1.1 0 2-.9 2-2s-.9-2-2-2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"10\" y1=\"6\" x2=\"10\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"10\" y1=\"16\" x2=\"10\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"6\" x2=\"14\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"16\" x2=\"14\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AASO,MAAM,iBAAsC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IAC1E,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1E,8OAAC;gBAAK,GAAE;gBAAiB,QAAO;gBAAe,aAAY;;;;;;0BAC3D,8OAAC;gBAAK,GAAE;gBAAsC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACzF,8OAAC;gBAAK,GAAE;gBAAuC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1F,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E;AAEO,MAAM,UAA+B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACnE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1E,8OAAC;gBAAK,GAAE;gBAAkE,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACrH,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACxE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E", "debugId": null}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/EcoFriendly.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const Leaf: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <path d=\"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z\" fill=\"currentColor\"/>\n    </svg>\n  );\n};\n\nexport const Recycle: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <path d=\"M7 19H4.815a1.83 1.83 0 01-1.57-.881 1.785 1.785 0 01-.004-1.784L7.196 9.5\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M11 19h8.203a1.83 1.83 0 001.556-.89 1.784 1.784 0 000-1.775l-1.226-2.12\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M14 16l-3 3 3 3\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8.293 13.596L7.196 9.5l3.1 1.598\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M9.344 5.811L11.271 2a1.784 1.784 0 011.57-.881c.65 0 1.235.361 1.556.881l3.68 6.361\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M16 8l3-3-3-3\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n    </svg>\n  );\n};\n\nexport const WindTurbine: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <line x1=\"12\" y1=\"12\" x2=\"12\" y2=\"22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <path d=\"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <circle cx=\"12\" cy=\"12\" r=\"1\" fill=\"currentColor\"/>\n      <line x1=\"10\" y1=\"22\" x2=\"14\" y2=\"22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AASO,MAAM,OAA4B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IAChE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAK,GAAE;gBAAwD,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3G,8OAAC;gBAAK,GAAE;gBAA8C,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACjG,8OAAC;gBAAK,GAAE;gBAA4C,MAAK;;;;;;;;;;;;AAG/D;AAEO,MAAM,UAA+B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACnE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAK,GAAE;gBAA6E,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAChI,8OAAC;gBAAK,GAAE;gBAA2E,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC9H,8OAAC;gBAAK,GAAE;gBAAkB,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACrE,8OAAC;gBAAK,GAAE;gBAAoC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACvF,8OAAC;gBAAK,GAAE;gBAAuF,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1I,8OAAC;gBAAK,GAAE;gBAAgB,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;;;;;;;AAGzE;AAEO,MAAM,cAAmC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACvE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACxE,8OAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,8OAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,8OAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAI,MAAK;;;;;;0BACnC,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E", "debugId": null}}, {"offset": {"line": 1743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/index.ts"], "sourcesContent": ["export { SolarPanel } from './SolarPanel';\nexport { MiningRig } from './MiningRig';\nexport { Cryptocurrency, Bitcoin } from './Cryptocurrency';\nexport { Leaf, Recycle, WindTurbine } from './EcoFriendly';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/app/%28auth%29/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { Button, Input } from '@/components/ui';\nimport { Container, Flex } from '@/components/layout';\nimport { SolarPanel } from '@/components/icons';\nimport { Eye, EyeOff, ArrowLeft } from 'lucide-react';\nimport { useAuth } from '@/hooks/useAuth';\n\nexport default function LoginPage() {\n  const router = useRouter();\n  const { login, loading } = useAuth();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    try {\n      await login(formData.email, formData.password);\n      router.push('/dashboard');\n    } catch (err: any) {\n      setError(err.message || 'Login failed');\n    }\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData(prev => ({\n      ...prev,\n      [e.target.name]: e.target.value,\n    }));\n  };\n\n  return (\n    <div className=\"min-h-screen relative overflow-hidden\">\n      {/* Premium Animated Background */}\n      <div className=\"absolute inset-0 animated-gradient opacity-30\"></div>\n      <div className=\"absolute inset-0 bg-gradient-to-br from-white/95 via-solar-50/90 to-eco-50/95\"></div>\n\n      {/* Floating Elements */}\n      <div className=\"absolute top-20 left-10 w-16 h-16 bg-solar-400/20 rounded-full animate-float\"></div>\n      <div className=\"absolute top-40 right-20 w-12 h-12 bg-eco-400/20 rounded-full animate-float\" style={{animationDelay: '2s'}}></div>\n      <div className=\"absolute bottom-40 left-20 w-10 h-10 bg-purple-400/20 rounded-full animate-float\" style={{animationDelay: '4s'}}></div>\n\n      {/* Responsive Layout */}\n      <div className=\"relative z-10 min-h-screen flex\">\n        {/* Left Side - Hidden on mobile, visible on desktop */}\n        <div className=\"hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-solar-500/10 to-eco-500/10 items-center justify-center p-12\">\n          <div className=\"max-w-lg text-center\">\n            <div className=\"mb-8\">\n              <SolarPanel className=\"h-24 w-24 text-solar-500 mx-auto mb-6\" />\n              <h2 className=\"text-4xl xl:text-5xl font-black text-dark-900 mb-4\">\n                Welcome to the Future of Mining\n              </h2>\n              <p className=\"text-xl text-gray-600 leading-relaxed\">\n                Join thousands of users earning daily returns through our sustainable,\n                solar-powered cryptocurrency mining platform.\n              </p>\n            </div>\n            <div className=\"grid grid-cols-1 gap-6 text-left\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 bg-eco-500/20 rounded-full flex items-center justify-center\">\n                  <span className=\"text-eco-600 font-bold\">✓</span>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-dark-900\">Eco-Friendly Mining</h3>\n                  <p className=\"text-gray-600\">100% solar-powered operations</p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 bg-solar-500/20 rounded-full flex items-center justify-center\">\n                  <span className=\"text-solar-600 font-bold\">⚡</span>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-dark-900\">Daily Returns</h3>\n                  <p className=\"text-gray-600\">Consistent 0.6% - 1.1% daily ROI</p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center\">\n                  <span className=\"text-purple-600 font-bold\">🔒</span>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-dark-900\">Secure Platform</h3>\n                  <p className=\"text-gray-600\">Bank-level security & encryption</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Right Side - Form */}\n        <div className=\"w-full lg:w-1/2 xl:w-2/5 flex items-center justify-center p-4 lg:p-8\">\n          <div className=\"w-full max-w-md\">\n            <div className=\"glass-morphism rounded-3xl p-8 lg:p-10 shadow-2xl\">\n              {/* Premium Header */}\n              <div className=\"text-center mb-8 lg:mb-10\">\n                <Link href=\"/\" className=\"inline-flex items-center space-x-3 mb-6 lg:mb-8 group lg:hidden\">\n                  <div className=\"relative\">\n                    <SolarPanel className=\"h-10 w-10 lg:h-12 lg:w-12 text-solar-500 group-hover:scale-110 transition-transform\" />\n                    <div className=\"absolute inset-0 bg-solar-500/20 rounded-full animate-ping\"></div>\n                  </div>\n                  <span className=\"text-2xl lg:text-4xl font-black bg-gradient-to-r from-solar-600 to-eco-600 bg-clip-text text-transparent\">\n                    HashCoreX\n                  </span>\n                </Link>\n                <h1 className=\"text-2xl lg:text-3xl xl:text-4xl font-black text-dark-900 mb-3 lg:mb-4\">Welcome Back</h1>\n                <p className=\"text-base lg:text-lg text-gray-600 font-medium\">Sign in to your mining dashboard</p>\n              </div>\n\n          {/* Form */}\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\">\n                {error}\n              </div>\n            )}\n\n            <Input\n              label=\"Email Address\"\n              type=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              placeholder=\"Enter your email\"\n              required\n            />\n\n            <Input\n              label=\"Password\"\n              type={showPassword ? 'text' : 'password'}\n              name=\"password\"\n              value={formData.password}\n              onChange={handleChange}\n              placeholder=\"Enter your password\"\n              required\n              rightIcon={\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  {showPassword ? <EyeOff className=\"h-5 w-5\" /> : <Eye className=\"h-5 w-5\" />}\n                </button>\n              }\n            />\n\n            <div className=\"flex items-center justify-between\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  className=\"rounded border-gray-300 text-solar-500 focus:ring-solar-500\"\n                />\n                <span className=\"ml-2 text-sm text-gray-600\">Remember me</span>\n              </label>\n              <Link href=\"/forgot-password\" className=\"text-sm text-solar-500 hover:text-solar-600\">\n                Forgot password?\n              </Link>\n            </div>\n\n            <Button\n              type=\"submit\"\n              variant=\"primary\"\n              size=\"xl\"\n              className=\"w-full font-bold\"\n              loading={loading}\n            >\n              Sign In to Dashboard\n            </Button>\n          </form>\n\n              {/* Footer */}\n              <div className=\"mt-8 text-center\">\n                <p className=\"text-gray-600\">\n                  Don't have an account?{' '}\n                  <Link href=\"/register\" className=\"text-solar-500 hover:text-solar-600 font-medium\">\n                    Sign up\n                  </Link>\n                </p>\n              </div>\n\n              {/* Back to Home */}\n              <div className=\"mt-6 text-center lg:hidden\">\n                <Link href=\"/\" className=\"inline-flex items-center text-gray-500 hover:text-gray-700 text-sm\">\n                  <ArrowLeft className=\"h-4 w-4 mr-1\" />\n                  Back to Home\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QAET,IAAI;YACF,MAAM,MAAM,SAAS,KAAK,EAAE,SAAS,QAAQ;YAC7C,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;YACjC,CAAC;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;gBAA8E,OAAO;oBAAC,gBAAgB;gBAAI;;;;;;0BACzH,8OAAC;gBAAI,WAAU;gBAAmF,OAAO;oBAAC,gBAAgB;gBAAI;;;;;;0BAG9H,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,yIAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAG,WAAU;sDAAqD;;;;;;sDAGnE,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAKvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAyB;;;;;;;;;;;8DAE3C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA8B;;;;;;sEAC5C,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAGjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA2B;;;;;;;;;;;8DAE7C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA8B;;;;;;sEAC5C,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAGjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;8DAE9C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA8B;;;;;;sEAC5C,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;;kEACvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,yIAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;gEAAI,WAAU;;;;;;;;;;;;kEAEjB,8OAAC;wDAAK,WAAU;kEAA2G;;;;;;;;;;;;0DAI7H,8OAAC;gDAAG,WAAU;0DAAyE;;;;;;0DACvF,8OAAC;gDAAE,WAAU;0DAAiD;;;;;;;;;;;;kDAIpE,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;4CACrC,uBACC,8OAAC;gDAAI,WAAU;0DACZ;;;;;;0DAIL,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,aAAY;gDACZ,QAAQ;;;;;;0DAGV,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,MAAM,eAAe,SAAS;gDAC9B,MAAK;gDACL,OAAO,SAAS,QAAQ;gDACxB,UAAU;gDACV,aAAY;gDACZ,QAAQ;gDACR,yBACE,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;8DAET,6BAAe,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;+EAAe,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAKtE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAA6B;;;;;;;;;;;;kEAE/C,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAmB,WAAU;kEAA8C;;;;;;;;;;;;0DAKxF,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;0DACV;;;;;;;;;;;;kDAMC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;gDAAgB;gDACJ;8DACvB,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAAkD;;;;;;;;;;;;;;;;;kDAOvF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;8DACvB,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxD", "debugId": null}}]}