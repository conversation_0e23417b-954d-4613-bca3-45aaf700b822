import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { walletBalanceDb, transactionDb, systemLogDb, userDb } from '@/lib/database';

// POST - Manually adjust user wallet balance (admin only)
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      userId,
      amount,
      type, // 'CREDIT' or 'DEBIT'
      reason,
      description
    } = body;

    // Validate input
    if (!userId || !amount || !type || !reason) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: userId, amount, type, reason' },
        { status: 400 }
      );
    }

    if (!['CREDIT', 'DEBIT'].includes(type)) {
      return NextResponse.json(
        { success: false, error: 'Type must be either CREDIT or DEBIT' },
        { status: 400 }
      );
    }

    const adjustmentAmount = parseFloat(amount);
    if (isNaN(adjustmentAmount) || adjustmentAmount <= 0) {
      return NextResponse.json(
        { success: false, error: 'Amount must be a positive number' },
        { status: 400 }
      );
    }

    // Check if target user exists
    const targetUser = await userDb.findById(userId);
    if (!targetUser) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Get current wallet balance
    const currentWallet = await walletBalanceDb.getOrCreate(userId);
    
    // Calculate new balance
    const finalAmount = type === 'CREDIT' ? adjustmentAmount : -adjustmentAmount;
    const newBalance = currentWallet.availableBalance + finalAmount;

    // Prevent negative balance for debits
    if (type === 'DEBIT' && newBalance < 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Insufficient balance. Current: ${currentWallet.availableBalance}, Requested debit: ${adjustmentAmount}` 
        },
        { status: 400 }
      );
    }

    // Update wallet balance
    await walletBalanceDb.updateBalance(userId, {
      availableBalance: newBalance,
    });

    // Create transaction record
    const transactionType = type === 'CREDIT' ? 'ADMIN_CREDIT' : 'ADMIN_DEBIT';
    const transactionDescription = description || `Admin ${type.toLowerCase()}: ${reason}`;
    
    const transaction = await transactionDb.create({
      userId,
      type: transactionType,
      amount: finalAmount,
      description: transactionDescription,
      status: 'COMPLETED',
    });

    // Log the admin action
    await systemLogDb.create({
      action: 'WALLET_ADJUSTMENT',
      adminId: user.id,
      userId: userId,
      details: {
        type,
        amount: adjustmentAmount,
        reason,
        description: transactionDescription,
        previousBalance: currentWallet.availableBalance,
        newBalance,
        transactionId: transaction.id,
        targetUser: {
          id: targetUser.id,
          email: targetUser.email,
          name: `${targetUser.firstName} ${targetUser.lastName}`,
        },
      },
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: `Wallet ${type.toLowerCase()} completed successfully`,
      data: {
        transactionId: transaction.id,
        userId,
        type,
        amount: adjustmentAmount,
        previousBalance: currentWallet.availableBalance,
        newBalance,
        reason,
        processedBy: user.email,
        processedAt: new Date(),
      },
    });

  } catch (error) {
    console.error('Wallet adjustment error:', error);
    
    // Log error for debugging
    try {
      const { user } = await authenticateRequest(request);
      if (user) {
        await systemLogDb.create({
          action: 'WALLET_ADJUSTMENT_ERROR',
          adminId: user.id,
          details: { 
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined,
          },
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
        });
      }
    } catch (logError) {
      console.error('Failed to log wallet adjustment error:', logError);
    }

    return NextResponse.json(
      { success: false, error: 'Failed to adjust wallet balance' },
      { status: 500 }
    );
  }
}

// GET - Get wallet adjustment history (admin only)
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get wallet adjustment transactions
    const transactions = await transactionDb.findByUserId(userId || '', {
      types: ['ADMIN_CREDIT', 'ADMIN_DEBIT'],
      limit: Math.min(limit, 100),
      offset,
      includeUser: true,
    });

    return NextResponse.json({
      success: true,
      data: {
        transactions: transactions.map(tx => ({
          id: tx.id,
          userId: tx.userId,
          type: tx.type,
          amount: tx.amount,
          description: tx.description,
          status: tx.status,
          createdAt: tx.createdAt,
          user: tx.user ? {
            email: tx.user.email,
            firstName: tx.user.firstName,
            lastName: tx.user.lastName,
          } : null,
        })),
        pagination: {
          limit,
          offset,
          hasMore: transactions.length === limit,
        },
      },
    });

  } catch (error) {
    console.error('Wallet adjustment history error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch wallet adjustment history' },
      { status: 500 }
    );
  }
}
