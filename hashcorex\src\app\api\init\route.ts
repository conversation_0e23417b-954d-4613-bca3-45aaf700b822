import { NextRequest, NextResponse } from 'next/server';
import { initializeServices } from '@/lib/initServices';

let servicesInitialized = false;

/**
 * Initialize background services
 * This endpoint is called automatically when the server starts
 */
export async function GET(request: NextRequest) {
  try {
    if (!servicesInitialized) {
      console.log('Initializing background services...');
      await initializeServices();
      servicesInitialized = true;
      console.log('Background services initialized successfully');
    }

    return NextResponse.json({
      success: true,
      message: 'Services initialized successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error initializing services:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to initialize services',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * Auto-initialize services when this module is loaded
 */
if (typeof window === 'undefined') {
  // Only run on server side
  setTimeout(async () => {
    if (!servicesInitialized) {
      try {
        console.log('Auto-initializing background services...');
        await initializeServices();
        servicesInitialized = true;
        console.log('Background services auto-initialized successfully');
      } catch (error) {
        console.error('Error auto-initializing services:', error);
      }
    }
  }, 1000); // Wait 1 second after server start
}
