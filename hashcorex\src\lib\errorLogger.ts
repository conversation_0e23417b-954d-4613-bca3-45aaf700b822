import { systemLogDb } from './database';

export interface ErrorLogData {
  action: string;
  error: Error | string;
  userId?: string;
  adminId?: string;
  ipAddress?: string;
  userAgent?: string;
  requestUrl?: string;
  requestMethod?: string;
  requestBody?: any;
  additionalData?: any;
}

export interface ClientErrorData {
  message: string;
  stack?: string;
  url: string;
  userAgent: string;
  userId?: string;
  timestamp: string;
  additionalData?: any;
}

export class ErrorLogger {
  /**
   * Log server-side errors with comprehensive details
   */
  static async logError(data: ErrorLogData): Promise<void> {
    try {
      const error = data.error instanceof Error ? data.error : new Error(String(data.error));
      
      const logData = {
        action: data.action,
        userId: data.userId,
        adminId: data.adminId,
        details: {
          message: error.message,
          stack: error.stack,
          name: error.name,
          requestUrl: data.requestUrl,
          requestMethod: data.requestMethod,
          requestBody: data.requestBody,
          additionalData: data.additionalData,
          timestamp: new Date().toISOString(),
        },
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
      };

      await systemLogDb.create(logData);
      
      // Also log to console for development
      console.error(`[${data.action}] Error logged:`, {
        message: error.message,
        stack: error.stack,
        userId: data.userId,
        adminId: data.adminId,
        url: data.requestUrl,
      });
    } catch (logError) {
      // Fallback to console if database logging fails
      console.error('Failed to log error to database:', logError);
      console.error('Original error:', data.error);
    }
  }

  /**
   * Log API route errors with request context
   */
  static async logApiError(
    request: Request,
    error: Error | string,
    action: string,
    userId?: string,
    adminId?: string,
    additionalData?: any
  ): Promise<void> {
    try {
      let requestBody: any = null;
      
      // Safely extract request body
      try {
        if (request.method !== 'GET' && request.headers.get('content-type')?.includes('application/json')) {
          const clonedRequest = request.clone();
          requestBody = await clonedRequest.json();
          
          // Remove sensitive data from logs
          if (requestBody.password) requestBody.password = '[REDACTED]';
          if (requestBody.token) requestBody.token = '[REDACTED]';
          if (requestBody.apiKey) requestBody.apiKey = '[REDACTED]';
        }
      } catch (bodyError) {
        // Ignore body parsing errors
      }

      await this.logError({
        action,
        error,
        userId,
        adminId,
        ipAddress: request.headers.get('x-forwarded-for') || 
                  request.headers.get('x-real-ip') || 
                  'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        requestUrl: request.url,
        requestMethod: request.method,
        requestBody,
        additionalData,
      });
    } catch (logError) {
      console.error('Failed to log API error:', logError);
      console.error('Original error:', error);
    }
  }

  /**
   * Log client-side errors received from frontend
   */
  static async logClientError(data: ClientErrorData): Promise<void> {
    try {
      await systemLogDb.create({
        action: 'CLIENT_ERROR',
        userId: data.userId,
        details: {
          message: data.message,
          stack: data.stack,
          url: data.url,
          timestamp: data.timestamp,
          additionalData: data.additionalData,
        },
        userAgent: data.userAgent,
      });
    } catch (logError) {
      console.error('Failed to log client error:', logError);
    }
  }

  /**
   * Log authentication errors
   */
  static async logAuthError(
    request: Request,
    error: Error | string,
    email?: string,
    additionalData?: any
  ): Promise<void> {
    await this.logApiError(
      request,
      error,
      'AUTH_ERROR',
      undefined,
      undefined,
      { email, ...additionalData }
    );
  }

  /**
   * Log database errors
   */
  static async logDatabaseError(
    error: Error | string,
    operation: string,
    table?: string,
    userId?: string,
    additionalData?: any
  ): Promise<void> {
    try {
      await systemLogDb.create({
        action: 'DATABASE_ERROR',
        userId,
        details: {
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          operation,
          table,
          timestamp: new Date().toISOString(),
          additionalData,
        },
      });
    } catch (logError) {
      console.error('Failed to log database error:', logError);
      console.error('Original error:', error);
    }
  }

  /**
   * Log business logic errors
   */
  static async logBusinessError(
    error: Error | string,
    operation: string,
    userId?: string,
    adminId?: string,
    additionalData?: any
  ): Promise<void> {
    await this.logError({
      action: 'BUSINESS_LOGIC_ERROR',
      error,
      userId,
      adminId,
      additionalData: { operation, ...additionalData },
    });
  }

  /**
   * Log external API errors (Tron, payment gateways, etc.)
   */
  static async logExternalApiError(
    error: Error | string,
    service: string,
    endpoint?: string,
    userId?: string,
    additionalData?: any
  ): Promise<void> {
    await this.logError({
      action: 'EXTERNAL_API_ERROR',
      error,
      userId,
      additionalData: { service, endpoint, ...additionalData },
    });
  }

  /**
   * Log validation errors
   */
  static async logValidationError(
    error: Error | string,
    field: string,
    value?: any,
    userId?: string,
    additionalData?: any
  ): Promise<void> {
    await this.logError({
      action: 'VALIDATION_ERROR',
      error,
      userId,
      additionalData: { field, value, ...additionalData },
    });
  }
}

/**
 * Middleware function to wrap API routes with error logging
 */
export function withErrorLogging<T extends any[], R>(
  handler: (...args: T) => Promise<R>,
  actionName: string
) {
  return async (...args: T): Promise<R> => {
    try {
      return await handler(...args);
    } catch (error) {
      // Extract request if it's the first argument
      const request = args[0] as Request;
      
      if (request && typeof request === 'object' && 'url' in request) {
        await ErrorLogger.logApiError(request, error as Error, actionName);
      } else {
        await ErrorLogger.logError({
          action: actionName,
          error: error as Error,
        });
      }
      
      throw error; // Re-throw to maintain original behavior
    }
  };
}

/**
 * Global error handler for unhandled promise rejections
 */
export function setupGlobalErrorHandlers(): void {
  if (typeof process !== 'undefined') {
    process.on('unhandledRejection', async (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      
      await ErrorLogger.logError({
        action: 'UNHANDLED_REJECTION',
        error: reason instanceof Error ? reason : new Error(String(reason)),
        additionalData: { promise: promise.toString() },
      });
    });

    process.on('uncaughtException', async (error) => {
      console.error('Uncaught Exception:', error);
      
      await ErrorLogger.logError({
        action: 'UNCAUGHT_EXCEPTION',
        error,
      });
      
      // Exit process after logging
      process.exit(1);
    });
  }
}
