// Optional: configure or set up a testing framework before each test.
// If you delete this file, remove `setupFilesAfterEnv` from `jest.config.js`

// Mock environment variables for testing
process.env.DATABASE_URL = process.env.DATABASE_URL || 'postgresql://test:test@localhost:5432/hashcorex_test'
process.env.DIRECT_URL = process.env.DIRECT_URL || 'postgresql://test:test@localhost:5432/hashcorex_test'
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only'
process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3000'

// Global test timeout
jest.setTimeout(30000)

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to ignore specific console methods
  // log: jest.fn(),
  // debug: jest.fn(),
  // info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
}

// Global test utilities
global.testUtils = {
  // Helper to generate random test data
  generateTestUser: (overrides = {}) => ({
    email: `test${Math.random().toString(36).substr(2, 9)}@test.com`,
    firstName: 'Test',
    lastName: 'User',
    password: 'Password123!',
    ...overrides,
  }),
  
  // Helper to generate random referral ID
  generateReferralId: () => `REF${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
  
  // Helper to wait for async operations
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
}

// Clean up function for tests
global.afterEach(async () => {
  // Add any global cleanup here if needed
})

// Setup function for tests
global.beforeEach(async () => {
  // Add any global setup here if needed
})
