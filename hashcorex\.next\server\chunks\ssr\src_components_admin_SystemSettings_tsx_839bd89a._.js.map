{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/SystemSettings.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>le, CardContent, Button, Input, useConfirmDialog } from '@/components/ui';\nimport {\n  Settings,\n  Save,\n  DollarSign,\n  Percent,\n  Zap,\n  Users,\n  Shield,\n  AlertTriangle,\n  CheckCircle,\n  TrendingUp,\n  Wallet,\n  ArrowUpDown,\n  Globe\n} from 'lucide-react';\nimport { formatCurrency } from '@/lib/utils';\n\ninterface EarningsRange {\n  minTHS: number;\n  maxTHS: number;\n  dailyReturnMin: number;\n  dailyReturnMax: number;\n  monthlyReturnMin: number;\n  monthlyReturnMax: number;\n}\n\ninterface SystemSettings {\n  // Mining Unit Pricing\n  thsPriceUSD: number;\n  minPurchaseAmount: number;\n  maxPurchaseAmount: number;\n\n  // Dynamic Earnings Configuration\n  earningsRanges: EarningsRange[];\n  binaryBonusPercentage: number;\n  referralBonusPercentage: number;\n\n  // Binary Matching Settings\n  maxBinaryPointsPerSide: number;\n  binaryPointValue: number;\n  binaryMatchingEnabled: boolean;\n  binaryMatchingSchedule: string;\n\n  // Deposit Settings\n  usdtDepositAddress: string;\n  minDepositAmount: number;\n  maxDepositAmount: number;\n  depositEnabled: boolean;\n  minConfirmations: number;\n  depositFeePercentage: number;\n\n  // Tron Network Configuration\n  tronNetwork: 'mainnet' | 'testnet';\n  tronMainnetApiUrl: string;\n  tronTestnetApiUrl: string;\n  usdtMainnetContract: string;\n  usdtTestnetContract: string;\n\n  // Withdrawal Settings\n  minWithdrawalAmount: number;\n  withdrawalFeeFixed: number;\n  withdrawalFeePercentage: number;\n  withdrawalProcessingDays: number;\n\n  // Platform Settings\n  platformFeePercentage: number;\n  maintenanceMode: boolean;\n  registrationEnabled: boolean;\n  kycRequired: boolean;\n}\n\ntype TabType = 'pricing' | 'earnings' | 'binary' | 'deposits' | 'withdrawals' | 'platform';\n\nexport const SystemSettings: React.FC = () => {\n  const [settings, setSettings] = useState<SystemSettings | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [saveSuccess, setSaveSuccess] = useState(false);\n  const [activeTab, setActiveTab] = useState<TabType>('pricing');\n  const [updatingROI, setUpdatingROI] = useState(false);\n  const [processingMatching, setProcessingMatching] = useState(false);\n\n  const { showConfirm, ConfirmDialog } = useConfirmDialog();\n\n  useEffect(() => {\n    fetchSettings();\n  }, []);\n\n  const fetchSettings = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/admin/settings', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setSettings(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch settings:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSave = async () => {\n    if (!settings) return;\n\n    try {\n      setSaving(true);\n      console.log('Saving settings:', settings);\n      console.log('maxBinaryPointsPerSide value:', settings.maxBinaryPointsPerSide, typeof settings.maxBinaryPointsPerSide);\n      console.log('Full settings object being sent:', JSON.stringify(settings, null, 2));\n\n      const response = await fetch('/api/admin/settings', {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify(settings),\n      });\n\n      if (response.ok) {\n        setSaveSuccess(true);\n        setTimeout(() => setSaveSuccess(false), 3000);\n        console.log('Settings saved successfully');\n      } else {\n        const errorData = await response.json();\n        console.error('Failed to save settings:', errorData);\n      }\n    } catch (error) {\n      console.error('Failed to save settings:', error);\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const updateSetting = (key: keyof SystemSettings, value: any) => {\n    if (!settings) return;\n    setSettings({ ...settings, [key]: value });\n  };\n\n  const updateEarningsRange = (index: number, field: keyof EarningsRange, value: number) => {\n    if (!settings) return;\n    const newRanges = [...settings.earningsRanges];\n    newRanges[index] = { ...newRanges[index], [field]: value };\n    setSettings({ ...settings, earningsRanges: newRanges });\n  };\n\n  const addEarningsRange = () => {\n    if (!settings) return;\n    const newRange: EarningsRange = {\n      minTHS: 0,\n      maxTHS: 100,\n      dailyReturnMin: 0.5,\n      dailyReturnMax: 1.0,\n      monthlyReturnMin: 10.0,\n      monthlyReturnMax: 15.0,\n    };\n    setSettings({ ...settings, earningsRanges: [...settings.earningsRanges, newRange] });\n  };\n\n  const removeEarningsRange = (index: number) => {\n    if (!settings) return;\n    const newRanges = settings.earningsRanges.filter((_, i) => i !== index);\n    setSettings({ ...settings, earningsRanges: newRanges });\n  };\n\n  const handleUpdateMiningUnitsROI = async () => {\n    if (!confirm('Are you sure you want to update all existing mining units with the new ROI configuration? This will recalculate ROI for all active mining units based on their TH/s amounts.')) {\n      return;\n    }\n\n    setUpdatingROI(true);\n    try {\n      const response = await fetch('/api/admin/update-mining-units-roi', {\n        method: 'POST',\n        credentials: 'include',\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        alert(`Mining units ROI update completed successfully!\\n\\nUnits updated: ${data.data.unitsUpdated}\\nTotal units: ${data.data.totalUnits}`);\n      } else {\n        alert(`Error: ${data.error}`);\n      }\n    } catch (error) {\n      console.error('Mining units ROI update error:', error);\n      alert('Failed to update mining units ROI. Please try again.');\n    } finally {\n      setUpdatingROI(false);\n    }\n  };\n\n  const handleManualBinaryMatching = () => {\n    showConfirm({\n      title: 'Manual Binary Matching',\n      message: 'Are you sure you want to manually trigger binary matching? This will process all users with binary points and distribute earnings.',\n      variant: 'warning',\n      confirmText: 'Process Matching',\n      darkMode: true,\n      onConfirm: async () => {\n        setProcessingMatching(true);\n        try {\n          const response = await fetch('/api/admin/binary-matching/manual', {\n            method: 'POST',\n            credentials: 'include',\n          });\n\n          const data = await response.json();\n\n          if (data.success) {\n            // Show detailed results\n            const results = data.data.matchingResults || [];\n            const totalUsers = data.data.usersProcessed || 0;\n            const totalPayouts = data.data.totalPayouts || 0;\n\n            let resultsMessage = `Binary matching completed successfully!\\n\\n`;\n            resultsMessage += `📊 SUMMARY:\\n`;\n            resultsMessage += `• Users processed: ${totalUsers}\\n`;\n            resultsMessage += `• Total payouts: $${totalPayouts.toFixed(2)}\\n`;\n            resultsMessage += `• Total matched points: ${results.reduce((sum, r) => sum + r.matchedPoints, 0).toFixed(2)}\\n\\n`;\n\n            if (results.length > 0) {\n              resultsMessage += `💰 DETAILED RESULTS:\\n`;\n              results.slice(0, 10).forEach((result, index) => {\n                resultsMessage += `${index + 1}. User ${result.userId.substring(0, 8)}...\\n`;\n                resultsMessage += `   • Matched: ${result.matchedPoints.toFixed(2)} points\\n`;\n                resultsMessage += `   • Payout: $${result.payout.toFixed(2)}\\n`;\n                resultsMessage += `   • Remaining: L:${result.remainingLeftPoints.toFixed(2)} | R:${result.remainingRightPoints.toFixed(2)}\\n\\n`;\n              });\n\n              if (results.length > 10) {\n                resultsMessage += `... and ${results.length - 10} more users\\n\\n`;\n              }\n            }\n\n            resultsMessage += `✅ All earnings have been credited to user wallets.`;\n\n            showConfirm({\n              title: 'Binary Matching Results',\n              message: resultsMessage,\n              variant: 'success',\n              confirmText: 'OK',\n              cancelText: '',\n              darkMode: true,\n              onConfirm: () => {},\n            });\n          } else {\n            showConfirm({\n              title: 'Error',\n              message: `Error: ${data.error}`,\n              variant: 'danger',\n              confirmText: 'OK',\n              cancelText: '',\n              darkMode: true,\n              onConfirm: () => {},\n            });\n          }\n        } catch (error) {\n          console.error('Manual binary matching error:', error);\n          showConfirm({\n            title: 'Error',\n            message: 'Failed to process binary matching. Please try again.',\n            variant: 'danger',\n            confirmText: 'OK',\n            cancelText: '',\n            darkMode: true,\n            onConfirm: () => {},\n          });\n        } finally {\n          setProcessingMatching(false);\n        }\n      },\n    });\n  };\n\n  const tabs = [\n    { id: 'pricing' as TabType, label: 'Mining & Pricing', icon: Zap },\n    { id: 'earnings' as TabType, label: 'Earnings Config', icon: TrendingUp },\n    { id: 'binary' as TabType, label: 'Binary Matching', icon: ArrowUpDown },\n    { id: 'deposits' as TabType, label: 'Deposits', icon: Wallet },\n    { id: 'withdrawals' as TabType, label: 'Withdrawals', icon: DollarSign },\n    { id: 'platform' as TabType, label: 'Platform', icon: Globe },\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-slate-700 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-64 bg-slate-700 rounded\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!settings) {\n    return (\n      <div className=\"text-center py-12\">\n        <AlertTriangle className=\"h-12 w-12 text-red-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-white mb-2\">Failed to Load Settings</h3>\n        <p className=\"text-slate-400\">Unable to load system settings. Please try again.</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-white\">System Settings</h1>\n          <p className=\"text-slate-400 mt-1\">Configure platform parameters and business rules</p>\n        </div>\n        <Button\n          onClick={handleSave}\n          loading={saving}\n          className=\"flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white\"\n        >\n          {saveSuccess ? (\n            <>\n              <CheckCircle className=\"h-4 w-4\" />\n              Saved\n            </>\n          ) : (\n            <>\n              <Save className=\"h-4 w-4\" />\n              Save Changes\n            </>\n          )}\n        </Button>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-slate-700\">\n        <nav className=\"flex space-x-8\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon;\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-400'\n                    : 'border-transparent text-slate-400 hover:text-slate-300 hover:border-slate-300'\n                }`}\n              >\n                <Icon className=\"h-4 w-4\" />\n                {tab.label}\n              </button>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {renderTabContent()}\n\n      {/* Confirmation Dialog */}\n      <ConfirmDialog />\n    </div>\n  );\n\n  function renderTabContent() {\n    if (!settings) return null;\n\n    switch (activeTab) {\n      case 'pricing':\n        return renderPricingTab();\n      case 'earnings':\n        return renderEarningsTab();\n      case 'binary':\n        return renderBinaryTab();\n      case 'deposits':\n        return renderDepositsTab();\n      case 'withdrawals':\n        return renderWithdrawalsTab();\n      case 'platform':\n        return renderPlatformTab();\n      default:\n        return null;\n    }\n  }\n\n  function renderPricingTab() {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <Zap className=\"h-5 w-5 text-blue-400\" />\n            Mining Unit Pricing\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                THS Price (USD)\n              </label>\n              <div className=\"relative\">\n                <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.thsPriceUSD}\n                  onChange={(e) => updateSetting('thsPriceUSD', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Minimum Purchase ($)\n              </label>\n              <div className=\"relative\">\n                <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.minPurchaseAmount}\n                  onChange={(e) => updateSetting('minPurchaseAmount', parseFloat(e.target.value) || 0)}\n                  placeholder=\"50\"\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Maximum Purchase ($)\n              </label>\n              <div className=\"relative\">\n                <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.maxPurchaseAmount}\n                  onChange={(e) => updateSetting('maxPurchaseAmount', parseFloat(e.target.value) || 0)}\n                  placeholder=\"100000\"\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  function renderEarningsTab() {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <TrendingUp className=\"h-5 w-5 text-orange-400\" />\n            Dynamic Earnings Configuration\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          {/* TH/s Based Earnings Ranges */}\n          <div>\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-medium text-white\">TH/s Based Daily Returns</h3>\n              <Button\n                onClick={addEarningsRange}\n                className=\"bg-orange-600 text-white text-sm\"\n              >\n                Add Range\n              </Button>\n            </div>\n\n            <div className=\"space-y-4\">\n              {settings.earningsRanges?.map((range, index) => (\n                <div key={index} className=\"p-4 bg-slate-700 rounded-lg\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                        Min TH/s\n                      </label>\n                      <Input\n                        type=\"number\"\n                        step=\"0.1\"\n                        value={range.minTHS}\n                        onChange={(e) => updateEarningsRange(index, 'minTHS', parseFloat(e.target.value) || 0)}\n                        className=\"bg-slate-600 border-slate-500 text-white\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                        Max TH/s\n                      </label>\n                      <Input\n                        type=\"number\"\n                        step=\"0.1\"\n                        value={range.maxTHS}\n                        onChange={(e) => updateEarningsRange(index, 'maxTHS', parseFloat(e.target.value) || 0)}\n                        className=\"bg-slate-600 border-slate-500 text-white\"\n                      />\n                    </div>\n                    <div className=\"flex items-end\">\n                      <Button\n                        onClick={() => removeEarningsRange(index)}\n                        className=\"bg-red-600 hover:bg-red-700 text-white w-full\"\n                      >\n                        Remove\n                      </Button>\n                    </div>\n                  </div>\n                  <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                        Min Daily Return (%)\n                      </label>\n                      <Input\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={range.dailyReturnMin}\n                        onChange={(e) => updateEarningsRange(index, 'dailyReturnMin', parseFloat(e.target.value) || 0)}\n                        className=\"bg-slate-600 border-slate-500 text-white\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                        Max Daily Return (%)\n                      </label>\n                      <Input\n                        type=\"number\"\n                        step=\"0.01\"\n                        value={range.dailyReturnMax}\n                        onChange={(e) => updateEarningsRange(index, 'dailyReturnMax', parseFloat(e.target.value) || 0)}\n                        className=\"bg-slate-600 border-slate-500 text-white\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                        Min Monthly Return (%)\n                      </label>\n                      <Input\n                        type=\"number\"\n                        step=\"0.1\"\n                        value={range.monthlyReturnMin}\n                        onChange={(e) => updateEarningsRange(index, 'monthlyReturnMin', parseFloat(e.target.value) || 0)}\n                        className=\"bg-slate-600 border-slate-500 text-white\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                        Max Monthly Return (%)\n                      </label>\n                      <Input\n                        type=\"number\"\n                        step=\"0.1\"\n                        value={range.monthlyReturnMax}\n                        onChange={(e) => updateEarningsRange(index, 'monthlyReturnMax', parseFloat(e.target.value) || 0)}\n                        className=\"bg-slate-600 border-slate-500 text-white\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n\n\n          {/* Bonus Percentages */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Binary Bonus (%)\n              </label>\n              <div className=\"relative\">\n                <Percent className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.binaryBonusPercentage}\n                  onChange={(e) => updateSetting('binaryBonusPercentage', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Referral Bonus (%)\n              </label>\n              <div className=\"relative\">\n                <Percent className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.referralBonusPercentage}\n                  onChange={(e) => updateSetting('referralBonusPercentage', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"mt-4 p-4 bg-slate-700 rounded-lg\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div>\n                <h4 className=\"text-sm font-medium text-slate-300 mb-2\">Update Existing Mining Units</h4>\n                <p className=\"text-xs text-slate-400\">Apply new ROI configuration to all active mining units</p>\n              </div>\n              <Button\n                onClick={handleUpdateMiningUnitsROI}\n                loading={updatingROI}\n                className=\"bg-orange-600 hover:bg-orange-700 text-white\"\n              >\n                Update All Units\n              </Button>\n            </div>\n\n            <h4 className=\"text-sm font-medium text-slate-300 mb-2\">Important Notes:</h4>\n            <ul className=\"text-xs text-slate-400 space-y-1\">\n              <li>• Changes to earnings configuration will affect new mining units automatically</li>\n              <li>• Use \"Update All Units\" button to apply changes to existing active mining units</li>\n              <li>• Monthly return limits are enforced to prevent excessive payouts</li>\n              <li>• TH/s ranges should not overlap for proper calculation</li>\n              <li>• Daily returns are randomly selected within the specified range for each unit</li>\n            </ul>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  function renderBinaryTab() {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <ArrowUpDown className=\"h-5 w-5 text-purple-400\" />\n            Binary Matching Settings\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Max Points Per Side\n              </label>\n              <Input\n                type=\"number\"\n                value={settings.maxBinaryPointsPerSide}\n                onChange={(e) => {\n                  const value = e.target.value;\n                  const numValue = value === '' ? 0 : parseInt(value);\n                  if (!isNaN(numValue) && numValue >= 0) {\n                    updateSetting('maxBinaryPointsPerSide', numValue);\n                  }\n                }}\n                className=\"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-purple-500\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Point Value (USD)\n              </label>\n              <div className=\"relative\">\n                <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.binaryPointValue}\n                  onChange={(e) => updateSetting('binaryPointValue', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-purple-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Matching Schedule\n              </label>\n              <Input\n                type=\"text\"\n                value={settings.binaryMatchingSchedule}\n                onChange={(e) => updateSetting('binaryMatchingSchedule', e.target.value)}\n                placeholder=\"Weekly at 15:00 UTC\"\n                className=\"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-purple-500\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center justify-between pt-4 border-t border-slate-600\">\n            <div>\n              <h4 className=\"text-sm font-medium text-white\">Binary Matching Enabled</h4>\n              <p className=\"text-sm text-slate-400\">Enable automatic binary point matching</p>\n            </div>\n            <label className=\"relative inline-flex items-center cursor-pointer\">\n              <input\n                type=\"checkbox\"\n                checked={settings.binaryMatchingEnabled}\n                onChange={(e) => updateSetting('binaryMatchingEnabled', e.target.checked)}\n                className=\"sr-only peer\"\n              />\n              <div className=\"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600\"></div>\n            </label>\n          </div>\n\n          <div className=\"mt-4 p-4 bg-slate-700 rounded-lg\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div>\n                <h4 className=\"text-sm font-medium text-slate-300 mb-2\">Manual Binary Matching</h4>\n                <p className=\"text-xs text-slate-400\">Trigger binary matching process manually</p>\n              </div>\n              <Button\n                onClick={handleManualBinaryMatching}\n                loading={processingMatching}\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                Process Matching\n              </Button>\n            </div>\n\n            <h4 className=\"text-sm font-medium text-slate-300 mb-2\">Binary Matching Rules:</h4>\n            <ul className=\"text-xs text-slate-400 space-y-1\">\n              <li>• Points are matched weekly at the scheduled time</li>\n              <li>• Matched points are paid out at the configured point value</li>\n              <li>• Excess points beyond the maximum are reset (pressure out)</li>\n              <li>• Only active users (with mining units) receive binary points</li>\n              <li>• Manual matching can be triggered anytime by admin</li>\n            </ul>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  function renderDepositsTab() {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <Wallet className=\"h-5 w-5 text-green-400\" />\n            Deposit Settings\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          {/* Tron Network Configuration */}\n          <div className=\"bg-slate-700 p-4 rounded-lg border border-slate-600\">\n            <h3 className=\"text-lg font-medium text-white mb-4 flex items-center gap-2\">\n              <div className={`w-3 h-3 rounded-full ${settings.tronNetwork === 'mainnet' ? 'bg-green-500' : 'bg-orange-500'} animate-pulse`}></div>\n              Tron Network Configuration\n            </h3>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Active Network\n                </label>\n                <select\n                  value={settings.tronNetwork}\n                  onChange={(e) => updateSetting('tronNetwork', e.target.value as 'mainnet' | 'testnet')}\n                  className=\"w-full bg-slate-600 border-slate-500 text-white rounded-md px-3 py-2 focus:border-blue-500 focus:outline-none\"\n                >\n                  <option value=\"testnet\">Shasta Testnet</option>\n                  <option value=\"mainnet\">Mainnet</option>\n                </select>\n                <p className=\"text-xs text-slate-400 mt-1\">\n                  {settings.tronNetwork === 'mainnet' ? 'Production network for real transactions' : 'Test network for development'}\n                </p>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Current Network Status\n                </label>\n                <div className=\"flex items-center gap-2 p-2 bg-slate-600 rounded-md\">\n                  <div className={`w-2 h-2 rounded-full ${settings.tronNetwork === 'mainnet' ? 'bg-green-500' : 'bg-orange-500'} animate-pulse`}></div>\n                  <span className=\"text-sm text-white\">\n                    {settings.tronNetwork === 'mainnet' ? 'Mainnet Active' : 'Testnet Active'}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Mainnet API URL\n                </label>\n                <Input\n                  type=\"text\"\n                  value={settings.tronMainnetApiUrl}\n                  onChange={(e) => updateSetting('tronMainnetApiUrl', e.target.value)}\n                  className=\"bg-slate-600 border-slate-500 text-white placeholder-slate-400 focus:border-blue-500 font-mono text-sm\"\n                  placeholder=\"https://api.trongrid.io\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Testnet API URL\n                </label>\n                <Input\n                  type=\"text\"\n                  value={settings.tronTestnetApiUrl}\n                  onChange={(e) => updateSetting('tronTestnetApiUrl', e.target.value)}\n                  className=\"bg-slate-600 border-slate-500 text-white placeholder-slate-400 focus:border-blue-500 font-mono text-sm\"\n                  placeholder=\"https://api.shasta.trongrid.io\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Mainnet USDT Contract\n                </label>\n                <Input\n                  type=\"text\"\n                  value={settings.usdtMainnetContract}\n                  onChange={(e) => updateSetting('usdtMainnetContract', e.target.value)}\n                  className=\"bg-slate-600 border-slate-500 text-white placeholder-slate-400 focus:border-blue-500 font-mono text-sm\"\n                  placeholder=\"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                  Testnet USDT Contract\n                </label>\n                <Input\n                  type=\"text\"\n                  value={settings.usdtTestnetContract}\n                  onChange={(e) => updateSetting('usdtTestnetContract', e.target.value)}\n                  className=\"bg-slate-600 border-slate-500 text-white placeholder-slate-400 focus:border-blue-500 font-mono text-sm\"\n                  placeholder=\"TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                USDT TRC20 Deposit Address\n              </label>\n              <Input\n                type=\"text\"\n                value={settings.usdtDepositAddress}\n                onChange={(e) => updateSetting('usdtDepositAddress', e.target.value)}\n                placeholder=\"Enter USDT TRC20 address (e.g., TXXXxxxXXXxxxXXX...)\"\n                className=\"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500 font-mono text-sm\"\n              />\n              <p className=\"text-xs text-slate-400 mt-1\">\n                This address will be displayed to users for USDT deposits\n              </p>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Minimum Deposit\n              </label>\n              <div className=\"relative\">\n                <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.minDepositAmount}\n                  onChange={(e) => updateSetting('minDepositAmount', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Maximum Deposit\n              </label>\n              <div className=\"relative\">\n                <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.maxDepositAmount}\n                  onChange={(e) => updateSetting('maxDepositAmount', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Deposit Fee (%)\n              </label>\n              <div className=\"relative\">\n                <Percent className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.depositFeePercentage}\n                  onChange={(e) => updateSetting('depositFeePercentage', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Minimum Confirmations\n              </label>\n              <Input\n                type=\"number\"\n                value={settings.minConfirmations}\n                onChange={(e) => updateSetting('minConfirmations', parseInt(e.target.value) || 0)}\n                className=\"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500\"\n              />\n              <p className=\"text-xs text-slate-400 mt-1\">\n                Number of blockchain confirmations required\n              </p>\n            </div>\n            <div className=\"flex items-center justify-between pt-6\">\n              <div>\n                <h4 className=\"text-sm font-medium text-white\">Deposits Enabled</h4>\n                <p className=\"text-sm text-slate-400\">Allow users to make deposits</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.depositEnabled}\n                  onChange={(e) => updateSetting('depositEnabled', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600\"></div>\n              </label>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  function renderWithdrawalsTab() {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <DollarSign className=\"h-5 w-5 text-red-400\" />\n            Withdrawal Settings\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Minimum Withdrawal\n              </label>\n              <div className=\"relative\">\n                <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.minWithdrawalAmount}\n                  onChange={(e) => updateSetting('minWithdrawalAmount', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Fixed Fee ($)\n              </label>\n              <div className=\"relative\">\n                <DollarSign className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.withdrawalFeeFixed}\n                  onChange={(e) => updateSetting('withdrawalFeeFixed', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Percentage Fee (%)\n              </label>\n              <div className=\"relative\">\n                <Percent className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.withdrawalFeePercentage}\n                  onChange={(e) => updateSetting('withdrawalFeePercentage', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500\"\n                />\n              </div>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Processing Days\n              </label>\n              <Input\n                type=\"number\"\n                value={settings.withdrawalProcessingDays}\n                onChange={(e) => updateSetting('withdrawalProcessingDays', parseInt(e.target.value) || 0)}\n                className=\"bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500\"\n              />\n            </div>\n          </div>\n\n          <div className=\"mt-4 p-4 bg-slate-700 rounded-lg\">\n            <h4 className=\"text-sm font-medium text-slate-300 mb-2\">Fee Calculation Example:</h4>\n            <p className=\"text-xs text-slate-400\">\n              For a $100 withdrawal: Fixed Fee (${settings.withdrawalFeeFixed}) + Percentage Fee (${(100 * (settings.withdrawalFeePercentage / 100)).toFixed(2)}) = Total Fee: ${(settings.withdrawalFeeFixed + (100 * (settings.withdrawalFeePercentage / 100))).toFixed(2)}\n            </p>\n            <p className=\"text-xs text-slate-400 mt-1\">\n              User receives: ${(100 - (settings.withdrawalFeeFixed + (100 * (settings.withdrawalFeePercentage / 100)))).toFixed(2)}\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  function renderPlatformTab() {\n    return (\n      <Card className=\"bg-slate-800 border-slate-700\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2 text-white\">\n            <Globe className=\"h-5 w-5 text-blue-400\" />\n            Platform Settings\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-slate-300 mb-2\">\n                Platform Fee (%)\n              </label>\n              <div className=\"relative\">\n                <Percent className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4\" />\n                <Input\n                  type=\"number\"\n                  step=\"0.01\"\n                  value={settings.platformFeePercentage}\n                  onChange={(e) => updateSetting('platformFeePercentage', parseFloat(e.target.value) || 0)}\n                  className=\"pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div className=\"space-y-4 pt-4 border-t border-slate-600\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h4 className=\"text-sm font-medium text-white\">Maintenance Mode</h4>\n                <p className=\"text-sm text-slate-400\">Temporarily disable platform access</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.maintenanceMode}\n                  onChange={(e) => updateSetting('maintenanceMode', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h4 className=\"text-sm font-medium text-white\">Registration Enabled</h4>\n                <p className=\"text-sm text-slate-400\">Allow new user registrations</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.registrationEnabled}\n                  onChange={(e) => updateSetting('registrationEnabled', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h4 className=\"text-sm font-medium text-white\">KYC Required</h4>\n                <p className=\"text-sm text-slate-400\">Require KYC verification for withdrawals</p>\n              </div>\n              <label className=\"relative inline-flex items-center cursor-pointer\">\n                <input\n                  type=\"checkbox\"\n                  checked={settings.kycRequired}\n                  onChange={(e) => updateSetting('kycRequired', e.target.checked)}\n                  className=\"sr-only peer\"\n                />\n                <div className=\"w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"></div>\n              </label>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AA6EO,MAAM,iBAA2B;IACtC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAEtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,YAAY,KAAK,IAAI;gBACvB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,UAAU;YACV,QAAQ,GAAG,CAAC,oBAAoB;YAChC,QAAQ,GAAG,CAAC,iCAAiC,SAAS,sBAAsB,EAAE,OAAO,SAAS,sBAAsB;YACpH,QAAQ,GAAG,CAAC,oCAAoC,KAAK,SAAS,CAAC,UAAU,MAAM;YAE/E,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,eAAe;gBACf,WAAW,IAAM,eAAe,QAAQ;gBACxC,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,gBAAgB,CAAC,KAA2B;QAChD,IAAI,CAAC,UAAU;QACf,YAAY;YAAE,GAAG,QAAQ;YAAE,CAAC,IAAI,EAAE;QAAM;IAC1C;IAEA,MAAM,sBAAsB,CAAC,OAAe,OAA4B;QACtE,IAAI,CAAC,UAAU;QACf,MAAM,YAAY;eAAI,SAAS,cAAc;SAAC;QAC9C,SAAS,CAAC,MAAM,GAAG;YAAE,GAAG,SAAS,CAAC,MAAM;YAAE,CAAC,MAAM,EAAE;QAAM;QACzD,YAAY;YAAE,GAAG,QAAQ;YAAE,gBAAgB;QAAU;IACvD;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;QACf,MAAM,WAA0B;YAC9B,QAAQ;YACR,QAAQ;YACR,gBAAgB;YAChB,gBAAgB;YAChB,kBAAkB;YAClB,kBAAkB;QACpB;QACA,YAAY;YAAE,GAAG,QAAQ;YAAE,gBAAgB;mBAAI,SAAS,cAAc;gBAAE;aAAS;QAAC;IACpF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,UAAU;QACf,MAAM,YAAY,SAAS,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACjE,YAAY;YAAE,GAAG,QAAQ;YAAE,gBAAgB;QAAU;IACvD;IAEA,MAAM,6BAA6B;QACjC,IAAI,CAAC,QAAQ,iLAAiL;YAC5L;QACF;QAEA,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sCAAsC;gBACjE,QAAQ;gBACR,aAAa;YACf;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,CAAC,kEAAkE,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,KAAK,IAAI,CAAC,UAAU,EAAE;YAC3I,OAAO;gBACL,MAAM,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,6BAA6B;QACjC,YAAY;YACV,OAAO;YACP,SAAS;YACT,SAAS;YACT,aAAa;YACb,UAAU;YACV,WAAW;gBACT,sBAAsB;gBACtB,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,qCAAqC;wBAChE,QAAQ;wBACR,aAAa;oBACf;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,KAAK,OAAO,EAAE;wBAChB,wBAAwB;wBACxB,MAAM,UAAU,KAAK,IAAI,CAAC,eAAe,IAAI,EAAE;wBAC/C,MAAM,aAAa,KAAK,IAAI,CAAC,cAAc,IAAI;wBAC/C,MAAM,eAAe,KAAK,IAAI,CAAC,YAAY,IAAI;wBAE/C,IAAI,iBAAiB,CAAC,2CAA2C,CAAC;wBAClE,kBAAkB,CAAC,aAAa,CAAC;wBACjC,kBAAkB,CAAC,mBAAmB,EAAE,WAAW,EAAE,CAAC;wBACtD,kBAAkB,CAAC,kBAAkB,EAAE,aAAa,OAAO,CAAC,GAAG,EAAE,CAAC;wBAClE,kBAAkB,CAAC,wBAAwB,EAAE,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC,GAAG,IAAI,CAAC;wBAElH,IAAI,QAAQ,MAAM,GAAG,GAAG;4BACtB,kBAAkB,CAAC,sBAAsB,CAAC;4BAC1C,QAAQ,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,QAAQ;gCACpC,kBAAkB,GAAG,QAAQ,EAAE,OAAO,EAAE,OAAO,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,KAAK,CAAC;gCAC5E,kBAAkB,CAAC,cAAc,EAAE,OAAO,aAAa,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC;gCAC7E,kBAAkB,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;gCAC/D,kBAAkB,CAAC,kBAAkB,EAAE,OAAO,mBAAmB,CAAC,OAAO,CAAC,GAAG,KAAK,EAAE,OAAO,oBAAoB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;4BAClI;4BAEA,IAAI,QAAQ,MAAM,GAAG,IAAI;gCACvB,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,MAAM,GAAG,GAAG,eAAe,CAAC;4BACnE;wBACF;wBAEA,kBAAkB,CAAC,kDAAkD,CAAC;wBAEtE,YAAY;4BACV,OAAO;4BACP,SAAS;4BACT,SAAS;4BACT,aAAa;4BACb,YAAY;4BACZ,UAAU;4BACV,WAAW,KAAO;wBACpB;oBACF,OAAO;wBACL,YAAY;4BACV,OAAO;4BACP,SAAS,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;4BAC/B,SAAS;4BACT,aAAa;4BACb,YAAY;4BACZ,UAAU;4BACV,WAAW,KAAO;wBACpB;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iCAAiC;oBAC/C,YAAY;wBACV,OAAO;wBACP,SAAS;wBACT,SAAS;wBACT,aAAa;wBACb,YAAY;wBACZ,UAAU;wBACV,WAAW,KAAO;oBACpB;gBACF,SAAU;oBACR,sBAAsB;gBACxB;YACF;QACF;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAsB,OAAO;YAAoB,MAAM,gMAAA,CAAA,MAAG;QAAC;QACjE;YAAE,IAAI;YAAuB,OAAO;YAAmB,MAAM,kNAAA,CAAA,aAAU;QAAC;QACxE;YAAE,IAAI;YAAqB,OAAO;YAAmB,MAAM,wNAAA,CAAA,cAAW;QAAC;QACvE;YAAE,IAAI;YAAuB,OAAO;YAAY,MAAM,sMAAA,CAAA,SAAM;QAAC;QAC7D;YAAE,IAAI;YAA0B,OAAO;YAAe,MAAM,kNAAA,CAAA,aAAU;QAAC;QACvE;YAAE,IAAI;YAAuB,OAAO;YAAY,MAAM,oMAAA,CAAA,QAAK;QAAC;KAC7D;IAED,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,8OAAC;oBAAG,WAAU;8BAAsC;;;;;;8BACpD,8OAAC;oBAAE,WAAU;8BAAiB;;;;;;;;;;;;IAGpC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;kCAErC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAS;wBACT,WAAU;kCAET,4BACC;;8CACE,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;;yDAIrC;;8CACE,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;0BAQpC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,qBACE,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,mFAAmF,EAC7F,cAAc,IAAI,EAAE,GAChB,kCACA,iFACJ;;8CAEF,8OAAC;oCAAK,WAAU;;;;;;gCACf,IAAI,KAAK;;2BATL,IAAI,EAAE;;;;;oBAYjB;;;;;;;;;;;YAKH;0BAGD,8OAAC;;;;;;;;;;;;IAIL,SAAS;QACP,IAAI,CAAC,UAAU,OAAO;QAEtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,SAAS;QACP,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAA0B;;;;;;;;;;;;8BAI7C,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAgD;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IAAM,cAAc,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gDAC5E,WAAU;;;;;;;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAgD;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,iBAAiB;gDACjC,UAAU,CAAC,IAAM,cAAc,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gDAClF,aAAY;gDACZ,WAAU;;;;;;;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAgD;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,iBAAiB;gDACjC,UAAU,CAAC,IAAM,cAAc,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gDAClF,aAAY;gDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ1B;IAEA,SAAS;QACP,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAA4B;;;;;;;;;;;;8BAItD,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;8CAKH,8OAAC;oCAAI,WAAU;8CACZ,SAAS,cAAc,EAAE,IAAI,CAAC,OAAO,sBACpC,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAgD;;;;;;8EAGjE,8OAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,MAAK;oEACL,OAAO,MAAM,MAAM;oEACnB,UAAU,CAAC,IAAM,oBAAoB,OAAO,UAAU,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEACpF,WAAU;;;;;;;;;;;;sEAGd,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAgD;;;;;;8EAGjE,8OAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,MAAK;oEACL,OAAO,MAAM,MAAM;oEACnB,UAAU,CAAC,IAAM,oBAAoB,OAAO,UAAU,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEACpF,WAAU;;;;;;;;;;;;sEAGd,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAS,IAAM,oBAAoB;gEACnC,WAAU;0EACX;;;;;;;;;;;;;;;;;8DAKL,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAgD;;;;;;8EAGjE,8OAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,MAAK;oEACL,OAAO,MAAM,cAAc;oEAC3B,UAAU,CAAC,IAAM,oBAAoB,OAAO,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAC5F,WAAU;;;;;;;;;;;;sEAGd,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAgD;;;;;;8EAGjE,8OAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,MAAK;oEACL,OAAO,MAAM,cAAc;oEAC3B,UAAU,CAAC,IAAM,oBAAoB,OAAO,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAC5F,WAAU;;;;;;;;;;;;sEAGd,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAgD;;;;;;8EAGjE,8OAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,MAAK;oEACL,OAAO,MAAM,gBAAgB;oEAC7B,UAAU,CAAC,IAAM,oBAAoB,OAAO,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAC9F,WAAU;;;;;;;;;;;;sEAGd,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAAgD;;;;;;8EAGjE,8OAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,MAAK;oEACL,OAAO,MAAM,gBAAgB;oEAC7B,UAAU,CAAC,IAAM,oBAAoB,OAAO,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oEAC9F,WAAU;;;;;;;;;;;;;;;;;;;2CAjFR;;;;;;;;;;;;;;;;sCA6FhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,qBAAqB;oDACrC,UAAU,CAAC,IAAM,cAAc,yBAAyB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACtF,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,uBAAuB;oDACvC,UAAU,CAAC,IAAM,cAAc,2BAA2B,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACxF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA0C;;;;;;8DACxD,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAExC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;8CAKH,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMhB;IAEA,SAAS;QACP,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,wNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAA4B;;;;;;;;;;;;8BAIvD,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,sBAAsB;4CACtC,UAAU,CAAC;gDACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAC5B,MAAM,WAAW,UAAU,KAAK,IAAI,SAAS;gDAC7C,IAAI,CAAC,MAAM,aAAa,YAAY,GAAG;oDACrC,cAAc,0BAA0B;gDAC1C;4CACF;4CACA,WAAU;;;;;;;;;;;;8CAGd,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,gBAAgB;oDAChC,UAAU,CAAC,IAAM,cAAc,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACjF,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,sBAAsB;4CACtC,UAAU,CAAC,IAAM,cAAc,0BAA0B,EAAE,MAAM,CAAC,KAAK;4CACvE,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;8CAExC,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,SAAS,qBAAqB;4CACvC,UAAU,CAAC,IAAM,cAAc,yBAAyB,EAAE,MAAM,CAAC,OAAO;4CACxE,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAInB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA0C;;;;;;8DACxD,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAExC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;8CAKH,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMhB;IAEA,SAAS;QACP,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAA2B;;;;;;;;;;;;8BAIjD,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAI,WAAW,CAAC,qBAAqB,EAAE,SAAS,WAAW,KAAK,YAAY,iBAAiB,gBAAgB,cAAc,CAAC;;;;;;wCAAQ;;;;;;;8CAIvI,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAgD;;;;;;8DAGjE,8OAAC;oDACC,OAAO,SAAS,WAAW;oDAC3B,UAAU,CAAC,IAAM,cAAc,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC5D,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAU;;;;;;;;;;;;8DAE1B,8OAAC;oDAAE,WAAU;8DACV,SAAS,WAAW,KAAK,YAAY,6CAA6C;;;;;;;;;;;;sDAIvF,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAgD;;;;;;8DAGjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,qBAAqB,EAAE,SAAS,WAAW,KAAK,YAAY,iBAAiB,gBAAgB,cAAc,CAAC;;;;;;sEAC7H,8OAAC;4DAAK,WAAU;sEACb,SAAS,WAAW,KAAK,YAAY,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;8CAMjE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAgD;;;;;;8DAGjE,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,cAAc,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDAClE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAgD;;;;;;8DAGjE,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,SAAS,iBAAiB;oDACjC,UAAU,CAAC,IAAM,cAAc,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDAClE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAgD;;;;;;8DAGjE,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,SAAS,mBAAmB;oDACnC,UAAU,CAAC,IAAM,cAAc,uBAAuB,EAAE,MAAM,CAAC,KAAK;oDACpE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAgD;;;;;;8DAGjE,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,OAAO,SAAS,mBAAmB;oDACnC,UAAU,CAAC,IAAM,cAAc,uBAAuB,EAAE,MAAM,CAAC,KAAK;oDACpE,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAMpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAgD;;;;;;kDAGjE,8OAAC,iIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,OAAO,SAAS,kBAAkB;wCAClC,UAAU,CAAC,IAAM,cAAc,sBAAsB,EAAE,MAAM,CAAC,KAAK;wCACnE,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;;;;;;;;;;;;sCAM/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,gBAAgB;oDAChC,UAAU,CAAC,IAAM,cAAc,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACjF,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,gBAAgB;oDAChC,UAAU,CAAC,IAAM,cAAc,oBAAoB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACjF,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,oBAAoB;oDACpC,UAAU,CAAC,IAAM,cAAc,wBAAwB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACrF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,gBAAgB;4CAChC,UAAU,CAAC,IAAM,cAAc,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CAC/E,WAAU;;;;;;sDAEZ,8OAAC;4CAAE,WAAU;sDAA8B;;;;;;;;;;;;8CAI7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAExC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,cAAc;oDAChC,UAAU,CAAC,IAAM,cAAc,kBAAkB,EAAE,MAAM,CAAC,OAAO;oDACjE,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO7B;IAEA,SAAS;QACP,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAyB;;;;;;;;;;;;8BAInD,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,mBAAmB;oDACnC,UAAU,CAAC,IAAM,cAAc,uBAAuB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACpF,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,kBAAkB;oDAClC,UAAU,CAAC,IAAM,cAAc,sBAAsB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACnF,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,uBAAuB;oDACvC,UAAU,CAAC,IAAM,cAAc,2BAA2B,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACxF,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAgD;;;;;;sDAGjE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,OAAO,SAAS,wBAAwB;4CACxC,UAAU,CAAC,IAAM,cAAc,4BAA4B,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CACvF,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,8OAAC;oCAAE,WAAU;;wCAAyB;wCACA,SAAS,kBAAkB;wCAAC;wCAAsB,CAAC,MAAM,CAAC,SAAS,uBAAuB,GAAG,GAAG,CAAC,EAAE,OAAO,CAAC;wCAAG;wCAAiB,CAAC,SAAS,kBAAkB,GAAI,MAAM,CAAC,SAAS,uBAAuB,GAAG,GAAG,CAAE,EAAE,OAAO,CAAC;;;;;;;8CAE9P,8OAAC;oCAAE,WAAU;;wCAA8B;wCACxB,CAAC,MAAM,CAAC,SAAS,kBAAkB,GAAI,MAAM,CAAC,SAAS,uBAAuB,GAAG,GAAG,CAAE,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;IAM9H;IAEA,SAAS;QACP,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAA0B;;;;;;;;;;;;8BAI/C,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAgD;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,qBAAqB;gDACrC,UAAU,CAAC,IAAM,cAAc,yBAAyB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gDACtF,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAExC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,eAAe;oDACjC,UAAU,CAAC,IAAM,cAAc,mBAAmB,EAAE,MAAM,CAAC,OAAO;oDAClE,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAInB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAExC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,mBAAmB;oDACrC,UAAU,CAAC,IAAM,cAAc,uBAAuB,EAAE,MAAM,CAAC,OAAO;oDACtE,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAInB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;sDAExC,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS,SAAS,WAAW;oDAC7B,UAAU,CAAC,IAAM,cAAc,eAAe,EAAE,MAAM,CAAC,OAAO;oDAC9D,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAO7B;AACF", "debugId": null}}]}