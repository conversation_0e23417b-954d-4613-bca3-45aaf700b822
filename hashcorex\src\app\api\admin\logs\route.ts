import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);
    
    if (!authenticated || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const action = searchParams.get('action') || '';
    const userId = searchParams.get('userId') || '';
    const dateFrom = searchParams.get('dateFrom') || '';
    const dateTo = searchParams.get('dateTo') || '';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { action: { contains: search, mode: 'insensitive' } },
        { details: { contains: search, mode: 'insensitive' } },
        { ipAddress: { contains: search, mode: 'insensitive' } },
        { userAgent: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (action && action !== 'all') {
      if (action === 'ERROR') {
        where.action = { contains: 'ERROR', mode: 'insensitive' };
      } else if (action === 'LOGIN') {
        where.action = { contains: 'LOGIN', mode: 'insensitive' };
      } else if (action === 'ADMIN') {
        where.action = { contains: 'ADMIN', mode: 'insensitive' };
      } else if (action === 'PAYMENT') {
        where.OR = [
          { action: { contains: 'PAYMENT', mode: 'insensitive' } },
          { action: { contains: 'WALLET', mode: 'insensitive' } },
          { action: { contains: 'DEPOSIT', mode: 'insensitive' } },
          { action: { contains: 'WITHDRAWAL', mode: 'insensitive' } },
        ];
      } else {
        where.action = { contains: action, mode: 'insensitive' };
      }
    }

    if (userId) {
      where.userId = userId;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        const endDate = new Date(dateTo);
        endDate.setHours(23, 59, 59, 999); // End of day
        where.createdAt.lte = endDate;
      }
    }

    // Get logs with pagination
    const [logs, totalCount] = await Promise.all([
      prisma.systemLog.findMany({
        where,
        include: {
          user: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.systemLog.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    // Transform logs to include user name
    const transformedLogs = logs.map(log => ({
      ...log,
      user: log.user ? {
        name: `${log.user.firstName} ${log.user.lastName}`.trim(),
        email: log.user.email,
      } : null,
    }));

    return NextResponse.json({
      success: true,
      logs: transformedLogs,
      total: totalCount,
      totalPages,
      currentPage: page,
    });

  } catch (error) {
    console.error('Admin logs fetch error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch logs' },
      { status: 500 }
    );
  }
}
