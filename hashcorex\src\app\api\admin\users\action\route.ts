import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { systemLogDb } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);
    
    if (!authenticated || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const body = await request.json();
    const { userId, action } = body;

    if (!userId || !action) {
      return NextResponse.json({ error: 'User ID and action are required' }, { status: 400 });
    }

    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true, email: true, firstName: true, lastName: true, role: true, isActive: true },
    });

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    let updatedUser;
    let logAction = '';
    let logDetails: any = {};

    switch (action) {
      case 'activate':
        updatedUser = await prisma.user.update({
          where: { id: userId },
          data: { isActive: true },
        });
        logAction = 'USER_ACTIVATED';
        logDetails = { targetUserId: userId, targetUserEmail: targetUser.email };
        break;

      case 'deactivate':
        updatedUser = await prisma.user.update({
          where: { id: userId },
          data: { isActive: false },
        });
        logAction = 'USER_DEACTIVATED';
        logDetails = { targetUserId: userId, targetUserEmail: targetUser.email };
        break;

      case 'promote':
        if (targetUser.role === 'ADMIN') {
          return NextResponse.json({ error: 'User is already an admin' }, { status: 400 });
        }
        updatedUser = await prisma.user.update({
          where: { id: userId },
          data: { role: 'ADMIN' },
        });
        logAction = 'USER_PROMOTED_TO_ADMIN';
        logDetails = { targetUserId: userId, targetUserEmail: targetUser.email };
        break;

      case 'demote':
        if (targetUser.role === 'USER') {
          return NextResponse.json({ error: 'User is already a regular user' }, { status: 400 });
        }
        updatedUser = await prisma.user.update({
          where: { id: userId },
          data: { role: 'USER' },
        });
        logAction = 'USER_DEMOTED_FROM_ADMIN';
        logDetails = { targetUserId: userId, targetUserEmail: targetUser.email };
        break;

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    // Log the admin action
    await systemLogDb.create({
      action: logAction,
      userId: user.id,
      details: logDetails,
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: `User ${action}d successfully`,
      data: updatedUser,
    });

  } catch (error) {
    console.error('Admin user action error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to perform user action' },
      { status: 500 }
    );
  }
}
