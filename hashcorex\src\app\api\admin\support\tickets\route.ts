import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { supportTicketDb } from '@/lib/database';

// GET - Fetch all support tickets (admin only)
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    const priority = url.searchParams.get('priority');
    const userId = url.searchParams.get('userId');

    // Get all tickets with optional filtering
    let tickets = await supportTicketDb.findAll();

    // Apply filters if provided
    if (status) {
      tickets = tickets.filter(ticket => ticket.status === status);
    }

    if (priority) {
      tickets = tickets.filter(ticket => ticket.priority === priority);
    }

    if (userId) {
      tickets = tickets.filter(ticket => ticket.userId === userId);
    }

    return NextResponse.json({
      success: true,
      data: tickets,
    });

  } catch (error: any) {
    console.error('Admin support tickets fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch support tickets' },
      { status: 500 }
    );
  }
}
