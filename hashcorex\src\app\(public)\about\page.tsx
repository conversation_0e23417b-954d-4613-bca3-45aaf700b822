'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui';
import { Container, Grid, Flex, PublicLayout } from '@/components/layout';
import { SolarPanel, MiningRig, Leaf, WindTurbine } from '@/components/icons';
import { ArrowLeft, Target, Users, Zap, Globe } from 'lucide-react';

export default function AboutPage() {
  return (
    <PublicLayout>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-solar-50 to-eco-50">
        <Container>
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-5xl lg:text-6xl font-bold text-dark-900 mb-6">
              About <span className="text-solar-500">HashCoreX</span>
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              We're pioneering the future of sustainable cryptocurrency mining 
              through innovative solar-powered data centers and cutting-edge technology.
            </p>
          </div>
        </Container>
      </section>

      {/* Mission Section */}
      <section className="py-20">
        <Container>
          <Grid cols={{ default: 1, lg: 2 }} gap={12} className="items-center">
            <div>
              <h2 className="text-4xl font-bold text-dark-900 mb-6">Our Mission</h2>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                HashCoreX was founded with a simple yet ambitious goal: to make 
                cryptocurrency mining sustainable, profitable, and accessible to everyone. 
                We believe that the future of digital currency should not come at the 
                expense of our planet.
              </p>
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                By harnessing the power of renewable energy sources, particularly solar 
                power, we're creating a mining ecosystem that generates real returns 
                while contributing to a cleaner, more sustainable future.
              </p>
              <Flex gap={4}>
                <div className="flex items-center space-x-2 text-eco-600">
                  <Leaf className="h-5 w-5" />
                  <span className="font-medium">100% Renewable Energy</span>
                </div>
                <div className="flex items-center space-x-2 text-solar-600">
                  <Zap className="h-5 w-5" />
                  <span className="font-medium">Real Mining Operations</span>
                </div>
              </Flex>
            </div>
            <div className="relative">
              <div className="bg-gradient-to-br from-solar-100 to-eco-100 rounded-2xl p-8 h-96 flex items-center justify-center">
                <div className="grid grid-cols-2 gap-8">
                  <SolarPanel className="h-24 w-24 text-solar-500" />
                  <MiningRig className="h-24 w-24 text-eco-500" />
                  <WindTurbine className="h-24 w-24 text-eco-500" />
                  <Leaf className="h-24 w-24 text-solar-500" />
                </div>
              </div>
            </div>
          </Grid>
        </Container>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gray-50">
        <Container>
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-dark-900 mb-4">Our Values</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              The principles that guide everything we do at HashCoreX.
            </p>
          </div>

          <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={8}>
            <div className="text-center p-6 bg-white rounded-xl shadow-sm">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-eco-100 rounded-full mb-4">
                <Leaf className="h-8 w-8 text-eco-600" />
              </div>
              <h3 className="text-xl font-semibold text-dark-900 mb-2">Sustainability</h3>
              <p className="text-gray-600">
                Environmental responsibility is at the core of our operations.
              </p>
            </div>

            <div className="text-center p-6 bg-white rounded-xl shadow-sm">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-solar-100 rounded-full mb-4">
                <Target className="h-8 w-8 text-solar-600" />
              </div>
              <h3 className="text-xl font-semibold text-dark-900 mb-2">Transparency</h3>
              <p className="text-gray-600">
                Open, honest communication and real-time performance data.
              </p>
            </div>

            <div className="text-center p-6 bg-white rounded-xl shadow-sm">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-eco-100 rounded-full mb-4">
                <Users className="h-8 w-8 text-eco-600" />
              </div>
              <h3 className="text-xl font-semibold text-dark-900 mb-2">Community</h3>
              <p className="text-gray-600">
                Building a global community of sustainable mining advocates.
              </p>
            </div>

            <div className="text-center p-6 bg-white rounded-xl shadow-sm">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-solar-100 rounded-full mb-4">
                <Globe className="h-8 w-8 text-solar-600" />
              </div>
              <h3 className="text-xl font-semibold text-dark-900 mb-2">Innovation</h3>
              <p className="text-gray-600">
                Continuously advancing renewable energy mining technology.
              </p>
            </div>
          </Grid>
        </Container>
      </section>

      {/* Roadmap Section */}
      <section className="py-20">
        <Container>
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-dark-900 mb-4">Our Roadmap</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our journey towards a sustainable mining future.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-eco-500 rounded-full flex items-center justify-center text-white font-bold">
                  ✓
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-dark-900 mb-2">Q1 2024 - Platform Launch</h3>
                  <p className="text-gray-600">
                    Launched HashCoreX platform with initial solar-powered mining facility 
                    and user onboarding system.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-eco-500 rounded-full flex items-center justify-center text-white font-bold">
                  ✓
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-dark-900 mb-2">Q2 2024 - Expansion</h3>
                  <p className="text-gray-600">
                    Expanded mining capacity to 1,000 TH/s and introduced binary 
                    referral system for community growth.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-solar-500 rounded-full flex items-center justify-center text-white font-bold">
                  ⏳
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-dark-900 mb-2">Q3 2024 - Mobile App</h3>
                  <p className="text-gray-600">
                    Launch mobile applications for iOS and Android with full 
                    platform functionality and push notifications.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 font-bold">
                  ⏳
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-dark-900 mb-2">Q4 2024 - Global Expansion</h3>
                  <p className="text-gray-600">
                    Open additional solar-powered mining facilities in multiple 
                    countries and reach 10,000+ active users.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-solar-500 to-eco-500 text-white">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Join Our Mission
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Be part of the sustainable mining revolution. Start earning 
              while supporting renewable energy.
            </p>
            <Link href="/register">
              <Button 
                size="xl" 
                variant="secondary"
                className="bg-white text-dark-900 hover:bg-gray-100"
              >
                Get Started Today
              </Button>
            </Link>
          </div>
        </Container>
      </section>
    </PublicLayout>
  );
}
