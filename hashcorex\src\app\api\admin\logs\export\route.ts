import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);
    
    if (!authenticated || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const action = searchParams.get('action') || '';
    const userId = searchParams.get('userId') || '';
    const dateFrom = searchParams.get('dateFrom') || '';
    const dateTo = searchParams.get('dateTo') || '';

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { action: { contains: search, mode: 'insensitive' } },
        { details: { contains: search, mode: 'insensitive' } },
        { ipAddress: { contains: search, mode: 'insensitive' } },
        { userAgent: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (action && action !== 'all') {
      if (action === 'ERROR') {
        where.action = { contains: 'ERROR', mode: 'insensitive' };
      } else if (action === 'LOGIN') {
        where.action = { contains: 'LOGIN', mode: 'insensitive' };
      } else if (action === 'ADMIN') {
        where.action = { contains: 'ADMIN', mode: 'insensitive' };
      } else if (action === 'PAYMENT') {
        where.OR = [
          { action: { contains: 'PAYMENT', mode: 'insensitive' } },
          { action: { contains: 'WALLET', mode: 'insensitive' } },
          { action: { contains: 'DEPOSIT', mode: 'insensitive' } },
          { action: { contains: 'WITHDRAWAL', mode: 'insensitive' } },
        ];
      } else {
        where.action = { contains: action, mode: 'insensitive' };
      }
    }

    if (userId) {
      where.userId = userId;
    }

    // Date range filter
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        const endDate = new Date(dateTo);
        endDate.setHours(23, 59, 59, 999); // End of day
        where.createdAt.lte = endDate;
      }
    }

    // Get all logs (no pagination for export)
    const logs = await prisma.systemLog.findMany({
      where,
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Convert to CSV
    const csvHeaders = [
      'Date',
      'Action',
      'User',
      'Email',
      'Details',
      'IP Address',
      'User Agent',
    ];

    const csvRows = logs.map(log => [
      log.createdAt.toISOString(),
      log.action,
      log.user ? `${log.user.firstName} ${log.user.lastName}` : 'System',
      log.user?.email || 'N/A',
      log.details || '',
      log.ipAddress || '',
      log.userAgent || '',
    ]);

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => 
        row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(',')
      ),
    ].join('\n');

    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="system-logs-${new Date().toISOString().split('T')[0]}.csv"`,
      },
    });

  } catch (error) {
    console.error('Admin logs export error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to export logs' },
      { status: 500 }
    );
  }
}
