import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest, isAdmin } from '@/lib/auth';
import { adminSettingsDb, systemLogDb } from '@/lib/database';
import { isValidTronAddress } from '@/lib/trongrid';

// GET - Fetch deposit settings
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Fetch deposit-related settings
    const depositAddress = await adminSettingsDb.get('USDT_DEPOSIT_ADDRESS');
    const minDepositAmount = await adminSettingsDb.get('MIN_DEPOSIT_AMOUNT');
    const maxDepositAmount = await adminSettingsDb.get('MAX_DEPOSIT_AMOUNT');
    const depositEnabled = await adminSettingsDb.get('DEPOSIT_ENABLED');
    const minConfirmations = await adminSettingsDb.get('MIN_CONFIRMATIONS');
    const depositFeePercentage = await adminSettingsDb.get('DEPOSIT_FEE_PERCENTAGE');

    return NextResponse.json({
      success: true,
      data: {
        depositAddress: depositAddress || '',
        minDepositAmount: parseFloat(minDepositAmount || '10'),
        maxDepositAmount: parseFloat(maxDepositAmount || '10000'),
        depositEnabled: depositEnabled === 'true',
        minConfirmations: parseInt(minConfirmations || '1'),
        depositFeePercentage: parseFloat(depositFeePercentage || '0'),
      },
    });

  } catch (error) {
    console.error('Deposit settings fetch error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch deposit settings' },
      { status: 500 }
    );
  }
}

// POST - Update deposit settings
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      depositAddress,
      minDepositAmount,
      maxDepositAmount,
      depositEnabled,
      minConfirmations,
      depositFeePercentage,
    } = body;

    // Validation
    const errors: string[] = [];

    if (depositAddress && !isValidTronAddress(depositAddress)) {
      errors.push('Invalid USDT TRC20 address format');
    }

    if (minDepositAmount !== undefined && (minDepositAmount < 0 || minDepositAmount > 1000000)) {
      errors.push('Minimum deposit amount must be between 0 and 1,000,000');
    }

    if (maxDepositAmount !== undefined && (maxDepositAmount < 0 || maxDepositAmount > 1000000)) {
      errors.push('Maximum deposit amount must be between 0 and 1,000,000');
    }

    if (minDepositAmount !== undefined && maxDepositAmount !== undefined && minDepositAmount > maxDepositAmount) {
      errors.push('Minimum deposit amount cannot be greater than maximum deposit amount');
    }

    if (minConfirmations !== undefined && (minConfirmations < 0 || minConfirmations > 100)) {
      errors.push('Minimum confirmations must be between 0 and 100');
    }

    if (depositFeePercentage !== undefined && (depositFeePercentage < 0 || depositFeePercentage > 50)) {
      errors.push('Deposit fee percentage must be between 0 and 50');
    }

    if (errors.length > 0) {
      return NextResponse.json(
        { success: false, error: errors.join(', ') },
        { status: 400 }
      );
    }

    // Update settings
    const updates: Array<{ key: string; value: string }> = [];

    if (depositAddress !== undefined) {
      await adminSettingsDb.set('USDT_DEPOSIT_ADDRESS', depositAddress);
      updates.push({ key: 'USDT_DEPOSIT_ADDRESS', value: depositAddress });
    }

    if (minDepositAmount !== undefined) {
      await adminSettingsDb.set('MIN_DEPOSIT_AMOUNT', minDepositAmount.toString());
      updates.push({ key: 'MIN_DEPOSIT_AMOUNT', value: minDepositAmount.toString() });
    }

    if (maxDepositAmount !== undefined) {
      await adminSettingsDb.set('MAX_DEPOSIT_AMOUNT', maxDepositAmount.toString());
      updates.push({ key: 'MAX_DEPOSIT_AMOUNT', value: maxDepositAmount.toString() });
    }

    if (depositEnabled !== undefined) {
      await adminSettingsDb.set('DEPOSIT_ENABLED', depositEnabled.toString());
      updates.push({ key: 'DEPOSIT_ENABLED', value: depositEnabled.toString() });
    }

    if (minConfirmations !== undefined) {
      await adminSettingsDb.set('MIN_CONFIRMATIONS', minConfirmations.toString());
      updates.push({ key: 'MIN_CONFIRMATIONS', value: minConfirmations.toString() });
    }

    if (depositFeePercentage !== undefined) {
      await adminSettingsDb.set('DEPOSIT_FEE_PERCENTAGE', depositFeePercentage.toString());
      updates.push({ key: 'DEPOSIT_FEE_PERCENTAGE', value: depositFeePercentage.toString() });
    }

    // Log the settings update
    await systemLogDb.create({
      action: 'DEPOSIT_SETTINGS_UPDATED',
      adminId: user.id,
      details: { updates },
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: 'Deposit settings updated successfully',
    });

  } catch (error) {
    console.error('Deposit settings update error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update deposit settings' },
      { status: 500 }
    );
  }
}

// PUT - Reset deposit settings to defaults
export async function PUT(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const userIsAdmin = await isAdmin(user.id);
    if (!userIsAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Reset to default values
    const defaultSettings = [
      { key: 'USDT_DEPOSIT_ADDRESS', value: '' },
      { key: 'MIN_DEPOSIT_AMOUNT', value: '10' },
      { key: 'MAX_DEPOSIT_AMOUNT', value: '10000' },
      { key: 'DEPOSIT_ENABLED', value: 'true' },
      { key: 'MIN_CONFIRMATIONS', value: '1' },
      { key: 'DEPOSIT_FEE_PERCENTAGE', value: '0' },
    ];

    for (const setting of defaultSettings) {
      await adminSettingsDb.set(setting.key, setting.value);
    }

    // Log the reset action
    await systemLogDb.create({
      action: 'DEPOSIT_SETTINGS_RESET',
      adminId: user.id,
      details: { defaultSettings },
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: 'Deposit settings reset to defaults',
    });

  } catch (error) {
    console.error('Deposit settings reset error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to reset deposit settings' },
      { status: 500 }
    );
  }
}
