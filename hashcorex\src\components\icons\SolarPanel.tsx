'use client';

import React from 'react';

interface IconProps {
  className?: string;
  size?: number;
}

export const SolarPanel: React.FC<IconProps> = ({ className, size = 24 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect x="2" y="4" width="20" height="16" rx="2" stroke="currentColor" strokeWidth="2" fill="none"/>
      <line x1="2" y1="8" x2="22" y2="8" stroke="currentColor" strokeWidth="2"/>
      <line x1="2" y1="12" x2="22" y2="12" stroke="currentColor" strokeWidth="2"/>
      <line x1="2" y1="16" x2="22" y2="16" stroke="currentColor" strokeWidth="2"/>
      <line x1="6" y1="4" x2="6" y2="20" stroke="currentColor" strokeWidth="2"/>
      <line x1="10" y1="4" x2="10" y2="20" stroke="currentColor" strokeWidth="2"/>
      <line x1="14" y1="4" x2="14" y2="20" stroke="currentColor" strokeWidth="2"/>
      <line x1="18" y1="4" x2="18" y2="20" stroke="currentColor" strokeWidth="2"/>
      <circle cx="20" cy="2" r="1" fill="currentColor"/>
      <path d="M19 1l1 1-1 1-1-1z" fill="currentColor"/>
    </svg>
  );
};
