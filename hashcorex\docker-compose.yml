version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: hashcorex-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: hashcorex
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - hashcorex-network

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: hashcorex-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - hashcorex-network

  # HashCoreX Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hashcorex-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - DATABASE_URL=*********************************************************/hashcorex
      - DIRECT_URL=*********************************************************/hashcorex
      - REDIS_URL=redis://redis:6379
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis
    networks:
      - hashcorex-network
    volumes:
      - ./uploads:/app/public/uploads

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: hashcorex-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - hashcorex-network

volumes:
  postgres_data:
  redis_data:

networks:
  hashcorex-network:
    driver: bridge
