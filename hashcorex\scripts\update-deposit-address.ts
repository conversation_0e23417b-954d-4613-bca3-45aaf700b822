import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function updateDepositAddress() {
  console.log('🔧 Updating Deposit Address...\n');

  try {
    // Update the deposit address to match the test transaction
    const depositAddress = 'TBtMPmTkz4f8xDn2E6mP1wvGtWnzzRwQeK';
    
    await prisma.adminSettings.upsert({
      where: { key: 'depositAddress' },
      update: { value: depositAddress },
      create: { key: 'depositAddress', value: depositAddress },
    });

    console.log(`✅ Deposit address updated to: ${depositAddress}`);
    console.log('   This matches the recipient address in the test transaction.');
    console.log('   Users can now successfully verify deposits to this address.\n');

    // Also verify the network settings
    const networkSettings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: ['tronNetwork', 'tronApiUrl', 'tronUsdtContract']
        }
      }
    });

    console.log('📋 Current Network Settings:');
    networkSettings.forEach(setting => {
      console.log(`   ${setting.key}: ${setting.value}`);
    });

    if (networkSettings.length === 0) {
      console.log('⚠️  Network settings not found. Run init-tron-network-settings.ts first.');
    }

  } catch (error) {
    console.error('❌ Error updating deposit address:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateDepositAddress();
