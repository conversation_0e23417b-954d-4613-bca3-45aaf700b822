import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testDepositAPI() {
  console.log('🧪 Testing Deposit API Endpoint...\n');

  // First, let's create a test user and deposit address
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Test User',
      password: 'hashedpassword',
      isEmailVerified: true,
    },
  });

  // Create or update deposit address
  await prisma.adminSettings.upsert({
    where: { key: 'depositAddress' },
    update: { value: 'T9yKCjp6EMhTtnNvq6osdhjEjQKGS7yMtT' },
    create: { key: 'depositAddress', value: 'T9yKCjp6EMhTtnNvq6osdhjEjQKGS7yMtT' },
  });

  console.log(`Created test user: ${testUser.email} (ID: ${testUser.id})`);

  // Test the API endpoint
  const testTxId = 'afd3b8edf843dc4392476846b6029b714ef4f4eb970b3cacac654bce8b2b4f17';
  
  try {
    console.log('🔍 Testing deposit verification API...');
    
    const response = await fetch('http://localhost:3000/api/wallet/deposit/verify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer test-token-for-user-${testUser.id}`, // Mock token
      },
      body: JSON.stringify({
        transactionId: testTxId,
      }),
    });

    const result = await response.json();

    console.log('\n📊 API Response:');
    console.log(`  Status: ${response.status}`);
    console.log(`  Success: ${result.success}`);
    console.log(`  Message: ${result.message}`);
    
    if (result.data) {
      console.log(`  Amount: ${result.data.amount} USDT`);
      console.log(`  Status: ${result.data.status}`);
      console.log(`  Confirmations: ${result.data.confirmations}/${result.data.minConfirmations}`);
      console.log(`  Block Number: ${result.data.blockNumber}`);
    }

    if (response.ok && result.success) {
      console.log('\n✅ API test PASSED!');
      console.log('   The deposit verification API is working correctly.');
    } else {
      console.log('\n❌ API test FAILED!');
      console.log(`   Error: ${result.message || 'Unknown error'}`);
    }

  } catch (error) {
    console.error('\n❌ Error testing API:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testDepositAPI();
