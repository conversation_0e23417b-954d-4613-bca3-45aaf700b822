import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createAdmin() {
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
  
  console.log('Creating admin user...');
  console.log('Email:', adminEmail);
  
  // Check if admin already exists
  const existingAdmin = await prisma.user.findUnique({
    where: { email: adminEmail },
  });
  
  if (existingAdmin) {
    console.log('Admin user already exists');
    
    // Update to admin role if not already
    if (existingAdmin.role !== 'ADMIN') {
      await prisma.user.update({
        where: { id: existingAdmin.id },
        data: { role: 'ADMIN' },
      });
      console.log('Updated existing user to admin role');
    }
    
    return;
  }
  
  // Hash password
  const hashedPassword = await bcrypt.hash(adminPassword, 12);
  
  // Generate unique referral ID
  let referralId: string;
  let isUnique = false;
  do {
    referralId = Math.random().toString(36).substring(2, 8).toUpperCase();
    const existing = await prisma.user.findUnique({
      where: { referralId },
    });
    isUnique = !existing;
  } while (!isUnique);
  
  // Create admin user
  const admin = await prisma.user.create({
    data: {
      email: adminEmail,
      firstName: 'Admin',
      lastName: 'User',
      password: hashedPassword,
      referralId,
      role: 'ADMIN',
      kycStatus: 'APPROVED',
      isActive: true,
    },
  });
  
  console.log('Admin user created successfully!');
  console.log('Email:', admin.email);
  console.log('Referral ID:', admin.referralId);
  console.log('Role:', admin.role);
  console.log('');
  console.log('You can now login to the admin panel at: http://localhost:3000/admin');
}

createAdmin()
  .catch((error) => {
    console.error('Error creating admin user:', error);
    process.exit(1);
  })
  .finally(() => prisma.$disconnect());
